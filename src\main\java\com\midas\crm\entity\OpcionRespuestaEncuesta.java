package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * Entidad que representa una opción de respuesta para una pregunta de encuesta
 */
@Entity
@Table(name = "opciones_respuesta_encuesta")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpcionRespuestaEncuesta {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String texto;

    @Column(nullable = false)
    private Integer orden; // Orden de la opción en la pregunta

    @Column(name = "valor")
    private Integer valor; // Valor numérico asociado (útil para escalas Likert)

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pregunta_id", nullable = false)
    private PreguntaEncuesta pregunta;

    @Column(nullable = false, length = 1)
    private String estado = "A"; // A: Activo, I: Inactivo

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;
}
