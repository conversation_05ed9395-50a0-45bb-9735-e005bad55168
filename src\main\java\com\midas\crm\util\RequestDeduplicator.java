package com.midas.crm.util;

import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Utilidad para deduplicar solicitudes
 * Evita procesar solicitudes duplicadas en un corto período de tiempo
 */
@Slf4j
public class RequestDeduplicator {

    // Mapa para almacenar las solicitudes procesadas recientemente
    private final Map<String, Instant> processedRequests = new ConcurrentHashMap<>();

    // Tiempo de expiración para las solicitudes
    private final long expirationTimeMillis;

    /**
     * Constructor
     * @param expirationTime Tiempo de expiración
     * @param timeUnit Unidad de tiempo
     */
    public RequestDeduplicator(long expirationTime, TimeUnit timeUnit) {
        this.expirationTimeMillis = timeUnit.toMillis(expirationTime);
    }

    /**
     * Verifica si una solicitud es duplicada
     * @param userId ID del usuario
     * @param requestType Tipo de solicitud
     * @return true si la solicitud es duplicada, false en caso contrario
     */
    public boolean isDuplicate(String userId, String requestType) {
        String key = userId + ":" + requestType;
        Instant now = Instant.now();
        Instant lastRequest = processedRequests.put(key, now);

        if (lastRequest == null) {
            // Primera solicitud, no es duplicada
            return false;
        }

        // Es duplicada si la última solicitud fue hace menos del tiempo de expiración
        long elapsedMillis = now.toEpochMilli() - lastRequest.toEpochMilli();
        boolean isDuplicate = elapsedMillis < expirationTimeMillis;

        if (isDuplicate) {
            log.debug("Solicitud duplicada detectada: {} ({}ms)", key, elapsedMillis);
        }

        return isDuplicate;
    }

    /**
     * Limpia las solicitudes expiradas
     */
    public void cleanup() {
        Instant now = Instant.now();
        int initialSize = processedRequests.size();

        processedRequests.entrySet().removeIf(entry ->
                now.toEpochMilli() - entry.getValue().toEpochMilli() > expirationTimeMillis);

        int removedCount = initialSize - processedRequests.size();
        if (removedCount > 0) {
            log.info("Limpieza de caché de deduplicación: {} entradas eliminadas", removedCount);
        }
    }
}