package com.midas.crm.entity.DTO.encuesta;

import com.midas.crm.entity.DTO.user.UserDTO;
import com.midas.crm.entity.Encuesta;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO para transferir información de encuestas
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EncuestaDTO {
    private Long id;
    private String titulo;
    private String descripcion;
    private LocalDateTime fechaInicio;
    private LocalDateTime fechaFin;
    private Integer tiempoLimite;
    private Boolean esAnonima;
    private Boolean mostrarResultados;
    private Encuesta.TipoAsignacion tipoAsignacion;

    // Información de sede (si aplica)
    private Long sedeId;
    private String sedeNombre;

    // Información de coordinador (si aplica)
    private Long coordinadorId;
    private String coordinadorNombre;

    // Información de usuario específico (si aplica)
    private Long usuarioId;
    private String usuarioNombre;

    // Información de usuarios múltiples (si aplica)
    private String usuarioIds; // IDs separados por comas
    private List<String> usuarioNombres; // Nombres de usuarios múltiples

    // Información del creador
    private UserDTO creador;

    // Lista de preguntas
    private List<PreguntaEncuestaDTO> preguntas;

    // Estadísticas
    private Long totalRespuestas;
    private Long respuestasCompletadas;

    private String estado;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
}
