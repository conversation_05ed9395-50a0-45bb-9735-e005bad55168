package com.midas.crm.controller;

import com.midas.crm.entity.DTO.curso.CursoListDTO;
import com.midas.crm.entity.DTO.curso.ModuloMinimalDTO;
import com.midas.crm.service.CursoOptimizedService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Controlador optimizado para cursos que reduce la cantidad de datos enviados al frontend
 */
@RestController
@RequestMapping("${api.route.curso}/optimized")
public class CursoOptimizedController {

    @Autowired
    private CursoOptimizedService cursoOptimizedService;

    /**
     * Obtiene cursos optimizados por lista de IDs
     * Solo devuelve los campos necesarios para la vista de lista
     */
    @PostMapping("/byIds")
    public ResponseEntity<GenericResponse<List<CursoListDTO>>> getCursosOptimizedByIds(@RequestBody Map<String, List<Long>> request) {
        List<Long> ids = request.get("ids");
        if (ids == null || ids.isEmpty()) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "No se encontraron cursos", new ArrayList<>())
            );
        }

        try {
            List<CursoListDTO> cursosDTO = cursoOptimizedService.getCursosOptimizedByIds(ids);
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cursos encontrados", cursosDTO)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener cursos: " + e.getMessage(), new ArrayList<>())
            );
        }
    }

    /**
     * Obtiene módulos optimizados por curso ID
     * Solo devuelve los campos necesarios para la vista de módulos
     */
    @GetMapping("/{cursoId}/modulos")
    public ResponseEntity<GenericResponse<List<ModuloMinimalDTO>>> getModulosOptimizedByCursoId(@PathVariable Long cursoId) {
        try {
            List<ModuloMinimalDTO> modulosDTO = cursoOptimizedService.getModulosOptimizedByCursoId(cursoId);
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Módulos encontrados", modulosDTO)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener módulos: " + e.getMessage(), new ArrayList<>())
            );
        }
    }

    /**
     * Obtiene un curso optimizado por ID
     * Solo devuelve los campos necesarios para la vista de detalle
     */
    @GetMapping("/{cursoId}")
    public ResponseEntity<GenericResponse<CursoListDTO>> getCursoOptimizedById(@PathVariable Long cursoId) {
        try {
            CursoListDTO cursoDTO = cursoOptimizedService.getCursoOptimizedById(cursoId);
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Curso encontrado", cursoDTO)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener curso: " + e.getMessage(), null)
            );
        }
    }
}
