package com.midas.crm.repository;

import com.midas.crm.entity.Notification;
import com.midas.crm.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repositorio para gestionar las operaciones de base de datos con la entidad
 * Notification
 */
@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {

    /**
     * Busca notificaciones para un usuario específico
     *
     * @param recipient Usuario destinatario
     * @param pageable  Configuración de paginación
     * @return Página de notificaciones
     */
    Page<Notification> findByRecipientOrderByCreatedAtDesc(User recipient, Pageable pageable);

    /**
     * Busca notificaciones para un ID de usuario específico
     *
     * @param recipientId ID del usuario destinatario
     * @param pageable    Configuración de paginación
     * @return Página de notificaciones
     */
    Page<Notification> findByRecipientIdOrderByCreatedAtDesc(Long recipientId, Pageable pageable);

    /**
     * Busca notificaciones de tipo broadcast (para todos los usuarios)
     *
     * @param pageable Configuración de paginación
     * @return Página de notificaciones
     */
    @Query("SELECT n FROM Notification n WHERE n.type = 'BROADCAST' ORDER BY n.createdAt DESC")
    Page<Notification> findBroadcastNotifications(Pageable pageable);

    /**
     * Busca notificaciones para un usuario específico o de tipo broadcast
     *
     * @param recipientId ID del usuario destinatario
     * @param pageable    Configuración de paginación
     * @return Página de notificaciones
     */
    @Query("SELECT n FROM Notification n WHERE n.recipientId = :recipientId OR n.type = 'BROADCAST' ORDER BY n.createdAt DESC")
    Page<Notification> findByRecipientIdOrBroadcast(@Param("recipientId") Long recipientId, Pageable pageable);

    /**
     * Busca notificaciones basadas en rol
     *
     * @param role     Rol de usuario
     * @param pageable Configuración de paginación
     * @return Página de notificaciones
     */
    @Query("SELECT n FROM Notification n WHERE n.type = 'ROLE_BASED' AND n.data LIKE %:role% ORDER BY n.createdAt DESC")
    Page<Notification> findByRoleBasedNotifications(@Param("role") String role, Pageable pageable);

    /**
     * Cuenta las notificaciones no leídas para un usuario específico
     *
     * @param recipientId ID del usuario destinatario
     * @return Número de notificaciones no leídas
     */
    @Query("SELECT COUNT(n) FROM Notification n WHERE " +
            "((n.recipientId = :recipientId AND n.read = false) OR " +
            "(n.type = 'BROADCAST' AND n.read = false AND " +
            "NOT EXISTS (SELECT nr FROM NotificationRead nr WHERE nr.notificationId = n.id AND nr.userId = :recipientId)))")
    long countUnreadNotifications(@Param("recipientId") Long recipientId);

    /**
     * Busca las últimas notificaciones para un usuario específico
     *
     * @param recipientId ID del usuario destinatario
     * @param limit       Número máximo de notificaciones a devolver
     * @return Lista de notificaciones
     */
    @Query("SELECT n, CASE " +
            "WHEN n.recipientId = :recipientId THEN " +
            "   CASE WHEN TRIM(COALESCE(CAST(n.read AS string), 'false')) = 'true' THEN true ELSE false END "
            +
            "WHEN EXISTS (SELECT nr FROM NotificationRead nr WHERE nr.notificationId = n.id AND nr.userId = :recipientId) THEN true "
            +
            "ELSE false END as isRead " +
            "FROM Notification n " +
            "WHERE n.recipientId = :recipientId OR n.type = 'BROADCAST' " +
            "ORDER BY n.createdAt DESC")
    List<Object[]> findLatestNotificationsWithReadStatus(@Param("recipientId") Long recipientId, Pageable pageable);

    /**
     * Busca las últimas notificaciones para un usuario específico (método legacy)
     *
     * @param recipientId ID del usuario destinatario
     * @param limit       Número máximo de notificaciones a devolver
     * @return Lista de notificaciones
     * @deprecated Use findLatestNotificationsWithReadStatus instead
     */
    @Query("SELECT n FROM Notification n WHERE n.recipientId = :recipientId OR n.type = 'BROADCAST' ORDER BY n.createdAt DESC")
    List<Notification> findLatestNotifications(@Param("recipientId") Long recipientId, Pageable pageable);

    /**
     * Busca notificaciones para un usuario incluyendo notificaciones grupales por
     * sede y rol
     *
     * @param recipientId ID del usuario destinatario
     * @param pageable    Configuración de paginación
     * @return Página de notificaciones
     */
    @Query("SELECT n FROM Notification n WHERE " +
            "n.recipientId = :recipientId OR " +
            "n.type = 'BROADCAST' " +
            "ORDER BY n.createdAt DESC")
    Page<Notification> findNotificationsForUserIncludingGroups(@Param("recipientId") Long recipientId,
                                                               Pageable pageable);

    /**
     * Busca las últimas notificaciones para un usuario incluyendo notificaciones
     * grupales
     *
     * @param recipientId ID del usuario destinatario
     * @param pageable    Configuración de paginación
     * @return Lista de notificaciones
     */
    @Query("SELECT n FROM Notification n WHERE " +
            "n.recipientId = :recipientId OR " +
            "n.type = 'BROADCAST' " +
            "ORDER BY n.createdAt DESC")
    List<Notification> findLatestNotificationsIncludingGroups(@Param("recipientId") Long recipientId,
                                                              Pageable pageable);

    /**
     * Cuenta las notificaciones no leídas incluyendo notificaciones grupales
     *
     * @param recipientId ID del usuario destinatario
     * @return Número de notificaciones no leídas
     */
    @Query("SELECT COUNT(n) FROM Notification n WHERE " +
            "((n.recipientId = :recipientId AND n.read = false) OR " +
            "(n.type = 'BROADCAST' AND n.read = false AND " +
            "NOT EXISTS (SELECT nr FROM NotificationRead nr WHERE nr.notificationId = n.id AND nr.userId = :recipientId)))")
    long countUnreadNotificationsIncludingGroups(@Param("recipientId") Long recipientId);
}