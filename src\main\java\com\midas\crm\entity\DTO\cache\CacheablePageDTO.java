package com.midas.crm.entity.DTO.cache;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.io.Serializable;
import java.util.List;

/**
 * DTO para cachear objetos Page en Redis de forma serializable
 * Evita problemas de serialización con objetos Page complejos
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CacheablePageDTO<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private List<T> content;
    private int pageNumber;
    private int pageSize;
    private long totalElements;
    private int totalPages;
    private boolean first;
    private boolean last;
    private boolean hasNext;
    private boolean hasPrevious;
    
    /**
     * Constructor desde un objeto Page
     */
    public CacheablePageDTO(Page<T> page) {
        this.content = page.getContent();
        this.pageNumber = page.getNumber();
        this.pageSize = page.getSize();
        this.totalElements = page.getTotalElements();
        this.totalPages = page.getTotalPages();
        this.first = page.isFirst();
        this.last = page.isLast();
        this.hasNext = page.hasNext();
        this.hasPrevious = page.hasPrevious();
    }
    
    /**
     * Convierte de vuelta a un objeto Page
     */
    public Page<T> toPage() {
        Pageable pageable = PageRequest.of(pageNumber, pageSize);
        return new PageImpl<>(content, pageable, totalElements);
    }
    
    /**
     * Método estático para crear desde Page
     */
    public static <T> CacheablePageDTO<T> from(Page<T> page) {
        return new CacheablePageDTO<>(page);
    }
    
    /**
     * Método estático para convertir a Page
     */
    public static <T> Page<T> toPage(CacheablePageDTO<T> cacheablePageDTO) {
        if (cacheablePageDTO == null) {
            return Page.empty();
        }
        return cacheablePageDTO.toPage();
    }
}
