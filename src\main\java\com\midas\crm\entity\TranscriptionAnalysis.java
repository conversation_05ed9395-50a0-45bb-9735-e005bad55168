package com.midas.crm.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entidad para almacenar el análisis completo de comparación de transcripción
 * Guarda todo el response detallado del API comparador para análisis futuro
 */
@Entity
@Table(name = "transcription_analysis")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class TranscriptionAnalysis {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * ID del cliente residencial (campo directo para evitar problemas de lazy loading)
     */
    @Column(name = "cliente_residencial_id", nullable = false, insertable = false, updatable = false)
    private Long clienteResidencialId;

    /**
     * Relación con el lead analizado
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cliente_residencial_id", referencedColumnName = "id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private ClienteResidencial clienteResidencial;

    /**
     * Response completo del API comparador en formato JSON
     */
    @Column(name = "response_completo", columnDefinition = "LONGTEXT", nullable = false)
    private String responseCompleto;

    /**
     * Campos analizados en formato JSON
     */
    @Column(name = "campos_analizados", columnDefinition = "LONGTEXT", nullable = true)
    private String camposAnalizados;

    /**
     * Información de debug en formato JSON
     */
    @Column(name = "debug_info", columnDefinition = "LONGTEXT", nullable = true)
    private String debugInfo;

    /**
     * Resumen del análisis en formato JSON
     */
    @Column(name = "resumen", columnDefinition = "TEXT", nullable = true)
    private String resumen;

    /**
     * Estadísticas del análisis en formato JSON
     */
    @Column(name = "estadisticas", columnDefinition = "TEXT", nullable = true)
    private String estadisticas;

    /**
     * Recomendaciones en formato JSON
     */
    @Column(name = "recomendaciones", columnDefinition = "TEXT", nullable = true)
    private String recomendaciones;

    /**
     * Porcentaje promedio extraído del análisis
     */
    @Column(name = "porcentaje_promedio", precision = 5, scale = 2, nullable = true)
    private BigDecimal porcentajePromedio;

    /**
     * Porcentaje ponderado extraído del análisis
     */
    @Column(name = "porcentaje_ponderado", precision = 5, scale = 2, nullable = true)
    private BigDecimal porcentajePonderado;

    /**
     * Nivel de confianza del análisis
     */
    @Column(name = "nivel_confianza", length = 50, nullable = true)
    private String nivelConfianza;

    /**
     * Total de campos analizados
     */
    @Column(name = "total_campos", nullable = true)
    private Integer totalCampos;

    /**
     * Campos exactos encontrados
     */
    @Column(name = "campos_exactos", nullable = true)
    private Integer camposExactos;

    /**
     * Campos buenos encontrados
     */
    @Column(name = "campos_buenos", nullable = true)
    private Integer camposBuenos;

    /**
     * Campos regulares encontrados
     */
    @Column(name = "campos_regulares", nullable = true)
    private Integer camposRegulares;

    /**
     * Campos malos encontrados
     */
    @Column(name = "campos_malos", nullable = true)
    private Integer camposMalos;

    /**
     * Fecha de creación del análisis
     */
    @Column(name = "fecha_creacion", nullable = false)
    @Builder.Default
    private LocalDateTime fechaCreacion = LocalDateTime.now();

    /**
     * Fecha de última actualización
     */
    @Column(name = "fecha_actualizacion", nullable = true)
    private LocalDateTime fechaActualizacion;

    /**
     * Estado del análisis (PENDING, COMPLETED, ERROR)
     */
    @Column(name = "estado", length = 20, nullable = false)
    @Builder.Default
    private String estado = "COMPLETED";

    /**
     * Observaciones adicionales
     */
    @Column(name = "observaciones", length = 1000, nullable = true)
    private String observaciones;

    /**
     * Versión del API comparador utilizado
     */
    @Column(name = "version_api", length = 20, nullable = true)
    private String versionApi;

    /**
     * Tiempo de procesamiento en milisegundos
     */
    @Column(name = "tiempo_procesamiento", nullable = true)
    private Long tiempoProcesamiento;

    @PreUpdate
    protected void onUpdate() {
        fechaActualizacion = LocalDateTime.now();
    }
}
