package com.midas.crm.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "faqs")
@Data
public class Faq {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String pregunta;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String respuesta;

    private String categoria;

    private Integer views = 0;

    // Usuario que crea la pregunta
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_pregunta_id", referencedColumnName = "codi_usuario")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "asesores", "coordinador"})
    private User usuarioPregunta;

    // Usuario que responde la pregunta
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_respuesta_id", referencedColumnName = "codi_usuario")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "asesores", "coordinador"})
    private User usuarioRespuesta;

    // Indica si la pregunta es pública o privada
    @Column(name = "es_publica", nullable = false)
    private Boolean esPublica = true;

    // Indica si la pregunta ha sido respondida
    @Column(name = "respondida", nullable = false)
    private Boolean respondida = false;

    // Estado de la pregunta (ABIERTA, CERRADA)
    @Column(name = "estado", nullable = false)
    @Enumerated(EnumType.STRING)
    private EstadoFaq estado = EstadoFaq.ABIERTA;

    // Archivos adjuntos a la pregunta
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "faq_id")
    private List<FileFaq> archivos = new ArrayList<>();

    // Respuestas a esta pregunta
    @OneToMany(mappedBy = "faq", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "faq"})
    private List<FaqRespuesta> respuestas = new ArrayList<>();

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Método para agregar una respuesta
    public void agregarRespuesta(FaqRespuesta respuesta) {
        respuestas.add(respuesta);
        respuesta.setFaq(this);
        this.respondida = true;
    }

    // Método para eliminar una respuesta
    public void eliminarRespuesta(FaqRespuesta respuesta) {
        respuestas.remove(respuesta);
        respuesta.setFaq(null);
        // Si no quedan respuestas, marcar como no respondida
        if (respuestas.isEmpty()) {
            this.respondida = false;
        }
    }
}
