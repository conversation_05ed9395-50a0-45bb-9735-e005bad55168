package com.midas.crm.entity.DTO;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.midas.crm.entity.Notification.NotificationCategory;
import com.midas.crm.entity.Notification.NotificationType;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO para transferir datos de notificaciones
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NotificationDTO {
    private Long id;
    private Long recipientId;
    private Long senderId;
    private String senderName;
    private String title; // Agregamos el campo título
    private String message;
    private String time;
    private boolean read;
    private String avatar;
    private NotificationType type;
    private NotificationCategory category;
    private LocalDateTime createdAt;
    private Object data; // Datos adicionales (opcional)
}