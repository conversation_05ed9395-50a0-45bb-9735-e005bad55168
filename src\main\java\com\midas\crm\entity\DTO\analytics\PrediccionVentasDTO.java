package com.midas.crm.entity.DTO.analytics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.YearMonth;
import java.util.List;
import java.util.Map;

/**
 * DTO para representar predicciones de ventas futuras
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PrediccionVentasDTO {
    // Predicción para los próximos meses
    private List<PrediccionMensualDTO> prediccionesMensuales;
    
    // Predicción por asesor
    private Map<String, Double> prediccionPorAsesor;
    
    // Factores que influyen en la predicción
    private List<FactorPrediccionDTO> factoresInfluyentes;
    
    // Precisión del modelo
    private double precisionModelo;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PrediccionMensualDTO {
        private YearMonth mes;
        private double ventasEstimadas;
        private double intervaloConfianzaInferior;
        private double intervaloConfianzaSuperior;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FactorPrediccionDTO {
        private String factor;
        private double importancia; // Valor entre 0 y 1
        private String tendencia; // "creciente", "decreciente", "estable"
    }
}
