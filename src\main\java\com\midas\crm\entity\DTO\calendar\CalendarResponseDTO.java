package com.midas.crm.entity.DTO.calendar;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CalendarResponseDTO {
    private Long id;
    private String titulo;
    private String descripcion;
    private String color;
    private LocalDate fechaInicio;
    private LocalTime horaInicio;
    private LocalDate fechaFinal;
    private LocalTime horaFinal;
    private Boolean isActive;
    private Boolean isSeen;
    private Long userCreateId;
}
