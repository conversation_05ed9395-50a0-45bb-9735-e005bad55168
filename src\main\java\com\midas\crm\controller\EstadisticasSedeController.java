package com.midas.crm.controller;

import com.midas.crm.entity.DTO.EstadisticaSedeDTO;
import com.midas.crm.entity.DTO.EstadisticaSedePaginadaResponse;
import com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionCoordinadorDTO;
import com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO;
import com.midas.crm.service.EstadisticasSedeService;
import com.midas.crm.service.GoogleDriveService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.time.Instant;
import java.time.ZoneId;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Controlador para manejar las estadísticas por sede
 */
@RestController
@RequestMapping("/api/estadisticas-sede")
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
public class EstadisticasSedeController {

    @Autowired
    private final GoogleDriveService googleDriveService;

    @Autowired
    private EstadisticasSedeService estadisticasSedeService;

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor
     *
     * @param sedeId ID de la sede (opcional)
     * @param fecha  Fecha para filtrar (opcional, por defecto hoy)
     * @return Lista de estadísticas
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<EstadisticaSedeDTO>>> obtenerEstadisticasPorSede(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {

        try {
            // Si no se proporciona fecha, usar la fecha actual
            if (fecha == null) {
                fecha = LocalDate.now();
            }

            List<EstadisticaSedeDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasPorSede(sedeId, fecha);

            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas resumidas por sede
     *
     * @param fecha Fecha para filtrar
     * @return Estadísticas resumidas
     */
    @GetMapping("/resumen")
    public ResponseEntity<GenericResponse<List<EstadisticaSedeDTO>>> obtenerResumenPorSede(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {

        try {
            if (fecha == null) {
                fecha = LocalDate.now();
            }

            List<EstadisticaSedeDTO> resumen = estadisticasSedeService.obtenerResumenPorSede(fecha);

            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Resumen obtenido correctamente");
            response.setData(resumen);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener resumen: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas por rango de fechas
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @return Estadísticas por rango
     */
    @GetMapping("/rango")
    public ResponseEntity<GenericResponse<List<EstadisticaSedeDTO>>> obtenerEstadisticasPorRango(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId) {

        try {
            List<EstadisticaSedeDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasPorRango(fechaInicio,
                    fechaFin, sedeId);

            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas por rango obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas por rango: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    // ===== ENDPOINTS PAGINADOS =====

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor con paginación
     *
     * @param sedeId ID de la sede (opcional)
     * @param fecha  Fecha para filtrar (opcional, por defecto hoy)
     * @param page   Número de página (por defecto 0)
     * @param size   Tamaño de página (por defecto 10)
     * @param sort   Campo de ordenamiento (por defecto 'sede')
     * @return Estadísticas paginadas
     */
    @GetMapping("/paginado")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorSedePaginado(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Si no se proporciona fecha, usar la fecha actual
            if (fecha == null) {
                fecha = LocalDate.now();
            }

            // Crear objeto Pageable sin ordenamiento específico
            Pageable pageable = PageRequest.of(page, size);

            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorSedePaginado(sedeId, supervisorId, fecha, pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas paginadas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas paginadas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica con paginación y filtro de búsqueda por nombre de vendedor
     *
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param fecha            Fecha para filtrar (opcional, por defecto hoy)
     * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
     *                         (opcional)
     * @param page             Número de página (por defecto 0)
     * @param size             Tamaño de página (por defecto 10)
     * @return Estadísticas paginadas con filtro de búsqueda
     */
    @GetMapping("/paginado/busqueda")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorSedePaginadoConBusqueda(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(required = false) String busquedaVendedor,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Si no se proporciona fecha, usar la fecha actual
            if (fecha == null) {
                fecha = LocalDate.now();
            }

            // Crear objeto Pageable sin ordenamiento específico
            Pageable pageable = PageRequest.of(page, size);

            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorSedePaginadoConBusqueda(sedeId, supervisorId, fecha, busquedaVendedor,
                            pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas paginadas con búsqueda obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas paginadas con búsqueda: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas acumuladas por rango de fechas con paginación
     *
     * @param fechaInicio  Fecha de inicio del rango
     * @param fechaFin     Fecha de fin del rango
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param page         Número de página (por defecto 0)
     * @param size         Tamaño de página (por defecto 10)
     * @return Estadísticas acumuladas paginadas por rango
     */
    @GetMapping("/rango-fechas/paginado")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorRangoFechasPaginado(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {

            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener estadísticas acumuladas por rango de fechas
            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorRangoFechas(sedeId, supervisorId, fechaInicio, fechaFin, pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas acumuladas por rango de fechas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas por rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas por rango de fechas con paginación
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @param page        Número de página (por defecto 0)
     * @param size        Tamaño de página (por defecto 10)
     * @param sort        Campo de ordenamiento (por defecto 'sede')
     * @return Estadísticas paginadas por rango
     */
    @GetMapping("/rango/paginado")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorRangoPaginado(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Crear objeto Pageable sin ordenamiento específico
            Pageable pageable = PageRequest.of(page, size);

            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorRangoPaginado(fechaInicio, fechaFin, sedeId, pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas paginadas por rango obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas paginadas por rango: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas acumuladas por rango de fechas con paginación y búsqueda
     * por vendedor
     *
     * @param fechaInicio      Fecha de inicio del rango
     * @param fechaFin         Fecha de fin del rango
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
     *                         (opcional)
     * @param page             Número de página (por defecto 0)
     * @param size             Tamaño de página (por defecto 10)
     * @return Estadísticas acumuladas paginadas con búsqueda
     */
    @GetMapping("/rango-fechas/paginado/busqueda")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorRangoFechasPaginadoConBusqueda(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(required = false) String busquedaVendedor,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {

            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener estadísticas acumuladas por rango de fechas con búsqueda
            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorRangoFechasConBusqueda(sedeId, supervisorId, fechaInicio, fechaFin,
                            busquedaVendedor, pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas acumuladas por rango de fechas con búsqueda obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas por rango de fechas con búsqueda: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene leads específicos de un asesor para una fecha determinada
     *
     * @param nombreAsesor Nombre completo del asesor
     * @param fecha        Fecha para filtrar
     * @param numeroMovil  Número móvil para filtrar (opcional)
     * @param page         Número de página (por defecto 0)
     * @param size         Tamaño de página (por defecto 10)
     * @return Leads del asesor paginados
     */
    @GetMapping("/leads-asesor")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsPorAsesorYFecha(
            @RequestParam String nombreAsesor,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(required = false) String numeroMovil,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Validar parámetros de entrada
            if (nombreAsesor == null || nombreAsesor.trim().isEmpty()) {
                GenericResponse<Map<String, Object>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("El nombre del asesor es requerido");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener leads del asesor
            Map<String, Object> leadsData = estadisticasSedeService.obtenerLeadsPorAsesorYFecha(
                    nombreAsesor.trim(), fecha, numeroMovil, pageable);

            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Leads del asesor obtenidos correctamente");
            response.setData(leadsData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener leads del asesor: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene leads específicos de un asesor para un rango de fechas
     *
     * @param nombreAsesor Nombre completo del asesor
     * @param fechaInicio  Fecha de inicio para filtrar
     * @param fechaFin     Fecha de fin para filtrar
     * @param numeroMovil  Número móvil para filtrar (opcional)
     * @param page         Número de página (por defecto 0)
     * @param size         Tamaño de página (por defecto 10)
     * @return Leads del asesor paginados
     */
    @GetMapping("/leads-asesor-rango")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsPorAsesorYRangoFechas(
            @RequestParam String nombreAsesor,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) String numeroMovil,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Validar parámetros de entrada
            if (nombreAsesor == null || nombreAsesor.trim().isEmpty()) {
                GenericResponse<Map<String, Object>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("El nombre del asesor es requerido");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<Map<String, Object>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener leads del asesor por rango de fechas
            Map<String, Object> leadsData = estadisticasSedeService.obtenerLeadsPorAsesorYRangoFechas(
                    nombreAsesor.trim(), fechaInicio, fechaFin, numeroMovil, pageable);

            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Leads del asesor por rango de fechas obtenidos correctamente");
            response.setData(leadsData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener leads del asesor por rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene supervisores/coordinadores por sede
     *
     * @param sedeId ID de la sede
     * @return Lista de supervisores de la sede
     */
    @GetMapping("/supervisores-por-sede")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> obtenerSupervisoresPorSede(
            @RequestParam Long sedeId) {

        try {
            List<Map<String, Object>> supervisores = estadisticasSedeService.obtenerSupervisoresPorSede(sedeId);

            GenericResponse<List<Map<String, Object>>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Supervisores obtenidos correctamente");
            response.setData(supervisores);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<Map<String, Object>>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener supervisores: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y fecha
     * específica
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fecha        Fecha para filtrar (formato yyyy-MM-dd)
     * @return Archivo Excel con los leads filtrados
     */
    @GetMapping("/exportar-leads-por-fecha")
    public ResponseEntity<?> exportarLeadsPorRango(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {

        try {

            // Generar el Excel con los leads filtrados
            byte[] excelData = estadisticasSedeService.exportarLeadsPorRango(sedeId, supervisorId, fecha);

            if (excelData == null || excelData.length == 0) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("No se encontraron leads para los filtros especificados");
                response.setData(null);
                return ResponseEntity.ok(response);
            }

            // Crear nombre del archivo basado en los filtros
            String nombreArchivo = "leads_estadisticas_" + fecha.format(DateTimeFormatter.ISO_LOCAL_DATE);
            if (sedeId != null) {
                nombreArchivo += "_sede_" + sedeId;
            }
            if (supervisorId != null) {
                nombreArchivo += "_supervisor_" + supervisorId;
            }
            nombreArchivo += ".xlsx";

            // Configurar headers para descarga
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nombreArchivo);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {

            GenericResponse<String> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al generar el archivo Excel: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y rango de
     * fechas
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fechaInicio  Fecha de inicio del rango (formato yyyy-MM-dd)
     * @param fechaFin     Fecha de fin del rango (formato yyyy-MM-dd)
     * @return Archivo Excel con los leads filtrados
     */
    @GetMapping("/exportar-leads-por-rango-fechas")
    public ResponseEntity<?> exportarLeadsPorRangoFechas(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin) {

        try {

            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Generar el Excel con los leads filtrados por rango de fechas
            byte[] excelData = estadisticasSedeService.exportarLeadsPorRangoFechas(sedeId, supervisorId, fechaInicio,
                    fechaFin);

            if (excelData == null || excelData.length == 0) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("No se encontraron leads para el rango de fechas especificado");
                response.setData(null);
                return ResponseEntity.ok(response);
            }

            // Crear nombre del archivo basado en los filtros
            String nombreArchivo = "leads_estadisticas_" + fechaInicio.format(DateTimeFormatter.ISO_LOCAL_DATE)
                    + "_a_" + fechaFin.format(DateTimeFormatter.ISO_LOCAL_DATE);
            if (sedeId != null) {
                nombreArchivo += "_sede_" + sedeId;
            }
            if (supervisorId != null) {
                nombreArchivo += "_supervisor_" + supervisorId;
            }
            nombreArchivo += ".xlsx";

            // Configurar headers para descarga
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nombreArchivo);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {

            GenericResponse<String> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al generar el archivo Excel por rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Exporta a Excel todos los leads de un coordinador filtrados por tipo de interés
     * Este endpoint es específico para la funcionalidad de estadísticas de coordinador
     *
     * @param coordinador      Nombre del coordinador
     * @param tipoInteres      Tipo de interés: 'seguros', 'energia', 'lowi'
     * @param fecha            Fecha específica (formato yyyy-MM-dd)
     * @param fechaFin         Fecha de fin del rango (opcional, para rango de fechas)
     * @param sedeId           ID de la sede (opcional)
     * @param busquedaMovil    Término de búsqueda para filtrar por número móvil (opcional)
     * @return Archivo Excel con los leads filtrados
     */
    @GetMapping("/exportar-leads-coordinador")
    public ResponseEntity<?> exportarLeadsCoordinador(
            @RequestParam String coordinador,
            @RequestParam String tipoInteres,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) String busquedaMovil) {

        try {
            // Validar tipo de interés
            if (!tipoInteres.equals("seguros") && !tipoInteres.equals("energia") && !tipoInteres.equals("lowi")) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("Tipo de interés inválido. Debe ser: seguros, energia o lowi");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Validar fechas si es rango
            if (fechaFin != null && fecha.isAfter(fechaFin)) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Generar el Excel con los leads del coordinador filtrados
            byte[] excelData = estadisticasSedeService.exportarLeadsCoordinador(
                    coordinador, tipoInteres, fecha, fechaFin, sedeId, busquedaMovil);

            if (excelData == null || excelData.length == 0) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("No se encontraron leads para los filtros especificados");
                response.setData(null);
                return ResponseEntity.ok(response);
            }

            // Crear nombre del archivo basado en los filtros
            String tipoInteresTitle = tipoInteres.substring(0, 1).toUpperCase() + tipoInteres.substring(1);
            String coordinadorSafe = coordinador.replaceAll("[^a-zA-Z0-9]", "_");
            String fechaStr = fechaFin != null && !fecha.equals(fechaFin)
                    ? fecha.format(DateTimeFormatter.ISO_LOCAL_DATE) + "_a_" + fechaFin.format(DateTimeFormatter.ISO_LOCAL_DATE)
                    : fecha.format(DateTimeFormatter.ISO_LOCAL_DATE);

            String nombreArchivo = "Leads_" + tipoInteresTitle + "_" + coordinadorSafe + "_" + fechaStr + ".xlsx";

            // Configurar headers para descarga
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nombreArchivo);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {
            GenericResponse<String> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al generar el archivo Excel: " + e.getMessage());
            response.setData(null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Exporta a Excel todos los leads filtrados incluyendo búsqueda por vendedor
     * Este endpoint respeta TODOS los filtros aplicados en el frontend
     *
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param fecha            Fecha específica (opcional, para exportar por fecha)
     * @param fechaInicio      Fecha de inicio del rango (opcional, para exportar
     *                         por rango)
     * @param fechaFin         Fecha de fin del rango (opcional, para exportar por
     *                         rango)
     * @param busquedaVendedor Término de búsqueda para filtrar por vendedor
     *                         (opcional)
     * @return Archivo Excel con los leads filtrados
     */
    @GetMapping("/exportar-leads-filtrados")
    public ResponseEntity<?> exportarLeadsFiltrados(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) String busquedaVendedor) {

        try {

            // Validar que se proporcione al menos una fecha
            if (fecha == null && (fechaInicio == null || fechaFin == null)) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("Debe proporcionar una fecha específica o un rango de fechas");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Validar rango de fechas si se proporciona
            if (fechaInicio != null && fechaFin != null && fechaInicio.isAfter(fechaFin)) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Generar el Excel con todos los filtros aplicados
            byte[] excelData = estadisticasSedeService.exportarLeadsFiltrados(
                    sedeId, supervisorId, fecha, fechaInicio, fechaFin, busquedaVendedor);

            if (excelData == null || excelData.length == 0) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("No se encontraron leads para los filtros especificados");
                response.setData(null);
                return ResponseEntity.ok(response);
            }

            // Crear nombre del archivo basado en los filtros
            String nombreArchivo = "leads_filtrados_";
            if (fecha != null) {
                nombreArchivo += fecha.format(DateTimeFormatter.ISO_LOCAL_DATE);
            } else {
                nombreArchivo += fechaInicio.format(DateTimeFormatter.ISO_LOCAL_DATE)
                        + "_a_" + fechaFin.format(DateTimeFormatter.ISO_LOCAL_DATE);
            }
            if (sedeId != null) {
                nombreArchivo += "_sede_" + sedeId;
            }
            if (supervisorId != null) {
                nombreArchivo += "_supervisor_" + supervisorId;
            }
            if (busquedaVendedor != null && !busquedaVendedor.trim().isEmpty()) {
                nombreArchivo += "_vendedor_" + busquedaVendedor.replaceAll("[^a-zA-Z0-9]", "_");
            }
            nombreArchivo += ".xlsx";

            // Configurar headers para descarga
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nombreArchivo);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {

            GenericResponse<String> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al generar el archivo Excel filtrado: " + e.getMessage());
            response.setData(null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Obtiene el rendimiento de leads por asesor para un período específico
     *
     * @param periodo      Tipo de período: 'diario', 'semanal', 'mensual'
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fecha        Fecha de referencia (formato yyyy-MM-dd)
     * @return Lista de rendimiento de asesores
     */
    @GetMapping("/rendimiento-leads")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> obtenerRendimientoLeadsPorAsesor(
            @RequestParam String periodo,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {

        try {

            // Validar período
            if (!periodo.equals("diario") && !periodo.equals("semanal") && !periodo.equals("mensual")) {
                GenericResponse<List<Map<String, Object>>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("Período inválido. Debe ser: diario, semanal o mensual");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Obtener datos de rendimiento
            List<Map<String, Object>> rendimientoData = estadisticasSedeService.obtenerRendimientoLeadsPorAsesor(
                    periodo, sedeId, supervisorId, fecha);

            GenericResponse<List<Map<String, Object>>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Rendimiento de leads obtenido correctamente");
            response.setData(rendimientoData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {

            GenericResponse<List<Map<String, Object>>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener rendimiento de leads: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // ===== ENDPOINTS PARA ESTADÍSTICAS DE TRANSCRIPCIONES POR COORDINADOR =====

    /**
     * Obtiene estadísticas de transcripciones por coordinador para todas las fechas
     * Clasifica leads por porcentajes de eficiencia basados en nota_agente_comparador_ia
     *
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    @GetMapping("/transcripciones-coordinador")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionPorCoordinador() {
        try {
            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionPorCoordinador();

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones por coordinador: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por sede
     *
     * @param sedeId ID de la sede para filtrar
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    @GetMapping("/transcripciones-coordinador/sede")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionPorCoordinadorYSede(
            @RequestParam Long sedeId) {
        try {
            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionPorCoordinadorYSede(sedeId);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador y sede obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones por coordinador y sede: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por fecha específica
     *
     * @param fecha Fecha específica para filtrar (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    @GetMapping("/transcripciones-coordinador/fecha")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionPorCoordinadorYFecha(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {
        try {
            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionPorCoordinadorYFecha(fecha);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador y fecha obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones por coordinador y fecha: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por sede y fecha específica
     *
     * @param sedeId ID de la sede para filtrar
     * @param fecha  Fecha específica para filtrar (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    @GetMapping("/transcripciones-coordinador/sede-fecha")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionPorCoordinadorSedeYFecha(
            @RequestParam Long sedeId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {
        try {
            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionPorCoordinadorSedeYFecha(sedeId, fecha);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador, sede y fecha obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones por coordinador, sede y fecha: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por rango de fechas
     *
     * @param fechaInicio Fecha de inicio del rango (formato yyyy-MM-dd)
     * @param fechaFin    Fecha de fin del rango (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    @GetMapping("/transcripciones-coordinador/rango-fechas")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionPorCoordinadorYRangoFechas(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin) {
        try {
            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionPorCoordinadorYRangoFechas(fechaInicio, fechaFin);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador y rango de fechas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones por coordinador y rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por sede y rango de fechas
     *
     * @param sedeId      ID de la sede para filtrar
     * @param fechaInicio Fecha de inicio del rango (formato yyyy-MM-dd)
     * @param fechaFin    Fecha de fin del rango (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    @GetMapping("/transcripciones-coordinador/sede-rango-fechas")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionPorCoordinadorSedeYRangoFechas(
            @RequestParam Long sedeId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin) {
        try {
            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionPorCoordinadorSedeYRangoFechas(sedeId, fechaInicio, fechaFin);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador, sede y rango de fechas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones por coordinador, sede y rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // ===== ENDPOINTS PARA ESTADÍSTICAS DE TRANSCRIPCIONES POR ASESOR =====

    /**
     * Obtiene estadísticas de transcripciones por asesor de un coordinador específico
     */
    @GetMapping("/transcripciones-asesor/coordinador/{coordinadorId}")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionAsesorDTO>>> obtenerEstadisticasTranscripcionPorAsesor(
            @PathVariable Long coordinadorId,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin) {
        try {
            List<EstadisticaTranscripcionAsesorDTO> estadisticas =
                    estadisticasSedeService.obtenerEstadisticasTranscripcionPorAsesor(coordinadorId, sedeId, fecha, fechaInicio, fechaFin);

            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response =
                    new GenericResponse<>(1, "Estadísticas de transcripciones por asesor obtenidas exitosamente", estadisticas);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response =
                    new GenericResponse<>(0, "Error al obtener estadísticas: " + e.getMessage(), null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // ===== ENDPOINTS PARA LEADS FILTRADOS POR COORDINADOR Y TIPO DE INTERÉS =====

    /**
     * Obtiene leads específicos de un coordinador filtrados por tipo de interés para una fecha determinada
     *
     * @param nombreCoordinador Nombre completo del coordinador
     * @param tipoInteres       Tipo de interés: 'seguros', 'energia', 'lowi'
     * @param fecha             Fecha para filtrar
     * @param sedeId            ID de la sede (opcional)
     * @param numeroMovil       Número móvil para filtrar (opcional)
     * @param page              Número de página (por defecto 0)
     * @param size              Tamaño de página (por defecto 10)
     * @return Leads del coordinador filtrados por tipo de interés
     */
    @GetMapping("/leads-coordinador-tipo-interes")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsPorCoordinadorYTipoInteres(
            @RequestParam String nombreCoordinador,
            @RequestParam String tipoInteres,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) String numeroMovil,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener leads filtrados por coordinador y tipo de interés
            Map<String, Object> resultado = estadisticasSedeService.obtenerLeadsPorCoordinadorYTipoInteres(
                    nombreCoordinador, tipoInteres, fecha, sedeId, numeroMovil, pageable);

            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Leads del coordinador filtrados por tipo de interés obtenidos correctamente");
            response.setData(resultado);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener leads del coordinador: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Obtiene leads específicos de un coordinador filtrados por tipo de interés para un rango de fechas
     *
     * @param nombreCoordinador Nombre completo del coordinador
     * @param tipoInteres       Tipo de interés: 'seguros', 'energia', 'lowi'
     * @param fechaInicio       Fecha de inicio para filtrar
     * @param fechaFin          Fecha de fin para filtrar
     * @param sedeId            ID de la sede (opcional)
     * @param numeroMovil       Número móvil para filtrar (opcional)
     * @param page              Número de página (por defecto 0)
     * @param size              Tamaño de página (por defecto 10)
     * @return Leads del coordinador filtrados por tipo de interés
     */
    @GetMapping("/leads-coordinador-tipo-interes-rango")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsPorCoordinadorYTipoInteresRango(
            @RequestParam String nombreCoordinador,
            @RequestParam String tipoInteres,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) String numeroMovil,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener leads filtrados por coordinador y tipo de interés en rango de fechas
            Map<String, Object> resultado = estadisticasSedeService.obtenerLeadsPorCoordinadorYTipoInteresRango(
                    nombreCoordinador, tipoInteres, fechaInicio, fechaFin, sedeId, numeroMovil, pageable);

            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Leads del coordinador filtrados por tipo de interés en rango obtenidos correctamente");
            response.setData(resultado);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener leads del coordinador: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // ===== NUEVOS ENDPOINTS CON LÓGICA DE AUDIOS HUÉRFANOS =====

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS
     * Incluye audios de Google Drive que no tienen lead correspondiente
     *
     * LEADS CONTACTO = Total de audios de Google Drive del asesor (registrados + audios sin lead)
     * LEADS REGISTRADO = Solo audios que tienen lead en cliente_residencial
     * BIEN REGISTRADO = Leads con nota_agente_comparador_ia >= 50%
     * MAL REGISTRADO = Leads con nota_agente_comparador_ia < 50%
     * NO REGISTRADO = Audios en audio_sin_lead de números de agente del asesor
     *
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    @GetMapping("/transcripciones-coordinador-con-audios-huerfanos")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionConAudiosHuerfanos() {
        try {
            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionConAudiosHuerfanos();

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador con audios huérfanos obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones con audios huérfanos: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por sede
     *
     * @param sedeId ID de la sede para filtrar
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    @GetMapping("/transcripciones-coordinador-con-audios-huerfanos/sede")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSede(
            @RequestParam Long sedeId) {
        try {
            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSede(sedeId);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador con audios huérfanos por sede obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones con audios huérfanos por sede: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por fecha específica
     *
     * @param fecha Fecha específica para filtrar (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    @GetMapping("/transcripciones-coordinador-con-audios-huerfanos/fecha")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorFecha(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {
        try {
            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorFecha(fecha);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador con audios huérfanos por fecha obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones con audios huérfanos por fecha: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por sede y fecha específica
     *
     * @param sedeId ID de la sede para filtrar
     * @param fecha  Fecha específica para filtrar (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    @GetMapping("/transcripciones-coordinador-con-audios-huerfanos/sede-fecha")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYFecha(
            @RequestParam Long sedeId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {
        try {
            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYFecha(sedeId, fecha);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador con audios huérfanos por sede y fecha obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones con audios huérfanos por sede y fecha: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por rango de fechas
     * CARGA POR DEFECTO: Este método se usa para cargar datos del mes anterior por defecto
     *
     * @param fechaInicio Fecha de inicio del rango (formato yyyy-MM-dd)
     * @param fechaFin    Fecha de fin del rango (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    @GetMapping("/transcripciones-coordinador-con-audios-huerfanos/rango-fechas")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorRangoFechas(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin) {
        try {
            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorRangoFechas(fechaInicio, fechaFin);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador con audios huérfanos por rango de fechas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones con audios huérfanos por rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por sede y rango de fechas
     *
     * @param sedeId      ID de la sede para filtrar
     * @param fechaInicio Fecha de inicio del rango (formato yyyy-MM-dd)
     * @param fechaFin    Fecha de fin del rango (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    @GetMapping("/transcripciones-coordinador-con-audios-huerfanos/sede-rango-fechas")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>>> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYRangoFechas(
            @RequestParam Long sedeId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin) {
        try {
            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            List<EstadisticaTranscripcionCoordinadorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYRangoFechas(sedeId, fechaInicio, fechaFin);

            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por coordinador con audios huérfanos por sede y rango de fechas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionCoordinadorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones con audios huérfanos por sede y rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // ===== NUEVOS ENDPOINTS PARA ESTADÍSTICAS DE TRANSCRIPCIONES POR ASESOR CON AUDIOS HUÉRFANOS =====

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS
     *
     * Incluye audios de Google Drive que no tienen lead correspondiente en la métrica LEADS CONTACTO.
     * Esta nueva implementación proporciona una visión más completa del rendimiento real de cada asesor.
     *
     * MÉTRICAS INCLUIDAS:
     * - LEADS CONTACTO: Total de audios de Google Drive del asesor (registrados + audios sin lead)
     * - LEADS REGISTRADO: Solo audios que tienen lead correspondiente en cliente_residencial
     * - BIEN REGISTRADO: Leads con nota_agente_comparador_ia >= 50%
     * - MAL REGISTRADO: Leads con nota_agente_comparador_ia < 50%
     * - NO REGISTRADO: Audios en audio_sin_lead de números de agente del asesor
     * - EFICIENCIA: Porcentaje basado en leads registrados (no en total de contactos)
     *
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    @GetMapping("/transcripciones-asesor-con-audios-huerfanos")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionAsesorDTO>>> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanos() {
        try {
            List<EstadisticaTranscripcionAsesorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanos();

            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por asesor con audios huérfanos obtenidas exitosamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por sede
     *
     * @param sedeId ID de la sede para filtrar
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    @GetMapping("/transcripciones-asesor-con-audios-huerfanos/sede/{sedeId}")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionAsesorDTO>>> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSede(
            @PathVariable Long sedeId) {
        try {
            List<EstadisticaTranscripcionAsesorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSede(sedeId);

            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por asesor con audios huérfanos por sede obtenidas exitosamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por sede: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por fecha específica
     *
     * @param fecha Fecha específica para filtrar (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    @GetMapping("/transcripciones-asesor-con-audios-huerfanos/fecha")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionAsesorDTO>>> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorFecha(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {
        try {
            List<EstadisticaTranscripcionAsesorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorFecha(fecha);

            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por asesor con audios huérfanos por fecha obtenidas exitosamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por fecha: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por sede y fecha específica
     *
     * @param sedeId ID de la sede para filtrar
     * @param fecha Fecha específica para filtrar (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    @GetMapping("/transcripciones-asesor-con-audios-huerfanos/sede/{sedeId}/fecha")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionAsesorDTO>>> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYFecha(
            @PathVariable Long sedeId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {
        try {
            List<EstadisticaTranscripcionAsesorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYFecha(sedeId, fecha);

            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por asesor con audios huérfanos por sede y fecha obtenidas exitosamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por sede y fecha: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por rango de fechas
     *
     * @param fechaInicio Fecha de inicio del rango (formato yyyy-MM-dd)
     * @param fechaFin Fecha de fin del rango (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    @GetMapping("/transcripciones-asesor-con-audios-huerfanos/rango")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionAsesorDTO>>> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorRangoFechas(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin) {
        try {
            List<EstadisticaTranscripcionAsesorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorRangoFechas(fechaInicio, fechaFin);

            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por asesor con audios huérfanos por rango de fechas obtenidas exitosamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por sede y rango de fechas
     *
     * @param sedeId ID de la sede para filtrar
     * @param fechaInicio Fecha de inicio del rango (formato yyyy-MM-dd)
     * @param fechaFin Fecha de fin del rango (formato yyyy-MM-dd)
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    @GetMapping("/transcripciones-asesor-con-audios-huerfanos/sede/{sedeId}/rango")
    public ResponseEntity<GenericResponse<List<EstadisticaTranscripcionAsesorDTO>>> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYRangoFechas(
            @PathVariable Long sedeId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin) {
        try {
            List<EstadisticaTranscripcionAsesorDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYRangoFechas(sedeId, fechaInicio, fechaFin);

            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas de transcripciones por asesor con audios huérfanos por sede y rango de fechas obtenidas exitosamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaTranscripcionAsesorDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por sede y rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // ===== ENDPOINTS PARA OBTENER DETALLES DE MÉTRICAS CON AUDIOS HUÉRFANOS =====

    /**
     * COORDINADORES: Obtiene leads bien registrados de un coordinador específico
     * GET /api/estadisticas-sede/coordinador-leads-bien-registrados
     */
    @GetMapping("/coordinador-leads-bien-registrados")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsBienRegistradosPorCoordinador(
            @RequestParam String nombreCoordinador,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Map<String, Object> resultado = estadisticasSedeService.obtenerLeadsBienRegistradosPorCoordinador(
                    nombreCoordinador, sedeId, fecha, fechaInicio, fechaFin, pageable);

            return ResponseEntity.ok(new GenericResponse<>(1, "Leads bien registrados obtenidos exitosamente", resultado));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(0, "Error al obtener leads bien registrados: " + e.getMessage(), null));
        }
    }

    /**
     * COORDINADORES: Obtiene leads mal registrados de un coordinador específico
     * GET /api/estadisticas-sede/coordinador-leads-mal-registrados
     */
    @GetMapping("/coordinador-leads-mal-registrados")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsMalRegistradosPorCoordinador(
            @RequestParam String nombreCoordinador,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Map<String, Object> resultado = estadisticasSedeService.obtenerLeadsMalRegistradosPorCoordinador(
                    nombreCoordinador, sedeId, fecha, fechaInicio, fechaFin, pageable);

            return ResponseEntity.ok(new GenericResponse<>(1, "Leads mal registrados obtenidos exitosamente", resultado));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(0, "Error al obtener leads mal registrados: " + e.getMessage(), null));
        }
    }

    /**
     * COORDINADORES: Obtiene todos los leads registrados de un coordinador específico
     * GET /api/estadisticas-sede/coordinador-leads-registrados
     */
    @GetMapping("/coordinador-leads-registrados")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsRegistradosPorCoordinador(
            @RequestParam String nombreCoordinador,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Map<String, Object> resultado = estadisticasSedeService.obtenerLeadsRegistradosPorCoordinador(
                    nombreCoordinador, sedeId, fecha, fechaInicio, fechaFin, pageable);

            return ResponseEntity.ok(new GenericResponse<>(1, "Leads registrados obtenidos exitosamente", resultado));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(0, "Error al obtener leads registrados: " + e.getMessage(), null));
        }
    }

    /**
     * COORDINADORES: Obtiene audios huérfanos de un coordinador específico
     * GET /api/estadisticas-sede/coordinador-audios-huerfanos
     */
    @GetMapping("/coordinador-audios-huerfanos")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerAudiosHuerfanosPorCoordinador(
            @RequestParam String nombreCoordinador,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Map<String, Object> resultado = estadisticasSedeService.obtenerAudiosHuerfanosPorCoordinador(
                    nombreCoordinador, sedeId, fecha, fechaInicio, fechaFin, pageable);

            return ResponseEntity.ok(new GenericResponse<>(1, "Audios huérfanos obtenidos exitosamente", resultado));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(0, "Error al obtener audios huérfanos: " + e.getMessage(), null));
        }
    }

    /**
     * Endpoint CORREGIDO para obtener el detalle de "Leads Contacto".
     * Ahora consulta la base de datos local para asegurar que el total coincida con el resumen.
     */
    @GetMapping("/coordinador-leads-contacto/detalle")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getDetalleLeadsContacto(
            @RequestParam("coordinadorId") Long coordinadorId,
            @RequestParam(value = "sedeId", required = false) Long sedeId,
            @RequestParam(value = "fecha", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(value = "fechaInicio", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(value = "fechaFin", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Map<String, Object> resultado = estadisticasSedeService.obtenerDetalleLeadsContacto(
                    coordinadorId, sedeId, fecha, fechaInicio, fechaFin, pageable);

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Detalle de Leads Contacto Obtenido",
                    resultado
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener el detalle", null));
        }
    }

    /**
     * ASESORES: Obtiene leads bien registrados de un asesor específico
     * GET /api/estadisticas-sede/asesor-leads-bien-registrados
     */
    @GetMapping("/asesor-leads-bien-registrados")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsBienRegistradosPorAsesor(
            @RequestParam String nombreAsesor,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Map<String, Object> resultado = estadisticasSedeService.obtenerLeadsBienRegistradosPorAsesor(
                    nombreAsesor, sedeId, fecha, fechaInicio, fechaFin, pageable);

            return ResponseEntity.ok(new GenericResponse<>(1, "Leads bien registrados obtenidos exitosamente", resultado));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(0, "Error al obtener leads bien registrados: " + e.getMessage(), null));
        }
    }

    /**
     * ASESORES: Obtiene leads mal registrados de un asesor específico
     * GET /api/estadisticas-sede/asesor-leads-mal-registrados
     */
    @GetMapping("/asesor-leads-mal-registrados")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsMalRegistradosPorAsesor(
            @RequestParam String nombreAsesor,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Map<String, Object> resultado = estadisticasSedeService.obtenerLeadsMalRegistradosPorAsesor(
                    nombreAsesor, sedeId, fecha, fechaInicio, fechaFin, pageable);

            return ResponseEntity.ok(new GenericResponse<>(1, "Leads mal registrados obtenidos exitosamente", resultado));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(0, "Error al obtener leads mal registrados: " + e.getMessage(), null));
        }
    }

    /**
     * ASESORES: Obtiene todos los leads registrados de un asesor específico
     * GET /api/estadisticas-sede/asesor-leads-registrados
     */
    @GetMapping("/asesor-leads-registrados")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsRegistradosPorAsesor(
            @RequestParam String nombreAsesor,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Map<String, Object> resultado = estadisticasSedeService.obtenerLeadsRegistradosPorAsesor(
                    nombreAsesor, sedeId, fecha, fechaInicio, fechaFin, pageable);

            return ResponseEntity.ok(new GenericResponse<>(1, "Leads registrados obtenidos exitosamente", resultado));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(0, "Error al obtener leads registrados: " + e.getMessage(), null));
        }
    }

    /**
     * ASESORES: Obtiene audios huérfanos de un asesor específico
     * GET /api/estadisticas-sede/asesor-audios-huerfanos
     */
    @GetMapping("/asesor-audios-huerfanos")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerAudiosHuerfanosPorAsesor(
            @RequestParam String nombreAsesor,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Map<String, Object> resultado = estadisticasSedeService.obtenerAudiosHuerfanosPorAsesor(
                    nombreAsesor, sedeId, fecha, fechaInicio, fechaFin, pageable);

            return ResponseEntity.ok(new GenericResponse<>(1, "Audios huérfanos obtenidos exitosamente", resultado));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(0, "Error al obtener audios huérfanos: " + e.getMessage(), null));
        }
    }

    @GetMapping("/coordinador-audios-google-drive")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getAudiosGoogleDrivePorCoordinador(
            @RequestParam("coordinadorId") Long   coordinadorId,
            @RequestParam(value = "sedeId",     required = false) Long   sedeId,
            @RequestParam(value = "fecha",      required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(value = "fechaInicio",required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(value = "fechaFin",   required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(value = "page",       defaultValue = "0")        int    page,
            @RequestParam(value = "size",       defaultValue = "10")       int    size
    ) {
        try {
            // 1. Get agent numbers for the coordinator
            List<String> agentes = estadisticasSedeService.obtenerNumerosAgentePorCoordinador(
                    coordinadorId, sedeId, fecha, fechaInicio, fechaFin
            );

            // 2. Get the list of audio files from Drive, now with parsed data
            Map<String,Object> result = googleDriveService.listarAudiosPorAgentes(
                    agentes, fecha, fechaInicio, fechaFin, page, size
            );

            // 3. NEW: Add advisor's name to each audio file record
            List<Map<String, Object>> content = (List<Map<String, Object>>) result.get("content");
            for (Map<String, Object> audio : content) {
                String agente = (String) audio.get("extractedAgente");
                String fechaStr = (String) audio.get("createdTime");
                if (agente != null && fechaStr != null) {
                    // Find which advisor was using that agent number on that specific day
                    String nombreAsesor = estadisticasSedeService.obtenerNombreAsesorPorAgenteYFecha(agente, Instant.parse(fechaStr).atZone(ZoneId.systemDefault()).toLocalDate());
                    audio.put("asesorNombre", nombreAsesor != null ? nombreAsesor : "Asesor no encontrado");
                } else {
                    audio.put("asesorNombre", "Información insuficiente");
                }
            }

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Audios de Google Drive obtenidos",
                    result
            ));
        } catch (Exception e) {
            // Log the full error for better debugging
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Ha ocurrido un error en el servidor", null));
        }
    }

}
