package com.midas.crm.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import com.midas.crm.utils.MidasConstants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Order(2)
public class SqlInjectionFilter implements Filter {

	@Override
	public void init(FilterConfig filterConfig) {
	}

	@Override
	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) {
		List<String> values = new ArrayList<>();
		String[] tempValues = null;
		boolean sqlInjection = false;
		if (req instanceof HttpServletRequest) {
			HttpServletRequest request = (HttpServletRequest) req;
			Enumeration<?> params = request.getParameterNames();
			while (params.hasMoreElements()) {
				String nameParam = params.nextElement().toString();
				tempValues = request.getParameterValues(nameParam);
				values.add(tempValues[0]);
			}

		}
		try {
			if (!ObjectUtils.isEmpty(values)) {
				log.debug("Interceptor verificación SqlInjection....");
				sqlInjection = values.stream().anyMatch(this::validarParamRequest);
			}
			if (sqlInjection) {
				log.debug("Se intercepto petición posible SqlInjection....");
				HttpServletResponse response = (HttpServletResponse) res;
				response.setHeader(MidasConstants.HEADER_RESPONSE_NAME_SERVER, MidasConstants.HEADER_RESPONSE_VALUE_SERVER);
				response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
			} else {
				chain.doFilter(req, res);
			}
		} catch (Exception e) {
			log.error("StackTrace : ", e);
			log.error("Ocurrio un error en SqlInjectionFilter {}", e.getMessage());
		}
	}
    private boolean validarParamRequest(String parameterValue) {
		final List<String> listaSqlInjection = Arrays.asList(MidasConstants.LIST_VALUE_SQL_INJECTION.split(MidasConstants.PIPE_URL));
		String[] paramReq = parameterValue.replaceAll(MidasConstants.REGEX_ALPHANUMERIC_VALIDATE, MidasConstants.COMA).split(MidasConstants.COMA);
		List<String> listValueReq = Arrays.asList(paramReq);
		listValueReq = listValueReq.stream().filter(obj -> !obj.isEmpty()).map(String::toUpperCase).collect(Collectors.toList());
		boolean resultado = false;
		log.debug("SqlInjectionFilter.validarParamRequest.listValueRq {}", listValueReq);
		try {
			if (!ObjectUtils.isEmpty(listValueReq)) {
				if (listValueReq.stream().anyMatch(listaSqlInjection::contains)) {
					resultado = true;
				}
			}
		} catch (Exception e) {
			log.error("StackTrace : ", e);
			log.error("error en validarParamRequest {}", e.getMessage());
		}
		log.debug("SqlInjectionFilter.validarParamRequest.resultado {}", resultado);
		return resultado;
    }

	@Override
	public void destroy() {
	}

}
