# Script de PowerShell para monitorear la salud de la aplicación CRM
# Ejecutar: .\monitor-health.ps1

param(
    [string]$BaseUrl = "http://localhost:9039",
    [int]$IntervalSeconds = 30,
    [switch]$Continuous
)

function Get-Timestamp {
    return Get-Date -Format "yyyy-MM-dd HH:mm:ss"
}

function Test-Endpoint {
    param([string]$Url, [string]$Name)
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method Get -TimeoutSec 10
        Write-Host "✅ $(Get-Timestamp) - $Name: OK" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ $(Get-Timestamp) - $Name: ERROR - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Get-ApplicationMetrics {
    param([string]$BaseUrl)
    
    try {
        $metricsUrl = "$BaseUrl/actuator/prometheus"
        $metrics = Invoke-RestMethod -Uri $metricsUrl -Method Get -TimeoutSec 10
        
        # Extraer métricas clave
        $lines = $metrics -split "`n"
        
        # Buscar métricas específicas
        $activeRequests = ($lines | Where-Object { $_ -match "http_server_requests_active_seconds_max.*POST" }) -replace '.*\s(\d+\.?\d*)', '$1'
        $oldGenUsed = ($lines | Where-Object { $_ -match "jvm_memory_used_bytes.*Old Gen" }) -replace '.*\s(\d+\.?\d*)', '$1'
        $hikariActive = ($lines | Where-Object { $_ -match "hikari_connections_active" }) -replace '.*\s(\d+\.?\d*)', '$1'
        $threads = ($lines | Where-Object { $_ -match "jvm_threads_live_threads" }) -replace '.*\s(\d+\.?\d*)', '$1'
        
        Write-Host "📊 $(Get-Timestamp) - Métricas:" -ForegroundColor Cyan
        if ($activeRequests) { Write-Host "   POST Requests Max Duration: $activeRequests s" }
        if ($oldGenUsed) { Write-Host "   Old Gen Memory: $([math]::Round($oldGenUsed/1MB, 2)) MB" }
        if ($hikariActive) { Write-Host "   Hikari Active Connections: $hikariActive" }
        if ($threads) { Write-Host "   Live Threads: $threads" }
        
    }
    catch {
        Write-Host "⚠️ $(Get-Timestamp) - No se pudieron obtener métricas: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

function Get-LongRunningRequests {
    param([string]$BaseUrl)
    
    try {
        $url = "$BaseUrl/api/monitoring/requests/long-running"
        $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 10 -Headers @{
            "Authorization" = "Bearer YOUR_TOKEN_HERE"  # Reemplazar con token real
        }
        
        if ($response.data.totalLongRequests -gt 0) {
            Write-Host "🚨 $(Get-Timestamp) - PETICIONES LARGAS DETECTADAS: $($response.data.totalLongRequests)" -ForegroundColor Red
            foreach ($request in $response.data.requests) {
                Write-Host "   - $($request.method) $($request.uri) - $([math]::Round($request.duration/1000, 1))s" -ForegroundColor Yellow
            }
        }
    }
    catch {
        # Silenciar errores de autenticación para este endpoint
        if ($_.Exception.Message -notmatch "401|403") {
            Write-Host "⚠️ $(Get-Timestamp) - Error verificando peticiones largas: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

function Show-HealthSummary {
    param([string]$BaseUrl)
    
    Write-Host "`n🏥 $(Get-Timestamp) - RESUMEN DE SALUD" -ForegroundColor Magenta
    Write-Host "=" * 50 -ForegroundColor Magenta
    
    # Verificar endpoints básicos
    $healthOk = Test-Endpoint "$BaseUrl/actuator/health" "Health Check"
    $cacheOk = Test-Endpoint "$BaseUrl/api/public/cache/health" "Cache Health"
    $prometheusOk = Test-Endpoint "$BaseUrl/actuator/prometheus" "Prometheus Metrics"
    
    # Obtener métricas
    Get-ApplicationMetrics $BaseUrl
    
    # Verificar peticiones largas
    Get-LongRunningRequests $BaseUrl
    
    Write-Host "=" * 50 -ForegroundColor Magenta
    
    $overallHealth = $healthOk -and $cacheOk -and $prometheusOk
    if ($overallHealth) {
        Write-Host "✅ Estado general: SALUDABLE" -ForegroundColor Green
    } else {
        Write-Host "❌ Estado general: PROBLEMAS DETECTADOS" -ForegroundColor Red
    }
    
    Write-Host ""
}

# Función principal
function Start-Monitoring {
    Write-Host "🚀 Iniciando monitoreo de CRM en $BaseUrl" -ForegroundColor Blue
    Write-Host "Presiona Ctrl+C para detener`n" -ForegroundColor Gray
    
    do {
        Show-HealthSummary $BaseUrl
        
        if ($Continuous) {
            Write-Host "⏱️ Esperando $IntervalSeconds segundos..." -ForegroundColor Gray
            Start-Sleep -Seconds $IntervalSeconds
        }
    } while ($Continuous)
}

# Ejecutar monitoreo
Start-Monitoring
