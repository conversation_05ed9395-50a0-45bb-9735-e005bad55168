package com.midas.crm.controller;

import com.midas.crm.entity.DTO.faq.FaqDTO;
import com.midas.crm.entity.EstadoFaq;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.service.FaqService;
import com.midas.crm.utils.GenericResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("${api.route.faqs}")
@Slf4j
public class FaqController {

    @Autowired
    private FaqService faqService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @PostMapping
    public ResponseEntity<GenericResponse<FaqDTO>> createFaq(@Valid @RequestBody FaqDTO faqDTO) {
        FaqDTO savedFaq = faqService.saveFaq(faqDTO);

        GenericResponse<FaqDTO> response = new GenericResponse<>(1, "FAQ creado exitosamente", savedFaq);

        // Notificar a todos los clientes sobre el nuevo FAQ
        messagingTemplate.convertAndSend("/topic/faqs", faqService.getAllFaqs());

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<FaqDTO>> updateFaq(@Valid @RequestBody FaqDTO faqDTO, @PathVariable Long id) {
        FaqDTO updatedFaq = faqService.updateFaq(faqDTO, id);

        GenericResponse<FaqDTO> response = new GenericResponse<>(1, "FAQ actualizado exitosamente", updatedFaq);

        // Notificar a todos los clientes sobre la actualización
        messagingTemplate.convertAndSend("/topic/faqs", faqService.getAllFaqs());

        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Void>> deleteFaq(@PathVariable Long id) {
        faqService.deleteFaq(id);

        GenericResponse<Void> response = new GenericResponse<>(1, "FAQ eliminado exitosamente", null);

        // Notificar a todos los clientes sobre la eliminación
        messagingTemplate.convertAndSend("/topic/faqs", faqService.getAllFaqs());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<FaqDTO>> getFaqById(@PathVariable Long id) {
        FaqDTO faq = faqService.getFaqById(id);

        GenericResponse<FaqDTO> response = new GenericResponse<>(1, "FAQ obtenido exitosamente", faq);

        return ResponseEntity.ok(response);
    }

    @GetMapping
    public ResponseEntity<GenericResponse<List<FaqDTO>>> getAllFaqs() {
        List<FaqDTO> faqs = faqService.getAllFaqs();

        GenericResponse<List<FaqDTO>> response = new GenericResponse<>(1, "FAQs obtenidos exitosamente", faqs);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/categoria/{categoria}")
    public ResponseEntity<GenericResponse<List<FaqDTO>>> getFaqsByCategoria(@PathVariable String categoria) {
        List<FaqDTO> faqs = faqService.getFaqsByCategoria(categoria);

        GenericResponse<List<FaqDTO>> response = new GenericResponse<>(1, "FAQs por categoría obtenidos exitosamente", faqs);

        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/increment-views")
    public ResponseEntity<GenericResponse<FaqDTO>> incrementViews(@PathVariable Long id) {
        FaqDTO faq = faqService.incrementViews(id);

        GenericResponse<FaqDTO> response = new GenericResponse<>(1, "Vistas incrementadas exitosamente", faq);

        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/estado/{estado}")
    public ResponseEntity<GenericResponse<FaqDTO>> cambiarEstadoFaq(@PathVariable Long id, @PathVariable String estado) {
        EstadoFaq estadoFaq = EstadoFaq.valueOf(estado.toUpperCase());
        FaqDTO faq = faqService.cambiarEstadoFaq(id, estadoFaq);

        GenericResponse<FaqDTO> response = new GenericResponse<>(1, "Estado de FAQ actualizado exitosamente", faq);

        // Notificar a todos los clientes sobre la actualización
        messagingTemplate.convertAndSend("/topic/faqs", faqService.getAllFaqs());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/estado/{estado}")
    public ResponseEntity<GenericResponse<List<FaqDTO>>> getPreguntasPorEstado(@PathVariable String estado) {
        EstadoFaq estadoFaq = EstadoFaq.valueOf(estado.toUpperCase());
        List<FaqDTO> faqs = faqService.getPreguntasPorEstado(estadoFaq);

        GenericResponse<List<FaqDTO>> response = new GenericResponse<>(1, "FAQs por estado obtenidos exitosamente", faqs);

        return ResponseEntity.ok(response);
    }

    // Endpoints WebSocket para FAQs
    @MessageMapping("/faqs.list")
    @SendTo("/topic/faqs")
    public List<FaqDTO> processFaqsList() {
        // log.info("Solicitud WebSocket para listar FAQs");
        return faqService.getAllFaqs();
    }

    @MessageMapping("/faqs.create")
    @SendTo("/topic/faqs")
    public List<FaqDTO> createFaqWs(@Payload FaqDTO faqDTO, SimpMessageHeaderAccessor headerAccessor) {
        // log.info("Solicitud WebSocket para crear FAQ: {}", faqDTO.getPregunta());

        // Obtener el usuario autenticado si está disponible
        Long usuarioId = null;
        if (headerAccessor.getUser() instanceof Authentication authentication &&
                authentication.getPrincipal() instanceof UserPrincipal userPrincipal) {
            usuarioId = userPrincipal.getId();
        }

        // Si tenemos un usuario, crear la pregunta asociada a él
        if (usuarioId != null) {
            faqService.crearPregunta(faqDTO, usuarioId);
        } else {
            // Si no hay usuario autenticado, crear un FAQ normal
            faqService.saveFaq(faqDTO);
        }

        // Devolver la lista actualizada
        return faqService.getAllFaqs();
    }

    @MessageMapping("/faqs.responder")
    @SendTo("/topic/faqs")
    public List<FaqDTO> responderPreguntaWs(@Payload Map<String, Object> payload, SimpMessageHeaderAccessor headerAccessor) {
        // log.info("Solicitud WebSocket para responder pregunta");

        // Extraer datos del payload
        Long faqId = Long.valueOf(payload.get("faqId").toString());
        String respuesta = payload.get("respuesta").toString();

        // Obtener el usuario autenticado si está disponible
        Long usuarioId = null;
        if (headerAccessor.getUser() instanceof Authentication authentication &&
                authentication.getPrincipal() instanceof UserPrincipal userPrincipal) {
            usuarioId = userPrincipal.getId();
        }

        // Si tenemos un usuario, responder la pregunta
        if (usuarioId != null) {
            faqService.responderPregunta(faqId, respuesta, usuarioId);
        } else {
            // Si no hay usuario autenticado, actualizar el FAQ normalmente
            FaqDTO faqDTO = faqService.getFaqById(faqId);
            faqDTO.setRespuesta(respuesta);
            faqDTO.setRespondida(true);
            faqService.updateFaq(faqDTO, faqId);
        }

        // Devolver la lista actualizada
        return faqService.getAllFaqs();
    }

    @MessageMapping("/faqs.by-usuario")
    @SendTo("/topic/faqs/usuario")
    public List<FaqDTO> getPreguntasByUsuarioWs(@Payload Map<String, Object> payload) {
        // log.info("Solicitud WebSocket para obtener preguntas por usuario");

        // Extraer el ID del usuario del payload
        Long usuarioId = Long.valueOf(payload.get("usuarioId").toString());

        // Devolver las preguntas del usuario
        return faqService.getPreguntasByUsuario(usuarioId);
    }

    @MessageMapping("/faqs.sin-responder")
    @SendTo("/topic/faqs/sin-responder")
    public List<FaqDTO> getPreguntasSinResponderWs() {
        // log.info("Solicitud WebSocket para obtener preguntas sin responder");
        return faqService.getPreguntasSinResponder();
    }

    @MessageMapping("/faqs.publicas")
    @SendTo("/topic/faqs/publicas")
    public List<FaqDTO> getPreguntasPublicasWs() {
        // log.info("Solicitud WebSocket para obtener preguntas públicas");
        return faqService.getPreguntasPublicas();
    }

    @MessageMapping("/faqs.estado")
    @SendTo("/topic/faqs/estado")
    public List<FaqDTO> getPreguntasPorEstadoWs(@Payload Map<String, Object> payload) {
        // Extraer el estado del payload
        String estadoStr = payload.get("estado").toString();
        EstadoFaq estado = EstadoFaq.valueOf(estadoStr.toUpperCase());

        // Devolver las preguntas por estado
        return faqService.getPreguntasPorEstado(estado);
    }

    @MessageMapping("/faqs.cambiar-estado")
    @SendTo("/topic/faqs")
    public List<FaqDTO> cambiarEstadoFaqWs(@Payload Map<String, Object> payload, SimpMessageHeaderAccessor headerAccessor) {
        // Extraer datos del payload
        Long faqId = Long.valueOf(payload.get("faqId").toString());
        String estadoStr = payload.get("estado").toString();
        EstadoFaq estado = EstadoFaq.valueOf(estadoStr.toUpperCase());

        // Cambiar el estado de la pregunta
        faqService.cambiarEstadoFaq(faqId, estado);

        // Devolver la lista actualizada
        return faqService.getAllFaqs();
    }
}
