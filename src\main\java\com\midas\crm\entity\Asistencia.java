package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.time.LocalDateTime;

@Entity
@Table(name = "asistencias")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Asistencia {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User usuario;

    // Campos básicos de entrada/salida
    @Column(name = "fecha_hora_entrada", nullable = false)
    private LocalDateTime fechaHoraEntrada;

    @Column(name = "fecha_hora_salida")
    private LocalDateTime fechaHoraSalida;

    @Column(name = "ip_entrada")
    private String ipEntrada;

    @Column(name = "ip_salida")
    private String ipSalida;

    @Column(name = "dispositivo_entrada")
    private String dispositivoEntrada;

    @Column(name = "dispositivo_salida")
    private String dispositivoSalida;

    @Column(name = "ubicacion_entrada")
    private String ubicacionEntrada;

    @Column(name = "ubicacion_salida")
    private String ubicacionSalida;

    // 🔥 CAMPOS PARA BREAKS
    @Column(name = "break_contador", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer breakContador = 0;

    @Column(name = "break_tiempo_total_minutos", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer breakTiempoTotalMinutos = 0;

    @Column(name = "break_activo", nullable = false, columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean breakActivo = false;

    @Column(name = "break_inicio_actual")
    private LocalDateTime breakInicioActual;

    // 🔥 CAMPOS PARA BAÑOS
    @Column(name = "bano_contador", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer banoContador = 0;

    @Column(name = "bano_tiempo_total_minutos", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer banoTiempoTotalMinutos = 0;

    @Column(name = "bano_activo", nullable = false, columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean banoActivo = false;

    @Column(name = "bano_inicio_actual")
    private LocalDateTime banoInicioActual;

    // 🔥 CAMPOS PARA ANÁLISIS
    @Column(name = "tiempo_neto_trabajado_minutos", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer tiempoNetoTrabajadoMinutos = 0;

    @Column(name = "porcentaje_jornada", nullable = false, columnDefinition = "DECIMAL(5,2) DEFAULT 0.00")
    private Double porcentajeJornada = 0.0;

    @Enumerated(EnumType.STRING)
    @Column(name = "estado_actual", nullable = false, columnDefinition = "VARCHAR(20) DEFAULT 'TRABAJANDO'")
    private EstadoActual estadoActual = EstadoActual.TRABAJANDO;

    // 🔥 HISTORIAL EN JSON
    @Column(name = "breaks_historial", columnDefinition = "JSON")
    private String breaksHistorial;

    @Column(name = "banos_historial", columnDefinition = "JSON")
    private String banosHistorial;

    // Campos existentes
    @Enumerated(EnumType.STRING)
    @Column(name = "tipo_actividad")
    private TipoActividad tipoActividad = TipoActividad.ENTRADA;

    @Enumerated(EnumType.STRING)
    @Column(name = "subtipo_actividad")
    private SubtipoActividad subtipoActividad;

    @Column(name = "duracion_minutos")
    private Integer duracionMinutos;

    @Column(name = "tiempo_sesion_minutos")
    private Integer tiempoSesionMinutos;

    @Column(name = "estado", length = 1, nullable = false)
    private String estado = "A";

    @Column(name = "observaciones", length = 500)
    private String observaciones;

    // 🔥 CONTADORES POR DÍA (opcionales)
    @Column(name = "contador_bano_dia", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer contadorBanoDia = 0;

    @Column(name = "contador_break_dia", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer contadorBreakDia = 0;

    // 🔥 ENUMS
    public enum EstadoActual {
        TRABAJANDO,
        EN_BREAK,
        EN_BANO,
        SALIDA,
        CERRADO_SISTEMA
    }

    public enum TipoActividad {
        ENTRADA,
        SALIDA,
        BREAK,
        BANO,
        SESION_CRM
    }

    public enum SubtipoActividad {
        AUTOMATICA,
        MANUAL,
        INICIO_BREAK,
        FIN_BREAK,
        INICIO_BANO,
        FIN_BANO,
        INICIO_SESION,
        FIN_SESION
    }

    // 🔥 MÉTODOS DE UTILIDAD

    /**
     * Verifica si puede tomar un break
     */
    public boolean puedeTomarBreak() {
        return this.breakContador < 2 && !this.breakActivo && !this.banoActivo;
    }

    /**
     * Verifica si puede ir al baño
     */
    public boolean puedeIrAlBano() {
        return this.banoContador < 2 && !this.banoActivo && !this.breakActivo;
    }

    /**
     * Obtiene el tiempo total de descansos
     */
    public int getTiempoTotalDescansos() {
        return this.breakTiempoTotalMinutos + this.banoTiempoTotalMinutos;
    }

    /**
     * Calcula el tiempo trabajado hasta ahora
     */
    public int calcularTiempoTrabajado() {
        if (this.fechaHoraEntrada == null) return 0;

        LocalDateTime fin = this.fechaHoraSalida != null ?
                this.fechaHoraSalida : LocalDateTime.now();

        long tiempoTotal = Duration.between(this.fechaHoraEntrada, fin).toMinutes();
        return Math.max(0, (int) (tiempoTotal - getTiempoTotalDescansos()));
    }
}