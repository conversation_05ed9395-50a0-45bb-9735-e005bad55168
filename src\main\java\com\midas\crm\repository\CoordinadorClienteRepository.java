package com.midas.crm.repository;

import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.ClienteResidencial;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;

@Repository
public interface CoordinadorClienteRepository extends JpaRepository<ClienteResidencial, Long> {

    @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
            "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
            "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) " +
            "FROM ClienteResidencial cr JOIN cr.usuario u " +
            "LEFT JOIN u.coordinador c " +
            "WHERE u.coordinador.id = :coordinadorId " +
            "AND (:dni IS NULL OR u.dni LIKE CONCAT('%', :dni, '%')) " +
            "AND (:nombre IS NULL OR CONCAT(u.nombre, ' ', u.apellido) LIKE CONCAT('%', :nombre, '%')) " +
            "AND (:numeroMovil IS NULL OR cr.movilContacto LIKE CONCAT('%', :numeroMovil, '%')) " +
            "AND (:fecha IS NULL OR DATE(cr.fechaCreacion) = :fecha)")
    Page<ClienteConUsuarioDTO> findClientesByCoordinadorWithFilters(
            @Param("coordinadorId") Long coordinadorId,
            @Param("dni") String dni,
            @Param("nombre") String nombre,
            @Param("numeroMovil") String numeroMovil,
            @Param("fecha") LocalDate fecha,
            Pageable pageable);



}