package com.midas.crm.service;

import com.midas.crm.entity.DTO.manual.ManualDTO;
import com.midas.crm.entity.DTO.manual.ManualResponseDto;
import com.midas.crm.entity.Manual;
import com.midas.crm.entity.Role;
import org.springframework.data.domain.Page;

import org.springframework.web.multipart.MultipartFile;
import java.util.List;

public interface ManualService {
    /**
     * Obtiene una página de manuales con filtros opcionales
     */
    Page<ManualResponseDto> index(String search, int page, int size, String column, String order);

    /**
     * Obtiene una página de manuales considerando el rol del usuario
     * Los usuarios ADMIN pueden ver todos los manuales (activos e inactivos)
     * Los demás usuarios solo pueden ver manuales activos
     */
    Page<ManualResponseDto> indexByRole(String search, int page, int size, String column, String order, Role role);

    /**
     * Obtiene todos los manuales
     */
    List<Manual> getAll();

    /**
     * Obtiene todos los manuales según el rol del usuario
     */
    List<Manual> getAllByRole(Role role);

    /**
     * Obtiene un manual por su ID
     */
    Manual getById(int id);

    /**
     * Crea un nuevo manual
     */
    Manual create(ManualDTO dto, MultipartFile file);

    /**
     * Actualiza un manual existente
     */
    Manual update(int id, ManualDTO dto, MultipartFile file);

    /**
     * Primera etapa de eliminación: marca el manual como inactivo
     */
    boolean delete(int id, Long userDeleteId);

    /**
     * Segunda etapa de eliminación: elimina permanentemente un manual que ya está
     * inactivo
     */
    boolean permanentDelete(int id, Long userDeleteId);

    /**
     * Restaura un manual eliminado o inactivo
     */
    Manual restore(int id);

    /**
     * Obtiene manuales filtrados por sede
     */
    List<Manual> getBySedeId(Long sedeId);

    /**
     * Obtiene manuales paginados filtrados por sede
     */
    Page<Manual> indexBySedeId(Long sedeId, String search, int page, int size, String column, String order);

    /**
     * Obtiene manuales globales (sin sede) o de una sede específica
     * Esto permite que los usuarios vean manuales generales + manuales de su sede
     */
    List<Manual> getGlobalOrBySedeId(Long sedeId);

    /**
     * Obtiene manuales globales (sin sede) o de una sede específica paginados
     */
    Page<Manual> indexGlobalOrBySedeId(Long sedeId, String search, int page, int size, String column, String order);
}
