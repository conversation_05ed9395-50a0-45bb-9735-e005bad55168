package com.midas.crm.utils;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * Convertidor para transformar strings a LocalDate
 * Soporta múltiples formatos de fecha, priorizando ISO-8601
 */
@Component
public class StringToLocalDateConverter implements Converter<String, LocalDate> {

    private static final DateTimeFormatter[] FORMATTERS = {
        DateTimeFormatter.ISO_DATE,                                // yyyy-MM-dd
        DateTimeFormatter.ISO_DATE_TIME,                          // yyyy-MM-dd'T'HH:mm:ss
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    };

    @Override
    public LocalDate convert(String source) {
        if (source == null || source.isEmpty()) {
            return null;
        }

        // Intentar con cada formato
        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                return LocalDate.parse(source, formatter);
            } catch (DateTimeParseException e) {
                // Intentar con el siguiente formato
            }
        }

        // Si llegamos aquí, ningún formato funcionó
        throw new IllegalArgumentException("No se pudo convertir la fecha: " + source);
    }
}