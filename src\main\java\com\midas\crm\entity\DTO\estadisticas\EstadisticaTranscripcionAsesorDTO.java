package com.midas.crm.entity.DTO.estadisticas;

/**
 * DTO para estadísticas de transcripciones por asesor individual
 *
 * NUEVA LÓGICA CON AUDIOS HUÉRFANOS:
 * - LEADS CONTACTO = Total de audios de Google Drive del asesor (registrados + audios sin lead)
 * - LEADS REGISTRADO = Solo audios que tienen lead en cliente_residencial
 * - BIEN REGISTRADO = Leads con nota_agente_comparador_ia >= 50%
 * - MAL REGISTRADO = Leads con nota_agente_comparador_ia < 50%
 * - NO REGISTRADO = Audios en audio_sin_lead de números de agente del asesor
 *
 * PROBLEMA COMPLEJO: Un asesor puede usar múltiples números de agente en diferentes fechas,
 * incluso dentro del mismo mes. Las consultas SQL manejan esta complejidad agregando
 * correctamente todos los audios huérfanos correspondientes.
 */
public class EstadisticaTranscripcionAsesorDTO {

    private String nombreComercial;

    /**
     * LEADS CONTACTO: Total de audios de Google Drive del asesor
     * Incluye tanto audios registrados como audios huérfanos
     */
    private Long leadsContacto;

    /**
     * LEADS REGISTRADO: Solo audios que tienen lead correspondiente en cliente_residencial
     * Este es el campo que se usa para calcular la eficiencia real
     */
    private Long leadsRegistrado;

    /**
     * BIEN REGISTRADO: Leads con nota_agente_comparador_ia >= 50%
     */
    private Long bienRegistrado;

    /**
     * MAL REGISTRADO: Leads con nota_agente_comparador_ia < 50%
     */
    private Long malRegistrado;

    /**
     * NO REGISTRADO: Audios en audio_sin_lead de números de agente del asesor
     */
    private Long noRegistrado;

    /**
     * EFICIENCIA: Porcentaje basado en leads registrados (no en total de contactos)
     */
    private Double eficienciaBienRegistrado;

    // Constructor completo con nueva lógica
    public EstadisticaTranscripcionAsesorDTO(String nombreComercial, Long leadsContacto, Long leadsRegistrado,
                                             Long bienRegistrado, Long malRegistrado, Long noRegistrado) {
        this.nombreComercial = nombreComercial;
        this.leadsContacto = leadsContacto;
        this.leadsRegistrado = leadsRegistrado;
        this.bienRegistrado = bienRegistrado;
        this.malRegistrado = malRegistrado;
        this.noRegistrado = noRegistrado;
        this.eficienciaBienRegistrado = calcularEficiencia();
    }

    /**
     * Constructor para compatibilidad con lógica anterior (DEPRECATED)
     * @deprecated Usar constructor con leadsRegistrado para nueva lógica
     */
    @Deprecated
    public EstadisticaTranscripcionAsesorDTO(String nombreComercial, Long leadsContacto,
                                             Long bienRegistrado, Long malRegistrado, Long noRegistrado) {
        this.nombreComercial = nombreComercial;
        this.leadsContacto = leadsContacto;
        this.leadsRegistrado = leadsContacto; // Para compatibilidad, asumimos que todos los contactos están registrados
        this.bienRegistrado = bienRegistrado;
        this.malRegistrado = malRegistrado;
        this.noRegistrado = noRegistrado;
        this.eficienciaBienRegistrado = calcularEficienciaAntigua();
    }

    // Constructor sin parámetros
    public EstadisticaTranscripcionAsesorDTO() {
    }

    /**
     * Calcula la eficiencia basada en leads registrados (NUEVA LÓGICA)
     * Eficiencia = (bien_registrado / leads_registrado) * 100
     */
    private Double calcularEficiencia() {
        if (leadsRegistrado == null || leadsRegistrado == 0) {
            return 0.0;
        }
        if (bienRegistrado == null) {
            return 0.0;
        }
        return (bienRegistrado.doubleValue() / leadsRegistrado.doubleValue()) * 100.0;
    }

    /**
     * Calcula la eficiencia basada en total de contactos (LÓGICA ANTERIOR)
     * @deprecated Usar calcularEficiencia() para nueva lógica
     */
    @Deprecated
    private Double calcularEficienciaAntigua() {
        if (leadsContacto == null || leadsContacto == 0) {
            return 0.0;
        }
        if (bienRegistrado == null) {
            return 0.0;
        }
        return (bienRegistrado.doubleValue() / leadsContacto.doubleValue()) * 100.0;
    }

    /**
     * Recalcula la eficiencia cuando se modifican los valores
     */
    public void recalcularEficiencia() {
        this.eficienciaBienRegistrado = calcularEficiencia();
    }

    // Getters y Setters
    public String getNombreComercial() {
        return nombreComercial;
    }

    public void setNombreComercial(String nombreComercial) {
        this.nombreComercial = nombreComercial;
    }

    public Long getLeadsContacto() {
        return leadsContacto;
    }

    public void setLeadsContacto(Long leadsContacto) {
        this.leadsContacto = leadsContacto;
        recalcularEficiencia();
    }

    public Long getLeadsRegistrado() {
        return leadsRegistrado;
    }

    public void setLeadsRegistrado(Long leadsRegistrado) {
        this.leadsRegistrado = leadsRegistrado;
    }

    /**
     * @deprecated Usar getLeadsRegistrado() para nueva lógica
     */
    @Deprecated
    public Long getLeadsRegistro() {
        return leadsRegistrado;
    }

    /**
     * @deprecated Usar setLeadsRegistrado() para nueva lógica
     */
    @Deprecated
    public void setLeadsRegistro(Long leadsRegistro) {
        this.leadsRegistrado = leadsRegistro;
    }

    public Long getBienRegistrado() {
        return bienRegistrado;
    }

    public void setBienRegistrado(Long bienRegistrado) {
        this.bienRegistrado = bienRegistrado;
        recalcularEficiencia();
    }

    public Long getMalRegistrado() {
        return malRegistrado;
    }

    public void setMalRegistrado(Long malRegistrado) {
        this.malRegistrado = malRegistrado;
    }

    public Long getNoRegistrado() {
        return noRegistrado;
    }

    public void setNoRegistrado(Long noRegistrado) {
        this.noRegistrado = noRegistrado;
    }

    public Double getEficienciaBienRegistrado() {
        return eficienciaBienRegistrado;
    }

    public void setEficienciaBienRegistrado(Double eficienciaBienRegistrado) {
        this.eficienciaBienRegistrado = eficienciaBienRegistrado;
    }

    @Override
    public String toString() {
        return "EstadisticaTranscripcionAsesorDTO{" +
                "nombreComercial='" + nombreComercial + '\'' +
                ", leadsContacto=" + leadsContacto +
                ", leadsRegistrado=" + leadsRegistrado +
                ", bienRegistrado=" + bienRegistrado +
                ", malRegistrado=" + malRegistrado +
                ", noRegistrado=" + noRegistrado +
                ", eficienciaBienRegistrado=" + eficienciaBienRegistrado +
                '}';
    }
}

