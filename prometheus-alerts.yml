groups:
  - name: crm-application-alerts
    rules:
      # Alerta para peticiones HTTP largas (más de 60 segundos)
      - alert: LongRunningHTTPRequests
        expr: http_server_requests_active_seconds_max{method="POST"} > 60
        for: 30s
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Peticiones HTTP largas detectadas"
          description: "Hay peticiones POST que llevan más de 60 segundos ejecutándose. Valor actual: {{ $value }}s"

      # Alerta para peticiones extremadamente largas (más de 5 minutos)
      - alert: VeryLongRunningHTTPRequests
        expr: http_server_requests_active_seconds_max{method="POST"} > 300
        for: 10s
        labels:
          severity: critical
          service: crm-backend
        annotations:
          summary: "Peticiones HTTP extremadamente largas"
          description: "Hay peticiones POST que llevan más de 5 minutos ejecutándose. Valor actual: {{ $value }}s"

      # Alerta para errores 5xx
      - alert: HighErrorRate5xx
        expr: sum by(uri)(rate(http_server_requests_seconds_count{status=~"5.."}[5m])) > 0
        for: 1m
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Errores 5xx detectados"
          description: "Se están produciendo errores 5xx en {{ $labels.uri }}. Tasa: {{ $value }} errores/segundo"

      # Alerta para tasa alta de errores 5xx
      - alert: CriticalErrorRate5xx
        expr: sum by(uri)(rate(http_server_requests_seconds_count{status=~"5.."}[5m])) > 0.1
        for: 30s
        labels:
          severity: critical
          service: crm-backend
        annotations:
          summary: "Tasa crítica de errores 5xx"
          description: "Tasa muy alta de errores 5xx en {{ $labels.uri }}. Tasa: {{ $value }} errores/segundo"

      # Alerta para crecimiento rápido de memoria (Old Gen)
      - alert: MemoryGrowthOldGen
        expr: rate(jvm_gc_live_data_size_bytes[5m]) > 100000000  # >100 MB/5min
        for: 2m
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Crecimiento rápido de memoria Old Gen"
          description: "La memoria Old Gen está creciendo rápidamente. Tasa: {{ $value | humanize }}B/5min"

      # Alerta para uso alto de memoria Old Gen
      - alert: HighOldGenUsage
        expr: jvm_memory_used_bytes{area="heap",id="G1 Old Gen"} / jvm_memory_max_bytes{area="heap",id="G1 Old Gen"} > 0.8
        for: 5m
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Uso alto de memoria Old Gen"
          description: "El uso de memoria Old Gen está por encima del 80%. Uso actual: {{ $value | humanizePercentage }}"

      # Alerta para uso crítico de memoria Old Gen
      - alert: CriticalOldGenUsage
        expr: jvm_memory_used_bytes{area="heap",id="G1 Old Gen"} / jvm_memory_max_bytes{area="heap",id="G1 Old Gen"} > 0.9
        for: 1m
        labels:
          severity: critical
          service: crm-backend
        annotations:
          summary: "Uso crítico de memoria Old Gen"
          description: "El uso de memoria Old Gen está por encima del 90%. Uso actual: {{ $value | humanizePercentage }}"

      # Alerta para pausas largas de GC
      - alert: LongGCPauses
        expr: rate(jvm_gc_pause_seconds_sum[5m]) / rate(jvm_gc_pause_seconds_count[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Pausas largas de Garbage Collection"
          description: "Las pausas de GC promedio están por encima de 100ms. Promedio: {{ $value }}s"

      # Alerta para churn alto de conexiones Hikari
      - alert: HighHikariConnectionChurn
        expr: rate(hikari_connections_creation_seconds_count[5m]) > 0.5
        for: 3m
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Alto churn de conexiones Hikari"
          description: "Se están creando muchas conexiones nuevas en Hikari. Tasa: {{ $value }} conexiones/segundo"

      # Alerta para conexiones Hikari agotadas
      - alert: HikariConnectionsExhausted
        expr: hikari_connections_active >= hikari_connections_max
        for: 30s
        labels:
          severity: critical
          service: crm-backend
        annotations:
          summary: "Pool de conexiones Hikari agotado"
          description: "Todas las conexiones del pool Hikari están en uso. Activas: {{ $value }}"

      # Alerta para threads altos
      - alert: HighThreadCount
        expr: jvm_threads_live_threads > 500
        for: 5m
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Número alto de threads"
          description: "El número de threads vivos es muy alto. Threads actuales: {{ $value }}"

      # Alerta para uso alto de CPU
      - alert: HighCPUUsage
        expr: system_cpu_usage > 0.8
        for: 5m
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Uso alto de CPU"
          description: "El uso de CPU está por encima del 80%. Uso actual: {{ $value | humanizePercentage }}"

      # Alerta para Redis desconectado
      - alert: RedisConnectionDown
        expr: up{job="redis"} == 0
        for: 30s
        labels:
          severity: critical
          service: crm-backend
        annotations:
          summary: "Conexión a Redis perdida"
          description: "No se puede conectar a Redis"

      # Alerta para comandos Redis lentos
      - alert: SlowRedisCommands
        expr: rate(lettuce_command_completion_seconds_sum[5m]) / rate(lettuce_command_completion_seconds_count[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Comandos Redis lentos"
          description: "Los comandos Redis están tardando más de 100ms en promedio. Tiempo promedio: {{ $value }}s"

  - name: crm-business-alerts
    rules:
      # Alerta para errores en endpoints críticos
      - alert: CriticalEndpointErrors
        expr: sum by(uri)(rate(http_server_requests_seconds_count{status=~"5..",uri=~"/api/clientes/.*|/api/user/.*"}[5m])) > 0
        for: 1m
        labels:
          severity: critical
          service: crm-backend
        annotations:
          summary: "Errores en endpoints críticos"
          description: "Errores 5xx en endpoint crítico {{ $labels.uri }}. Tasa: {{ $value }} errores/segundo"

      # Alerta para latencia alta en endpoints críticos
      - alert: HighLatencyCriticalEndpoints
        expr: histogram_quantile(0.95, sum(rate(http_server_requests_seconds_bucket{uri=~"/api/clientes/.*|/api/user/.*"}[5m])) by (le, uri)) > 5
        for: 3m
        labels:
          severity: warning
          service: crm-backend
        annotations:
          summary: "Latencia alta en endpoints críticos"
          description: "P95 de latencia en {{ $labels.uri }} está por encima de 5s. Latencia: {{ $value }}s"
