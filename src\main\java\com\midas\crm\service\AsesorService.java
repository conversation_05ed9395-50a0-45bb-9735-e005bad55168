package com.midas.crm.service;

import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.cliente.ClienteResidencialDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface AsesorService {

    /**
     * Obtiene todos los usuarios con rol de ASESOR
     * @return Lista de DTOs de asesores
     */
    List<AsesorDTO> getAllAsesores();

    /**
     * Obtiene un asesor por su ID
     * @param id ID del asesor
     * @return DTO del asesor si existe
     */
    Optional<AsesorDTO> getAsesorById(Long id);

    /**
     * Obtiene todos los asesores asignados a un coordinador
     * @param coordinadorId ID del coordinador
     * @return Lista de DTOs de asesores
     */
    List<AsesorDTO> getAsesoresByCoordinadorId(Long coordinadorId);

    /**
     * Convierte una entidad User a un DTO de Asesor
     * @param user Entidad User
     * @return DTO de Asesor
     */
    AsesorDTO convertToDTO(User user);

    /**
     * Obtiene los clientes residenciales asociados a un asesor
     * @param id ID del asesor
     * @return ResponseEntity con la lista de clientes o notFound
     */
    ResponseEntity<List<ClienteResidencialDTO>> getClientesByAsesor(Long id);

    /**
     * Obtiene estadísticas de clientes para un asesor
     * @param id ID del asesor
     * @return ResponseEntity con las estadísticas o notFound
     */
    ResponseEntity<Map<String, Object>> getEstadisticasByAsesor(Long id);

    /**
     * Obtiene los clientes con venta realizada asociados a un asesor
     * @param id ID del asesor
     * @return ResponseEntity con la lista de clientes o notFound
     */
    ResponseEntity<List<ClienteResidencialDTO>> getVentasByAsesor(Long id);

    /**
     * Exporta los clientes de hoy de los asesores asignados a un coordinador
     * @param coordinadorId ID del coordinador
     * @return ResponseEntity con los datos del Excel o noContent
     */
    ResponseEntity<byte[]> exportarClientesHoyDeAsesores(Long coordinadorId);
}