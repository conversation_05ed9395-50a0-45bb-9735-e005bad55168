package com.midas.crm.controller;

import com.midas.crm.entity.Calendar;
import com.midas.crm.entity.DTO.calendar.CalendarDTO;
import com.midas.crm.entity.DTO.calendar.CalendarResponseDTO;
import com.midas.crm.service.CalendarService;
import com.midas.crm.utils.GenericResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("${api.route.calendar}")
@RequiredArgsConstructor
public class CalendarController {

    private final CalendarService calendarService;

    @GetMapping
    public ResponseEntity<GenericResponse<List<Calendar>>> listAll() {
        return ResponseEntity.ok(calendarService.getAll());
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<GenericResponse<List<Calendar>>> getFilterByUser(@PathVariable Long userId) {
        return ResponseEntity.ok(calendarService.getFilterByUser(userId));
    }

    @PostMapping("/filter-dates")
    public ResponseEntity<GenericResponse<List<CalendarResponseDTO>>> getFilterByDates(@RequestBody CalendarDTO filter) {
        return ResponseEntity.ok(calendarService.getFilterByDates(filter));
    }

    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<Calendar>> get(@PathVariable Long id) {
        return ResponseEntity.ok(calendarService.getById(id));
    }

    @PostMapping
    public ResponseEntity<GenericResponse<?>> create(@Valid @RequestBody CalendarDTO calendarDTO) {
        GenericResponse<?> response = calendarService.create(calendarDTO);
        return response.getRpta() == 1 ? ResponseEntity.status(201).body(response) : ResponseEntity.badRequest().body(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<?>> update(@PathVariable Long id, @Valid @RequestBody CalendarDTO calendarDTO) {
        return ResponseEntity.ok(calendarService.update(calendarDTO, id));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<String>> delete(@PathVariable Long id) {
        return ResponseEntity.ok(calendarService.delete(id));
    }

    @PostMapping("/restore/{id}")
    public ResponseEntity<GenericResponse<String>> restore(@PathVariable Long id) {
        return ResponseEntity.ok(calendarService.restore(id));
    }
}