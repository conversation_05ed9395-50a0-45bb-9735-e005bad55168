package com.midas.crm.service;


import com.midas.crm.entity.DTO.asesor.AsesorConClientesDTO;
import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.asesor.AsesorDisponibleDTO;
import com.midas.crm.entity.DTO.asesor.AsignacionAsesorDTO;
import com.midas.crm.entity.DTO.coordinador.CoordinadorDTO;
import com.midas.crm.entity.User;
import com.midas.crm.utils.GenericResponse;
import jakarta.annotation.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface CoordinadorService {
    CoordinadorDTO asignarAsesoresACoordinador(AsignacionAsesorDTO asignacionDTO);

    @Transactional(readOnly = true)
    GenericResponse<Map<String, Object>> obtenerAsesoresPorCoordinadorPaginado(
            Long coordinadorId, int page, int size, String term);

    @Transactional(readOnly = true)
    Page<CoordinadorDTO> obtenerTodosLosCoordinadoresPaginado(
            int page,
            int size,
            @Nullable String term   /* ←  nuevo parámetro opcional de búsqueda */
    );

    CoordinadorDTO obtenerCoordinadorPorId(Long coordinadorId);

    @Transactional(readOnly = true)
    GenericResponse<Map<String,Object>> obtenerAsesoresSinCoordinador(
            int page, int size, String term);

    boolean eliminarAsesorDeCoordinador(Long coordinadorId, Long asesorId);



    GenericResponse<Map<String, Object>> obtenerClientesPorCoordinador(
            Long coordinadorId,
            String dni,
            String nombre,
            String numeroMovil,
            String fecha,
            int page,
            int size);

    @Transactional(readOnly = true)
    GenericResponse<Map<String,Object>> listarCoordinadoresReducido(
            int page, int size, String term);

    @Transactional(readOnly = true)
    List<AsesorDTO> obtenerAsesoresPorCoordinador(Long coordinadorId);
}