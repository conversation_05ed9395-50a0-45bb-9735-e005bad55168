package com.midas.crm.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Evento que se dispara cuando se elimina un anuncio
 */
@Getter
public class AnuncioDeletedEvent extends ApplicationEvent {
    
    private final Long anuncioId;
    
    public AnuncioDeletedEvent(Object source, Long anuncioId) {
        super(source);
        this.anuncioId = anuncioId;
    }
}
