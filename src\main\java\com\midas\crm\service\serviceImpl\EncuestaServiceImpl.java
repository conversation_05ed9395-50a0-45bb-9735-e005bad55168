package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.encuesta.*;
import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.OpcionRespuestaEncuesta;
import com.midas.crm.entity.PreguntaEncuesta;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.EncuestaMapper;
import com.midas.crm.mapper.OpcionRespuestaEncuestaMapper;
import com.midas.crm.mapper.PreguntaEncuestaMapper;
import com.midas.crm.repository.*;
import com.midas.crm.service.EncuestaService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class EncuestaServiceImpl implements EncuestaService {

    private final EncuestaRepository encuestaRepository;
    private final PreguntaEncuestaRepository preguntaEncuestaRepository;
    private final OpcionRespuestaEncuestaRepository opcionRespuestaEncuestaRepository;
    private final RespuestaEncuestaUsuarioRepository respuestaEncuestaUsuarioRepository;
    private final DetalleRespuestaEncuestaUsuarioRepository detalleRespuestaEncuestaUsuarioRepository;
    private final UserRepository userRepository;
    private final SedeRepository sedeRepository;

    @Override
    @Transactional
    public EncuestaDTO createEncuesta(EncuestaCreateDTO dto, Long creadorId) {
        // Obtener el usuario creador
        User creador = userRepository.findById(creadorId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Crear la encuesta
        Encuesta encuesta = EncuestaMapper.toEntity(dto, creador);

        // Asignar sede, coordinador o usuario según el tipo de asignación
        switch (dto.getTipoAsignacion()) {
            case SEDE:
                if (dto.getSedeId() == null) {
                    throw new MidasExceptions(MidasErrorMessage.SEDE_ID_REQUIRED);
                }
                Sede sede = sedeRepository.findById(dto.getSedeId())
                        .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.SEDE_NOT_FOUND));
                encuesta.setSede(sede);
                break;
            case COORDINACION:
                if (dto.getCoordinadorId() == null) {
                    throw new MidasExceptions(MidasErrorMessage.COORDINADOR_ID_REQUIRED);
                }
                User coordinador = userRepository.findById(dto.getCoordinadorId())
                        .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.COORDINADOR_NOT_FOUND));
                encuesta.setCoordinador(coordinador);
                break;
            case PERSONAL:
                // Manejar tanto usuario único como múltiples usuarios
                if (dto.getUsuarioIds() != null && !dto.getUsuarioIds().isEmpty()) {
                    // Múltiples usuarios - validar que existen y guardar como string separado por comas
                    for (Long usuarioId : dto.getUsuarioIds()) {
                        userRepository.findById(usuarioId)
                                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));
                    }

                    // Convertir lista de IDs a string separado por comas
                    String idsString = dto.getUsuarioIds().stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    encuesta.setUsuarioIds(idsString);
                } else if (dto.getUsuarioId() != null) {
                    // Usuario único - usar campo directo
                    User usuario = userRepository.findById(dto.getUsuarioId())
                            .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));
                    encuesta.setUsuario(usuario);
                } else {
                    throw new MidasExceptions(MidasErrorMessage.USUARIO_ID_REQUIRED);
                }
                break;
            default:
                // Para tipo TODOS no se requiere asignación específica
                break;
        }

        // Guardar la encuesta
        encuesta = encuestaRepository.save(encuesta);

        // Crear preguntas si se proporcionaron
        if (dto.getPreguntas() != null && !dto.getPreguntas().isEmpty()) {
            for (int i = 0; i < dto.getPreguntas().size(); i++) {
                PreguntaEncuestaCreateDTO preguntaDTO = dto.getPreguntas().get(i);

                // Asignar orden si no se proporcionó
                if (preguntaDTO.getOrden() == null) {
                    preguntaDTO.setOrden(i + 1);
                }

                // Asignar ID de encuesta
                preguntaDTO.setEncuestaId(encuesta.getId());

                // Crear la pregunta
                PreguntaEncuesta pregunta = PreguntaEncuestaMapper.toEntity(preguntaDTO, encuesta);
                pregunta = preguntaEncuestaRepository.save(pregunta);

                // Crear opciones si se proporcionaron y el tipo de pregunta lo requiere
                if (preguntaDTO.getOpciones() != null && !preguntaDTO.getOpciones().isEmpty() &&
                        (pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.OPCION_MULTIPLE ||
                                pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.SELECCION_MULTIPLE ||
                                pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.ESCALA_LIKERT)) {

                    for (int j = 0; j < preguntaDTO.getOpciones().size(); j++) {
                        OpcionRespuestaEncuestaCreateDTO opcionDTO = preguntaDTO.getOpciones().get(j);

                        // Asignar orden si no se proporcionó
                        if (opcionDTO.getOrden() == null) {
                            opcionDTO.setOrden(j + 1);
                        }

                        // Asignar ID de pregunta
                        opcionDTO.setPreguntaId(pregunta.getId());

                        // Crear la opción
                        opcionRespuestaEncuestaRepository.save(
                                OpcionRespuestaEncuestaMapper.toEntity(opcionDTO, pregunta));
                    }
                }
            }
        }

        // Retornar la encuesta creada con todas sus preguntas y opciones
        return getEncuestaCompleta(encuesta.getId());
    }

    @Override
    @Transactional(readOnly = true)
    public EncuestaDTO getEncuestaById(Long id) {
        Encuesta encuesta = encuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        return EncuestaMapper.toDTO(encuesta);
    }

    @Override
    @Transactional(readOnly = true)
    public EncuestaDTO getEncuestaCompleta(Long id) {
        // Obtener la encuesta
        Encuesta encuesta = encuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        // Obtener las preguntas
        List<PreguntaEncuesta> preguntas = preguntaEncuestaRepository.findByEncuestaOrderByOrdenAsc(encuesta);

        // Obtener estadísticas básicas de la encuesta
        Long totalRespuestas = respuestaEncuestaUsuarioRepository.countByEncuestaId(id);
        Long respuestasCompletadas = respuestaEncuestaUsuarioRepository.countCompletadasByEncuestaId(id);

        // Convertir preguntas a DTOs con sus opciones y estadísticas
        List<PreguntaEncuestaDTO> preguntasDTO = preguntas.stream()
                .map(pregunta -> {
                    // Obtener opciones para cada pregunta
                    List<OpcionRespuestaEncuesta> opciones = opcionRespuestaEncuestaRepository
                            .findByPreguntaOrderByOrdenAsc(pregunta);

                    // Obtener estadísticas de respuestas por opción para esta pregunta
                    List<Map<String, Object>> estadisticasOpciones = detalleRespuestaEncuestaUsuarioRepository
                            .getEstadisticasPorPregunta(pregunta.getId());

                    // Convertir opciones a DTOs con estadísticas
                    List<OpcionRespuestaEncuestaDTO> opcionesDTO = opciones.stream()
                            .map(opcion -> {
                                OpcionRespuestaEncuestaDTO opcionDTO = OpcionRespuestaEncuestaMapper.toDTO(opcion);

                                // Buscar estadísticas para esta opción
                                Long cantidadRespuestas = estadisticasOpciones.stream()
                                        .filter(e -> opcion.getId().equals(e.get("opcionId")))
                                        .map(e -> (Long) e.get("cantidad"))
                                        .findFirst()
                                        .orElse(0L);

                                // Calcular porcentaje de respuestas
                                Double porcentajeRespuestas = totalRespuestas > 0 ?
                                        (cantidadRespuestas.doubleValue() / totalRespuestas.doubleValue()) * 100.0 : 0.0;

                                opcionDTO.setCantidadRespuestas(cantidadRespuestas);
                                opcionDTO.setPorcentajeRespuestas(porcentajeRespuestas);

                                return opcionDTO;
                            })
                            .collect(Collectors.toList());

                    return PreguntaEncuestaMapper.toDTOWithOpciones(pregunta, opcionesDTO);
                })
                .collect(Collectors.toList());

        // Crear DTO completo
        EncuestaDTO dto = EncuestaMapper.toDTOWithPreguntas(encuesta, preguntasDTO);
        dto.setTotalRespuestas(totalRespuestas);
        dto.setRespuestasCompletadas(respuestasCompletadas);

        // Agregar nombres de usuarios múltiples si aplica
        if (encuesta.getUsuarioIds() != null && !encuesta.getUsuarioIds().isEmpty()) {
            dto.setUsuarioNombres(obtenerNombresUsuarios(encuesta.getUsuarioIds()));
        }

        return dto;
    }

    @Override
    @Transactional
    public EncuestaDTO updateEncuesta(Long id, EncuestaUpdateDTO dto) {
        // Obtener la encuesta
        Encuesta encuesta = encuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        // Actualizar campos básicos
        EncuestaMapper.updateEntityFromDTO(encuesta, dto);

        // Actualizar asignaciones según el tipo
        if (dto.getTipoAsignacion() != null) {
            encuesta.setTipoAsignacion(dto.getTipoAsignacion());

            // Limpiar asignaciones previas
            encuesta.setSede(null);
            encuesta.setCoordinador(null);
            encuesta.setUsuario(null);
            encuesta.setUsuarioIds(null); // Limpiar usuarios múltiples también

            // Asignar según el nuevo tipo
            switch (dto.getTipoAsignacion()) {
                case SEDE:
                    if (dto.getSedeId() != null) {
                        Sede sede = sedeRepository.findById(dto.getSedeId())
                                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.SEDE_NOT_FOUND));
                        encuesta.setSede(sede);
                    }
                    break;
                case COORDINACION:
                    if (dto.getCoordinadorId() != null) {
                        User coordinador = userRepository.findById(dto.getCoordinadorId())
                                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.COORDINADOR_NOT_FOUND));
                        encuesta.setCoordinador(coordinador);
                    }
                    break;
                case PERSONAL:
                    // Manejar tanto usuario único como múltiples usuarios en actualización
                    if (dto.getUsuarioIds() != null && !dto.getUsuarioIds().isEmpty()) {
                        // Múltiples usuarios - validar que existen y guardar como string separado por comas
                        for (Long usuarioId : dto.getUsuarioIds()) {
                            userRepository.findById(usuarioId)
                                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));
                        }

                        // Convertir lista de IDs a string separado por comas
                        String idsString = dto.getUsuarioIds().stream()
                                .map(String::valueOf)
                                .collect(Collectors.joining(","));
                        encuesta.setUsuarioIds(idsString);
                        // Limpiar usuario único si se están asignando múltiples
                        encuesta.setUsuario(null);
                    } else if (dto.getUsuarioId() != null) {
                        // Usuario único - usar campo directo
                        User usuario = userRepository.findById(dto.getUsuarioId())
                                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));
                        encuesta.setUsuario(usuario);
                        // Limpiar usuarios múltiples si se está asignando uno único
                        encuesta.setUsuarioIds(null);
                    }
                    break;
                default:
                    // Para tipo TODOS no se requiere asignación específica
                    break;
            }
        }

        // Guardar cambios
        encuesta = encuestaRepository.save(encuesta);

        return EncuestaMapper.toDTO(encuesta);
    }

    @Override
    @Transactional
    public void deleteEncuesta(Long id) {
        // Obtener la encuesta
        Encuesta encuesta = encuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        // Cambiar estado a inactivo
        encuesta.setEstado("I");
        encuestaRepository.save(encuesta);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EncuestaDTO> getAllEncuestas() {
        return encuestaRepository.findAll().stream()
                .map(EncuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<EncuestaDTO> getAllEncuestasActivas() {
        return encuestaRepository.findAllActive().stream()
                .map(EncuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<EncuestaDTO> getEncuestasDisponiblesParaUsuario(Long usuarioId) {
        // Obtener el usuario
        User usuario = userRepository.findById(usuarioId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Obtener sede y coordinador del usuario
        Long sedeId = usuario.getSede() != null ? usuario.getSede().getId() : null;
        Long coordinadorId = usuario.getCoordinador() != null ? usuario.getCoordinador().getId() : null;

        // Obtener encuestas disponibles
        return encuestaRepository.findEncuestasDisponiblesParaUsuario(sedeId, coordinadorId, usuarioId).stream()
                .map(EncuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<EncuestaDTO> getEncuestasByCreador(Long creadorId) {
        // Obtener el usuario creador
        User creador = userRepository.findById(creadorId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        return encuestaRepository.findByCreador(creador).stream()
                .map(EncuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EncuestaDTO> getEncuestasPageable(String search, Pageable pageable) {
        return encuestaRepository.findBySearchTerm(search, pageable)
                .map(EncuestaMapper::toDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getEstadisticasEncuesta(Long encuestaId) {
        // Verificar que la encuesta existe
        if (!encuestaRepository.existsById(encuestaId)) {
            throw new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND);
        }

        // Obtener estadísticas básicas
        Long totalRespuestas = respuestaEncuestaUsuarioRepository.countByEncuestaId(encuestaId);
        Long respuestasCompletadas = respuestaEncuestaUsuarioRepository.countCompletadasByEncuestaId(encuestaId);

        // Crear mapa de estadísticas
        Map<String, Object> estadisticas = new HashMap<>();
        estadisticas.put("totalRespuestas", totalRespuestas);
        estadisticas.put("respuestasCompletadas", respuestasCompletadas);

        return estadisticas;
    }

    /**
     * Obtiene los nombres de usuarios basándose en una cadena de IDs separados por comas
     */
    private List<String> obtenerNombresUsuarios(String usuarioIds) {
        if (usuarioIds == null || usuarioIds.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // Convertir string de IDs a lista de Long
            List<Long> ids = Arrays.stream(usuarioIds.split(","))
                    .map(String::trim)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            // Obtener usuarios por IDs
            List<User> usuarios = userRepository.findAllById(ids);

            // Convertir a nombres completos
            return usuarios.stream()
                    .map(user -> user.getNombre() + " " + user.getApellido())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            // En caso de error, retornar lista vacía
            return new ArrayList<>();
        }
    }
}
