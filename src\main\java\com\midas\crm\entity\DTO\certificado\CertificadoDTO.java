package com.midas.crm.entity.DTO.certificado;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CertificadoDTO {
    private Long id;
    private Long usuarioId;
    private String usuarioNombre;
    private String usuarioApellido;
    private String usuarioDni;
    private String usuarioEmail;
    private Long cursoId;
    private String cursoNombre;
    private String cursoDescripcion;
    private LocalDateTime fechaEmision;
    private Integer horasCurso;
    private String codigoCertificado;
    private Long emitidoPorId;
    private String emitidoPorNombre;
    private String emitidoPorApellido;
    private String observaciones;
    private String certificadoUrl;
    private String estado;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
}
