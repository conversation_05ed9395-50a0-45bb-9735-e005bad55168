package com.midas.crm.service;

import com.midas.crm.entity.DTO.cuestionario.PreguntaCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.PreguntaDTO;
import com.midas.crm.entity.DTO.cuestionario.PreguntaUpdateDTO;

import java.util.List;

public interface PreguntaService {
    PreguntaDTO createPregunta(PreguntaCreateDTO dto);
    List<PreguntaDTO> listPreguntas();
    List<PreguntaDTO> listPreguntasByCuestionarioId(Long cuestionarioId);
    PreguntaDTO getPreguntaById(Long id);
    PreguntaDTO updatePregunta(Long id, PreguntaUpdateDTO dto);
    void deletePregunta(Long id);
}
