package com.midas.crm.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Configuración global de caché con Redis y fallback en memoria
 */
@Configuration
@EnableCaching
@Slf4j
public class CacheConfig {

    /**
     * CacheManager principal que intenta usar Redis y cae a memoria si hay error
     */
    @Bean
    @Primary
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        try {
            redisConnectionFactory.getConnection().close(); // Test de conexión
            log.info("✅ Redis conectado correctamente. Usando RedisCacheManager.");
            return createRedisCacheManager(redisConnectionFactory);
        } catch (Exception e) {
            log.warn("⚠️ No se pudo conectar a Redis. Usando ConcurrentMapCacheManager. Error: {}", e.getMessage());
            return fallbackCacheManager();
        }
    }

    /**
     * Crea el RedisCacheManager con configuraciones específicas por cache
     */
    private RedisCacheManager createRedisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        // Configuración por defecto
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30)) // TTL por defecto aumentado
                .disableCachingNullValues()
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(createJsonRedisSerializer()));

        // Configuraciones específicas por cache
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        // Caches de datos estáticos (TTL largo)
        RedisCacheConfiguration staticDataConfig = defaultConfig.entryTtl(Duration.ofHours(2));
        cacheConfigurations.put("anuncioById", staticDataConfig);
        cacheConfigurations.put("anuncioPage", staticDataConfig);
        cacheConfigurations.put("anuncioPageSede", staticDataConfig);
        cacheConfigurations.put("manualById", staticDataConfig);
        cacheConfigurations.put("manualPage", staticDataConfig);
        cacheConfigurations.put("manualList", staticDataConfig);
        cacheConfigurations.put("cursoById", staticDataConfig);
        cacheConfigurations.put("cursoList", staticDataConfig);
        cacheConfigurations.put("asesorById", staticDataConfig);
        cacheConfigurations.put("asesorList", staticDataConfig);
        cacheConfigurations.put("asesorEstadisticas", staticDataConfig);
        cacheConfigurations.put("calendarById", staticDataConfig);
        cacheConfigurations.put("calendarList", staticDataConfig);
        cacheConfigurations.put("coordinadorById", staticDataConfig);

        // Caches de datos dinámicos (TTL medio)
        RedisCacheConfiguration dynamicDataConfig = defaultConfig.entryTtl(Duration.ofMinutes(15));
        cacheConfigurations.put("clienteById", dynamicDataConfig);
        cacheConfigurations.put("clientePageConUsuario", dynamicDataConfig);
        cacheConfigurations.put("clientesByAsesor", dynamicDataConfig);
        cacheConfigurations.put("clientePageFiltrados", dynamicDataConfig);
        cacheConfigurations.put("clientePageFiltradosPorFechaActual", dynamicDataConfig);

        // Caches de notificaciones (TTL MUY corto para tiempo real)
        RedisCacheConfiguration notificationConfig = defaultConfig.entryTtl(Duration.ofSeconds(1)); // 1 segundo para tiempo real
        cacheConfigurations.put("notificationById", notificationConfig);
        cacheConfigurations.put("notificationPage", notificationConfig);
        cacheConfigurations.put("notificationUnreadCount", notificationConfig);
        cacheConfigurations.put("notificationLatest", notificationConfig);

        return RedisCacheManager.RedisCacheManagerBuilder
                .fromConnectionFactory(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    /**
     * Crea un serializador JSON optimizado para Redis
     */
    private GenericJackson2JsonRedisSerializer createJsonRedisSerializer() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.findAndRegisterModules();
        return new GenericJackson2JsonRedisSerializer(objectMapper);
    }

    /**
     * Cache en memoria local como respaldo (usado si Redis falla)
     */
    private CacheManager fallbackCacheManager() {
        return new ConcurrentMapCacheManager(
                // Anuncios
                "anuncioById", "anuncioPage", "anuncioPageSede",
                // Clientes/Leads
                "clienteById", "clientePageConUsuario", "clientesByAsesor",
                "clientePageFiltrados", "clientePageFiltradosPorFechaActual",
                // Manuales
                "manualById", "manualPage", "manualList",
                // Cursos
                "cursoById", "cursoList",
                // Asesores
                "asesorById", "asesorList", "asesorEstadisticas",
                // Calendarios
                "calendarById", "calendarList",
                // Coordinadores
                "coordinadorById"
        );
    }

    /**
     * Template para Redis, con serialización JSON optimizada
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // Configurar serializadores
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer jsonSerializer = createJsonRedisSerializer();

        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);

        // Habilitar transacciones
        template.setEnableTransactionSupport(true);
        template.afterPropertiesSet();

        return template;
    }
}
