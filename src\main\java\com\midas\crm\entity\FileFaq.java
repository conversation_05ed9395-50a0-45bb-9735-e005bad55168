package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "file_faqs")
@Data
public class FileFaq {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    private String type;

    private Long size;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String url;
}