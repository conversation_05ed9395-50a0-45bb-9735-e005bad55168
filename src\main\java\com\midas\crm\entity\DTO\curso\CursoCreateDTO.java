package com.midas.crm.entity.DTO.curso;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CursoCreateDTO {
    @NotBlank(message = "El nombre del curso es obligatorio")
    private String nombre;
    private String descripcion;
    private String videoUrl;
    private LocalDateTime fechaInicio;
    private LocalDateTime fechaFin;
    private Long usuarioId; // 🔥 ahora ya no tiene @NotNull
}
