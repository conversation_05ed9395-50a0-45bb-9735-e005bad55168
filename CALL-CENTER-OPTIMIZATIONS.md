# 🏢 Optimizaciones para Call Center - 50 Usuarios Concurrentes

## 📊 Análisis de tu Situación Actual

### ✅ **Lo que está BIEN en tu servidor:**
- **Latencia promedio**: 14.1ms (excelente)
- **Latencia máxima**: 193ms (aceptable)
- **Configuración Hikari**: Ya optimizada para concurrencia
- **Métricas**: Bien configuradas con Micrometer
- **Cache Redis**: Implementado y funcionando

### 🔍 **El problema real: Red del Call Center**
Con 50 usuarios compartiendo la misma conexión de internet, el cuello de botella está en:
1. **Ancho de banda compartido**
2. **Latencia de red local**
3. **Congestión en horas pico**
4. **Múl<PERSON>les requests simultáneos**

## 🚀 Soluciones Inmediatas

### 1. **Optimización de Requests Frontend**
```javascript
// Implementar debouncing en búsquedas
const debouncedSearch = debounce((query) => {
    searchLeads(query);
}, 300); // Esperar 300ms antes de buscar

// Batch requests para múltiples operaciones
const batchRequests = async (operations) => {
    return Promise.all(operations.map(op => 
        fetch(op.url, { ...op.options, keepalive: true })
    ));
};

// Cache local para datos frecuentes
const localCache = new Map();
const getCachedData = (key, fetchFn, ttl = 300000) => { // 5 min TTL
    if (localCache.has(key)) {
        const { data, timestamp } = localCache.get(key);
        if (Date.now() - timestamp < ttl) return Promise.resolve(data);
    }
    return fetchFn().then(data => {
        localCache.set(key, { data, timestamp: Date.now() });
        return data;
    });
};
```

### 2. **Configuración de Red Optimizada**

#### A. **Configurar QoS en el Router del Call Center**
```bash
# Priorizar tráfico HTTP/HTTPS hacia tu servidor
# Configurar en el router principal:
# - Alta prioridad: Puerto 9039 (tu backend)
# - Media prioridad: Puertos 80, 443 (web general)
# - Baja prioridad: Streaming, descargas
```

#### B. **Optimizar Conexiones TCP**
```properties
# Agregar a application-prod.properties
# Optimizaciones para múltiples usuarios concurrentes
server.tomcat.max-connections=200
server.tomcat.accept-count=100
server.tomcat.max-threads=200
server.tomcat.min-spare-threads=25

# Optimizar keep-alive para call center
server.tomcat.keep-alive-timeout=60000
server.tomcat.max-keep-alive-requests=100
server.tomcat.connection-timeout=20000

# Compresión para reducir ancho de banda
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain,text/css,text/javascript,application/javascript
server.compression.min-response-size=1024
```

### 3. **Optimización de Base de Datos para Concurrencia**
```properties
# Aumentar pool para 50 usuarios concurrentes
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.minimum-idle=15
spring.datasource.hikari.connection-timeout=30000

# Optimizar queries para call center
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
```

### 4. **Cache Agresivo para Call Center**
```properties
# Cache más agresivo para datos frecuentes
spring.cache.redis.time-to-live=600000
spring.cache.redis.cache-null-values=false

# Cache específico para call center
cache.call-center.asesores.ttl=1800000
cache.call-center.coordinadores.ttl=1800000
cache.call-center.leads-recientes.ttl=300000
```

## 🔧 Implementaciones Específicas

### 1. **Endpoint de Health Check para Call Center**
```java
@GetMapping("/api/call-center/health")
public ResponseEntity<Map<String, Object>> callCenterHealth() {
    Map<String, Object> health = new HashMap<>();
    health.put("activeConnections", getActiveConnections());
    health.put("avgResponseTime", getAvgResponseTime());
    health.put("queueSize", getQueueSize());
    health.put("cacheHitRate", getCacheHitRate());
    health.put("recommendedAction", getRecommendedAction());
    return ResponseEntity.ok(health);
}
```

### 2. **Monitoreo en Tiempo Real**
```bash
# Script para monitorear el call center
#!/bin/bash
while true; do
    echo "=== $(date) ==="
    echo "Conexiones activas:"
    curl -s http://localhost:9039/api/call-center/health | jq '.activeConnections'
    
    echo "Tiempo de respuesta promedio:"
    curl -s http://localhost:9039/actuator/prometheus | grep "http_server_requests_seconds_sum" | tail -5
    
    echo "Memoria utilizada:"
    curl -s http://localhost:9039/actuator/prometheus | grep "jvm_memory_used_bytes" | grep "heap"
    
    sleep 30
done
```

## 📈 Métricas Específicas para Call Center

### Dashboard Grafana Personalizado:

#### Panel 1: **Usuarios Activos Simultáneos**
```promql
# Conexiones HTTP activas
sum(http_server_requests_active_seconds_count)

# Threshold: Verde <30, Amarillo 30-45, Rojo >45
```

#### Panel 2: **Latencia por Hora del Día**
```promql
# Latencia promedio por hora
avg_over_time(
  rate(http_server_requests_seconds_sum[5m]) / 
  rate(http_server_requests_seconds_count[5m])
)[1h:]
```

#### Panel 3: **Throughput Call Center**
```promql
# Requests por minuto (ideal para call center)
rate(http_server_requests_seconds_count[1m]) * 60
```

#### Panel 4: **Eficiencia de Cache**
```promql
# Hit rate del cache Redis
rate(lettuce_command_completion_seconds_count{command="GET"}[5m]) /
rate(lettuce_command_completion_seconds_count[5m])
```

## 🚨 Alertas Críticas para Call Center

### 1. **Demasiados Usuarios Simultáneos**
```yaml
alert: CallCenterOverload
expr: sum(http_server_requests_active_seconds_count) > 45
for: 2m
labels:
  severity: warning
annotations:
  summary: "Call center sobrecargado"
  description: "{{ $value }} usuarios activos simultáneos (límite: 45)"
```

### 2. **Latencia Alta en Horas Pico**
```yaml
alert: PeakHourLatency
expr: rate(http_server_requests_seconds_sum[5m]) / rate(http_server_requests_seconds_count[5m]) > 0.5
for: 3m
labels:
  severity: critical
annotations:
  summary: "Latencia alta en horas pico"
  description: "Latencia promedio: {{ $value }}s en call center"
```

## 🎯 Recomendaciones de Red

### 1. **Configuración de Router/Switch**
- **Bandwidth allocation**: 70% para CRM, 30% para otros usos
- **QoS rules**: Prioridad alta para puerto 9039
- **Connection limits**: Máximo 5 conexiones simultáneas por IP hacia el CRM

### 2. **Horarios de Mantenimiento**
- **Cache warming**: 7:00 AM (antes del inicio laboral)
- **Database maintenance**: 12:00 AM - 2:00 AM
- **Log rotation**: 6:00 AM

### 3. **Monitoreo Proactivo**
- **Alertas por WhatsApp/Telegram** cuando latencia > 500ms
- **Dashboard en TV** mostrando métricas en tiempo real
- **Reportes diarios** de rendimiento por email

---

**🎯 Próximos Pasos Inmediatos:**
1. Implementar las configuraciones de Tomcat
2. Configurar QoS en el router del call center
3. Crear dashboard específico en Grafana
4. Establecer alertas proactivas
5. Capacitar al equipo en interpretación de métricas
