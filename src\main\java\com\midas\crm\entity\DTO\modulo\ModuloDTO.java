package com.midas.crm.entity.DTO.modulo;

import com.midas.crm.entity.DTO.leccion.LeccionDTO;
import com.midas.crm.entity.DTO.seccion.SeccionDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModuloDTO {
    private Long id;
    private String titulo;
    private String descripcion;
    private Integer orden;
    private Long cursoId;
    private String cursoNombre;
    private String estado;
    private LocalDateTime fechaCreacion;
    private List<SeccionDTO> secciones;
}
