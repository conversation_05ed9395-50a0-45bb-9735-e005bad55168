package com.midas.crm.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Controlador para servir la interfaz de Swagger UI directamente
 */
@Controller
public class SwaggerController {

    /**
     * Sirve la interfaz de Swagger UI directamente
     * @return HTML de la interfaz de Swagger UI
     */
    @GetMapping(value = {"/swagger-ui", "/swagger-ui.html"}, produces = "text/html")
    @ResponseBody
    public String swaggerUI() {
        return """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <title>API de CRM Midas - Documentación</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                    }
                    h1 {
                        color: #333;
                        border-bottom: 1px solid #ddd;
                        padding-bottom: 10px;
                    }
                    h2 {
                        color: #0066cc;
                        margin-top: 30px;
                    }
                    .endpoint {
                        background-color: #f5f5f5;
                        padding: 15px;
                        border-radius: 5px;
                        margin-bottom: 20px;
                    }
                    .method {
                        display: inline-block;
                        padding: 5px 10px;
                        border-radius: 3px;
                        color: white;
                        font-weight: bold;
                        margin-right: 10px;
                    }
                    .get { background-color: #61affe; }
                    .post { background-color: #49cc90; }
                    .put { background-color: #fca130; }
                    .delete { background-color: #f93e3e; }
                    .path {
                        font-family: monospace;
                        font-size: 16px;
                    }
                    .description {
                        margin-top: 10px;
                        color: #555;
                    }
                    .footer {
                        margin-top: 50px;
                        color: #777;
                        font-size: 14px;
                        border-top: 1px solid #ddd;
                        padding-top: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>API de CRM Midas</h1>
                <p>API para el sistema CRM de Midas Solution Group</p>

                <h2>Autenticación</h2>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <span class="path">/api/authentication/sign-in</span>
                    <div class="description">Iniciar sesión en el sistema</div>
                </div>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <span class="path">/api/authentication/sign-up</span>
                    <div class="description">Registrar un nuevo usuario</div>
                </div>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <span class="path">/api/authentication/refresh-token</span>
                    <div class="description">Refrescar el token de autenticación</div>
                </div>

                <h2>Usuarios</h2>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="path">/api/user/listar</span>
                    <div class="description">Listar todos los usuarios</div>
                </div>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <span class="path">/api/user/registrar</span>
                    <div class="description">Registrar un nuevo usuario</div>
                </div>

                <h2>Clientes</h2>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="path">/api/clientes/con-usuario</span>
                    <div class="description">Listar clientes con usuario asignado</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="path">/api/clientes/{id}</span>
                    <div class="description">Obtener un cliente por su ID</div>
                </div>

                <h2>Anuncios</h2>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="path">/api/anuncios</span>
                    <div class="description">Listar todos los anuncios</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="path">/api/anuncios/recientes</span>
                    <div class="description">Obtener anuncios recientes</div>
                </div>

                <h2>Manuales</h2>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="path">/api/manuales/all</span>
                    <div class="description">Listar todos los manuales</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="path">/api/manuales/{id}</span>
                    <div class="description">Obtener un manual por su ID</div>
                </div>

                <div class="footer">
                    <p>Midas Solution Group &copy; 2025</p>
                    <p>Contacto: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>URL: <a href="https://www.midassolutiongroup.com">https://www.midassolutiongroup.com</a></p>
                </div>
            </body>
            </html>
        """;
    }
}
