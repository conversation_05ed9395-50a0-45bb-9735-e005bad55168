package com.midas.crm.mapper;

import com.midas.crm.entity.Anuncio;
import com.midas.crm.entity.DTO.anuncio.AnuncioDTO;
import com.midas.crm.entity.DTO.anuncio.AnuncioListDTO;
import com.midas.crm.entity.DTO.anuncio.AnuncioUpdateResponseDTO;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;

import java.time.LocalDateTime;
import java.util.Optional;

public class AnuncioMapper {

    public static Anuncio toEntity(AnuncioDTO dto, User usuario, Sede sede) {
        return Anuncio.builder()
                .titulo(dto.getTitulo())
                .descripcion(dto.getDescripcion())
                .imagenUrl(dto.getImagenUrl())
                .categoria(dto.getCategoria())
                .fechaPublicacion(LocalDateTime.now())
                .fechaInicio(dto.getFechaInicio())
                .fechaFin(dto.getFechaFin())
                .orden(dto.getOrden() != null ? dto.getOrden() : 0)
                .estado(dto.getEstado() != null ? dto.getEstado() : "ACTIVO")
                .usuario(usuario)
                .sede(sede)
                .build();
    }

    public static AnuncioListDTO toListDTO(Anuncio anuncio) {
        return AnuncioListDTO.builder()
                .id(anuncio.getId())
                .titulo(anuncio.getTitulo())
                .descripcion(anuncio.getDescripcion())
                .imagenUrl(anuncio.getImagenUrl())
                .categoria(anuncio.getCategoria())
                .fechaPublicacion(anuncio.getFechaPublicacion().toString())
                .fechaInicio(anuncio.getFechaInicio() != null ? anuncio.getFechaInicio().toString() : null)
                .fechaFin(anuncio.getFechaFin() != null ? anuncio.getFechaFin().toString() : null)
                .orden(anuncio.getOrden())
                .estado(anuncio.getEstado())
                .nombreUsuario(anuncio.getUsuario().getNombre())
                .sedeId(anuncio.getSede() != null ? anuncio.getSede().getId() : null)
                .nombreSede(anuncio.getSede() != null ? anuncio.getSede().getNombre() : null)
                .build();
    }

    public static AnuncioUpdateResponseDTO toUpdateResponseDTO(Anuncio anuncio) {
        AnuncioUpdateResponseDTO dto = new AnuncioUpdateResponseDTO();
        dto.setId(anuncio.getId());
        dto.setTitulo(anuncio.getTitulo());
        dto.setDescripcion(anuncio.getDescripcion());
        dto.setImagenUrl(anuncio.getImagenUrl());
        dto.setCategoria(anuncio.getCategoria());
        dto.setFechaPublicacion(anuncio.getFechaPublicacion());
        dto.setFechaInicio(anuncio.getFechaInicio());
        dto.setFechaFin(anuncio.getFechaFin());
        dto.setOrden(anuncio.getOrden());
        dto.setEstado(anuncio.getEstado());
        dto.setUsuarioId(anuncio.getUsuario() != null ? anuncio.getUsuario().getId() : null);

        // Incluir el ID de la sede si existe
        dto.setSedeId(anuncio.getSede() != null ? anuncio.getSede().getId() : null);

        // Opcionalmente, incluir el nombre de la sede
        dto.setNombreSede(anuncio.getSede() != null ? anuncio.getSede().getNombre() : null);

        return dto;
    }
}
