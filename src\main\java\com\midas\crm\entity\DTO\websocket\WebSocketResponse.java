package com.midas.crm.entity.DTO.websocket;

import com.midas.crm.utils.GenericResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO para respuestas WebSocket
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WebSocketResponse<T> {
    private String type;
    private GenericResponse<T> data;
    
    /**
     * Crea una respuesta WebSocket con un tipo y datos
     * @param type Tipo de respuesta
     * @param status Código de estado (1 = éxito, 0 = error)
     * @param message Mensaje descriptivo
     * @param data Datos de la respuesta
     * @return Respuesta WebSocket
     */
    public static <T> WebSocketResponse<T> create(String type, int status, String message, T data) {
        return new WebSocketResponse<>(
            type,
            new GenericResponse<>(status, message, data)
        );
    }
    
    /**
     * Crea una respuesta WebSocket de éxito
     * @param type Tipo de respuesta
     * @param message Mensaje descriptivo
     * @param data Datos de la respuesta
     * @return Respuesta WebSocket
     */
    public static <T> WebSocketResponse<T> success(String type, String message, T data) {
        return create(type, 1, message, data);
    }
    
    /**
     * Crea una respuesta WebSocket de error
     * @param type Tipo de respuesta
     * @param message Mensaje descriptivo
     * @return Respuesta WebSocket
     */
    public static <T> WebSocketResponse<T> error(String type, String message) {
        return create(type, 0, message, null);
    }
}
