package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Cuestionario;
import com.midas.crm.entity.DetalleRespuestaUsuario;
import com.midas.crm.entity.DTO.cuestionario.DetalleRespuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUsuarioDTO;
import com.midas.crm.entity.Pregunta;
import com.midas.crm.entity.Respuesta;
import com.midas.crm.entity.RespuestaUsuario;
import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.DetalleRespuestaUsuarioMapper;
import com.midas.crm.mapper.RespuestaUsuarioMapper;
import com.midas.crm.repository.CuestionarioRepository;
import com.midas.crm.repository.DetalleRespuestaUsuarioRepository;
import com.midas.crm.repository.PreguntaRepository;
import com.midas.crm.repository.RespuestaRepository;
import com.midas.crm.repository.RespuestaUsuarioRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.RespuestaUsuarioService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RespuestaUsuarioServiceImpl implements RespuestaUsuarioService {

    private final RespuestaUsuarioRepository respuestaUsuarioRepository;
    private final DetalleRespuestaUsuarioRepository detalleRespuestaUsuarioRepository;
    private final CuestionarioRepository cuestionarioRepository;
    private final PreguntaRepository preguntaRepository;
    private final RespuestaRepository respuestaRepository;
    private final UserRepository userRepository;

    @Override
    @Transactional
    public RespuestaUsuarioDTO iniciarCuestionario(RespuestaUsuarioCreateDTO dto) {
        // Obtener el usuario
        User usuario = userRepository.findById(dto.getUsuarioId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Obtener el cuestionario
        Cuestionario cuestionario = cuestionarioRepository.findById(dto.getCuestionarioId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CUESTIONARIO_NOT_FOUND));

        // Verificar si el usuario puede intentar el cuestionario
        if (!puedeIntentarCuestionario(dto.getUsuarioId(), dto.getCuestionarioId())) {
            throw new MidasExceptions(MidasErrorMessage.CUESTIONARIO_MAX_INTENTOS);
        }

        // Obtener el número de intento
        int numeroIntento = respuestaUsuarioRepository.countByUsuarioIdAndCuestionarioId(dto.getUsuarioId(), dto.getCuestionarioId()) + 1;

        // Crear la respuesta del usuario
        RespuestaUsuario respuestaUsuario = RespuestaUsuarioMapper.toEntity(dto, usuario, cuestionario, numeroIntento);
        
        return RespuestaUsuarioMapper.toDTO(respuestaUsuarioRepository.save(respuestaUsuario));
    }

    @Override
    @Transactional
    public RespuestaUsuarioDTO responderPregunta(Long respuestaUsuarioId, DetalleRespuestaUsuarioCreateDTO dto) {
        // Obtener la respuesta del usuario
        RespuestaUsuario respuestaUsuario = respuestaUsuarioRepository.findById(respuestaUsuarioId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.RESPUESTA_USUARIO_NOT_FOUND));

        // Verificar que el cuestionario no esté completado
        if (respuestaUsuario.getCompletado()) {
            throw new MidasExceptions(MidasErrorMessage.CUESTIONARIO_YA_COMPLETADO);
        }

        // Obtener la pregunta
        Pregunta pregunta = preguntaRepository.findById(dto.getPreguntaId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_NOT_FOUND));

        // Obtener la respuesta (puede ser null para preguntas de texto libre)
        Respuesta respuesta = null;
        if (dto.getRespuestaId() != null) {
            respuesta = respuestaRepository.findById(dto.getRespuestaId())
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.RESPUESTA_NOT_FOUND));
        }

        // Verificar si ya existe una respuesta para esta pregunta
        detalleRespuestaUsuarioRepository.findByRespuestaUsuarioIdAndPreguntaId(respuestaUsuarioId, dto.getPreguntaId())
                .ifPresent(detalle -> {
                    throw new MidasExceptions(MidasErrorMessage.PREGUNTA_YA_RESPONDIDA);
                });

        // Crear el detalle de la respuesta
        DetalleRespuestaUsuario detalle = DetalleRespuestaUsuarioMapper.toEntity(dto, respuestaUsuario, pregunta, respuesta);
        detalleRespuestaUsuarioRepository.save(detalle);

        // Recargar la respuesta del usuario con sus detalles
        return RespuestaUsuarioMapper.toDTO(respuestaUsuarioRepository.findById(respuestaUsuarioId).orElse(respuestaUsuario));
    }

    @Override
    @Transactional
    public RespuestaUsuarioDTO finalizarCuestionario(Long respuestaUsuarioId) {
        // Obtener la respuesta del usuario
        RespuestaUsuario respuestaUsuario = respuestaUsuarioRepository.findById(respuestaUsuarioId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.RESPUESTA_USUARIO_NOT_FOUND));

        // Verificar que el cuestionario no esté completado
        if (respuestaUsuario.getCompletado()) {
            throw new MidasExceptions(MidasErrorMessage.CUESTIONARIO_YA_COMPLETADO);
        }

        // Obtener el cuestionario
        Cuestionario cuestionario = respuestaUsuario.getCuestionario();

        // Calcular el puntaje obtenido
        int totalPreguntas = preguntaRepository.countByCuestionarioId(cuestionario.getId());
        int respuestasCorrectas = detalleRespuestaUsuarioRepository.countByRespuestaUsuarioIdAndEsCorrectaTrue(respuestaUsuarioId);
        
        // Calcular el porcentaje de aprobación
        int porcentajeAprobacion = totalPreguntas > 0 ? (respuestasCorrectas * 100) / totalPreguntas : 0;
        
        // Determinar si el cuestionario fue aprobado
        boolean aprobado = porcentajeAprobacion >= cuestionario.getPuntajeAprobacion();

        // Actualizar la respuesta del usuario
        respuestaUsuario.setFechaFin(LocalDateTime.now());
        respuestaUsuario.setPuntajeObtenido(respuestasCorrectas);
        respuestaUsuario.setPorcentajeAprobacion(porcentajeAprobacion);
        respuestaUsuario.setCompletado(true);
        respuestaUsuario.setAprobado(aprobado);

        return RespuestaUsuarioMapper.toDTO(respuestaUsuarioRepository.save(respuestaUsuario));
    }

    @Override
    @Transactional(readOnly = true)
    public RespuestaUsuarioDTO getRespuestaUsuarioById(Long id) {
        return respuestaUsuarioRepository.findById(id)
                .map(RespuestaUsuarioMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.RESPUESTA_USUARIO_NOT_FOUND));
    }

    @Override
    @Transactional(readOnly = true)
    public List<RespuestaUsuarioDTO> getRespuestaUsuarioByUsuarioAndCuestionario(Long usuarioId, Long cuestionarioId) {
        return respuestaUsuarioRepository.findByUsuarioIdAndCuestionarioIdOrderByFechaInicioDesc(usuarioId, cuestionarioId).stream()
                .map(RespuestaUsuarioMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public RespuestaUsuarioDTO getMejorIntentoByUsuarioAndCuestionario(Long usuarioId, Long cuestionarioId) {
        List<RespuestaUsuario> intentos = respuestaUsuarioRepository.findMejorIntentoByCuestionarioAndUsuario(usuarioId, cuestionarioId);
        if (intentos.isEmpty()) {
            throw new MidasExceptions(MidasErrorMessage.RESPUESTA_USUARIO_NOT_FOUND);
        }
        return RespuestaUsuarioMapper.toDTO(intentos.get(0));
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getResumenCuestionariosByCursoAndUsuario(Long cursoId, Long usuarioId) {
        // Verificar que el usuario existe
        if (!userRepository.existsById(usuarioId)) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND);
        }

        // Obtener el número total de cuestionarios del curso
        Long totalCuestionarios = respuestaUsuarioRepository.countTotalCuestionariosByCurso(cursoId);
        
        // Obtener el número de cuestionarios aprobados por el usuario
        Long cuestionariosAprobados = respuestaUsuarioRepository.countCuestionariosAprobadosByCursoAndUsuario(cursoId, usuarioId);
        
        // Calcular el porcentaje de progreso
        int porcentajeProgreso = totalCuestionarios > 0 ? (int) ((cuestionariosAprobados * 100) / totalCuestionarios) : 0;
        
        // Crear el mapa de respuesta
        Map<String, Object> resumen = new HashMap<>();
        resumen.put("cursoId", cursoId);
        resumen.put("usuarioId", usuarioId);
        resumen.put("totalCuestionarios", totalCuestionarios);
        resumen.put("cuestionariosAprobados", cuestionariosAprobados);
        resumen.put("porcentajeProgreso", porcentajeProgreso);
        
        return resumen;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean puedeIntentarCuestionario(Long usuarioId, Long cuestionarioId) {
        // Obtener el cuestionario
        Cuestionario cuestionario = cuestionarioRepository.findById(cuestionarioId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CUESTIONARIO_NOT_FOUND));

        // Si no hay límite de intentos, siempre puede intentar
        if (cuestionario.getIntentosMaximos() == null) {
            return true;
        }

        // Obtener el número de intentos realizados
        int intentosRealizados = respuestaUsuarioRepository.countByUsuarioIdAndCuestionarioId(usuarioId, cuestionarioId);

        // Verificar si ha alcanzado el límite de intentos
        return intentosRealizados < cuestionario.getIntentosMaximos();
    }
}
