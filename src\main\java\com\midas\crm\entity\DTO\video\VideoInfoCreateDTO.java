package com.midas.crm.entity.DTO.video;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoInfoCreateDTO {
    @NotNull(message = "El ID de la lección es obligatorio")
    private Long leccionId;
    
    private String formato;
    private String resolucion;
    private Long tamanoBytes;
    private Integer duracionSegundos;
    private LocalDateTime fechaSubida;
}
