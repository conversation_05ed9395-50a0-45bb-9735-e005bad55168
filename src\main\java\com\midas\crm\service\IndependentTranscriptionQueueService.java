package com.midas.crm.service;

import com.midas.crm.entity.DTO.queue.IndependentTranscriptionQueueMessage;
import com.midas.crm.entity.DTO.GoogleDriveFileDTO;

import java.util.List;
import java.util.Map;

/**
 * Servicio para gestionar las colas de transcripciones independientes.
 * Maneja el procesamiento de múltiples archivos de audio de forma concurrente.
 */
public interface IndependentTranscriptionQueueService {

    /**
     * Procesa múltiples archivos de Google Drive para transcripción
     *
     * @param googleDriveFiles Lista de archivos de Google Drive seleccionados
     * @param userId ID del usuario que inicia el procesamiento
     * @param userName Nombre del usuario
     * @param whisperModel Modelo de Whisper a usar (small, base, large)
     * @param device Dispositivo de procesamiento (cpu, gpu)
     * @param targetLanguage Idioma objetivo (es, en, etc.)
     * @param tags Etiquetas opcionales
     * @param notes Notas opcionales
     * @return ID del lote creado
     */
    String processMultipleFiles(
            List<GoogleDriveFileDTO> googleDriveFiles,
            String userId,
            String userName,
            String whisperModel,
            String device,
            String targetLanguage,
            List<String> tags,
            String notes
    );

    /**
     * Procesa un archivo individual (para archivos únicos)
     *
     * @param googleDriveFile Archivo de Google Drive
     * @param userId ID del usuario
     * @param userName Nombre del usuario
     * @param whisperModel Modelo de Whisper
     * @param device Dispositivo de procesamiento
     * @param targetLanguage Idioma objetivo
     * @param tags Etiquetas opcionales
     * @param notes Notas opcionales
     * @return ID del archivo procesado
     */
    String processSingleFile(
            GoogleDriveFileDTO googleDriveFile,
            String userId,
            String userName,
            String whisperModel,
            String device,
            String targetLanguage,
            List<String> tags,
            String notes
    );

    /**
     * Obtiene el estado de un lote de procesamiento
     *
     * @param batchId ID del lote
     * @return Estado del lote con información detallada
     */
    IndependentTranscriptionQueueMessage getBatchStatus(String batchId);

    /**
     * Obtiene estadísticas de la cola de transcripciones independientes
     *
     * @return Estadísticas de la cola
     */
    Map<String, Object> getQueueStatistics();

    /**
     * Cancela un lote de procesamiento
     *
     * @param batchId ID del lote a cancelar
     * @return true si se canceló exitosamente
     */
    boolean cancelBatch(String batchId);

    /**
     * Reintenta el procesamiento de archivos fallidos en un lote
     *
     * @param batchId ID del lote
     * @return true si se reinició exitosamente
     */
    boolean retryFailedFiles(String batchId);

    /**
     * Obtiene los lotes de un usuario
     *
     * @param userId ID del usuario
     * @param page Página (0-based)
     * @param size Tamaño de página
     * @return Lista de lotes del usuario
     */
    List<IndependentTranscriptionQueueMessage> getUserBatches(String userId, int page, int size);

    /**
     * Obtiene todos los lotes activos
     *
     * @param page Página (0-based)
     * @param size Tamaño de página
     * @return Lista de lotes activos
     */
    List<IndependentTranscriptionQueueMessage> getActiveBatches(int page, int size);

    /**
     * Pausa el procesamiento de la cola
     */
    void pauseProcessing();

    /**
     * Reanuda el procesamiento de la cola
     */
    void resumeProcessing();

    /**
     * Verifica si el procesamiento está activo
     *
     * @return true si está activo
     */
    boolean isProcessingActive();

    /**
     * Limpia lotes completados antiguos
     *
     * @param daysOld Días de antigüedad para limpiar
     * @return Número de lotes limpiados
     */
    int cleanupOldBatches(int daysOld);
}
