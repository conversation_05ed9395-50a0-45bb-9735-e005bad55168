package com.midas.crm.service;

import com.midas.crm.entity.Role;
import com.midas.crm.service.AnuncioService;
import com.midas.crm.service.AsesorService;
import com.midas.crm.service.CalendarService;
import com.midas.crm.service.CoordinadorService;
import com.midas.crm.service.CursoService;
import com.midas.crm.service.ManualService;
import com.midas.crm.service.serviceImpl.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * Servicio para pre-cargar datos importantes en Redis cache
 * Mejora el rendimiento al tener datos críticos ya cacheados
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CacheWarmupService {

    private final AnuncioService anuncioService;
    private final AsesorService asesorService;
    private final CalendarService calendarService;
    private final CoordinadorService coordinadorService;
    private final CursoService cursoService;
    private final ManualService manualService;
    private final NotificationService notificationService;

    /**
     * Ejecuta el warmup del cache cuando la aplicación está lista
     */
    @EventListener(ApplicationReadyEvent.class)
    @Async
    public void warmupCacheOnStartup() {
        try {
            warmupStaticData();
        } catch (Exception e) {
        }
    }

    /**
     * Warmup programado cada 6 horas para mantener el cache caliente
     */
    @Scheduled(fixedRate = 21600000) // 6 horas en milisegundos
    @Async
    public void scheduledCacheWarmup() {
        try {
            warmupStaticData();
        } catch (Exception e) {
        }
    }

    /**
     * Pre-carga datos estáticos que no cambian frecuentemente
     */
    private void warmupStaticData() {

        // Warmup de anuncios
        warmupAnuncios();
        
        // Warmup de asesores
        warmupAsesores();
        
        // Warmup de cursos
        warmupCursos();
        
        // Warmup de manuales
        warmupManuales();

        // Warmup de calendarios
        warmupCalendarios();

    }

    /**
     * Pre-carga anuncios en cache
     */
    private void warmupAnuncios() {
        try {
            
            // Cargar anuncios activos y vigentes (página 1)
            anuncioService.listarAnunciosActivosYVigentes(0, 20);
            
            // Cargar anuncios más recientes (página 1)
            anuncioService.listarMasRecientesPaginado(0, 20);
            
            // Cargar todos los anuncios (página 1)
            anuncioService.listarTodosLosAnuncios(0, 20);
            
        } catch (Exception e) {
        }
    }

    /**
     * Pre-carga asesores en cache
     */
    private void warmupAsesores() {
        try {            
            // Cargar lista de todos los asesores
            asesorService.getAllAsesores();
            
        } catch (Exception e) {
        }
    }

    /**
     * Pre-carga cursos en cache
     */
    private void warmupCursos() {
        try {            
            // Cargar lista de todos los cursos
            cursoService.listCursos();
                    } catch (Exception e) {
        }
    }

    /**
     * Pre-carga manuales en cache
     */
    private void warmupManuales() {
        try {            
            // Cargar manuales por diferentes roles
            manualService.getAllByRole(Role.ASESOR);
            manualService.getAllByRole(Role.ADMIN);
            manualService.getAllByRole(Role.COORDINADOR);
            
        } catch (Exception e) {
        }
    }

    /**
     * Limpia y recarga el cache completo (uso manual)
     */
    public void refreshAllCache() {
        try {
            // Aquí podrías agregar lógica para limpiar cache específico si es necesario
            warmupStaticData();
        } catch (Exception e) {
        }
    }

    /**
     * Pre-carga calendarios en cache
     */
    private void warmupCalendarios() {
        try {
            // Cargar todas las agendas
            calendarService.getAll();

        } catch (Exception e) {
        }
    }



    /**
     * Obtiene estadísticas del cache (para monitoreo)
     */
    public void logCacheStats() {
        log.info("📈 Estadísticas del cache:");
        log.info("- Cache warmup service activo");
        log.info("- Último warmup: programado cada 6 horas");
        log.info("- Datos pre-cargados: anuncios, asesores, cursos, manuales, calendarios, coordinadores");
    }
}
