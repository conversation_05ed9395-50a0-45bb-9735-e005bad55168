package com.midas.crm.controller;

import com.midas.crm.entity.DTO.certificado.CertificadoCreateDTO;
import com.midas.crm.entity.DTO.certificado.CertificadoDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.service.CertificadoService;
import com.midas.crm.service.UserService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.MidasErrorMessage;
import com.midas.crm.utils.ResponseBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/certificados")
@CrossOrigin(origins = "*")
public class CertificadoController {

    @Autowired
    private CertificadoService certificadoService;

    @Autowired
    private UserService userService;

    @PostMapping
    public ResponseEntity<GenericResponse<CertificadoDTO>> crearCertificado(
            @RequestBody CertificadoCreateDTO certificadoCreateDTO) {
        try {
            User adminUser = obtenerUsuarioAutenticado();

            if (!esAdministrador(adminUser)) {
                return ResponseBuilder.error("Solo los administradores pueden crear certificados");
            }

            CertificadoDTO certificado = certificadoService.crearCertificado(certificadoCreateDTO, adminUser.getId());

            return ResponseBuilder.success(certificado, "Certificado creado exitosamente");
        } catch (MidasExceptions e) {
            return ResponseBuilder.error(e.getDisplayMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("Error interno del servidor");
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<CertificadoDTO>> obtenerCertificado(@PathVariable Long id) {
        try {
            CertificadoDTO certificado = certificadoService.obtenerCertificadoPorId(id);
            return ResponseBuilder.success(certificado, "Certificado obtenido exitosamente");
        } catch (MidasExceptions e) {
            return ResponseBuilder.error(e.getDisplayMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("Error interno del servidor");
        }
    }

    @GetMapping("/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<List<CertificadoDTO>>> obtenerCertificadosPorUsuario(
            @PathVariable Long usuarioId) {
        try {
            List<CertificadoDTO> certificados = certificadoService.obtenerCertificadosPorUsuario(usuarioId);
            return ResponseBuilder.success(certificados, "Certificados obtenidos exitosamente");
        } catch (Exception e) {
            return ResponseBuilder.error("Error interno del servidor");
        }
    }

    @GetMapping("/curso/{cursoId}")
    public ResponseEntity<GenericResponse<List<CertificadoDTO>>> obtenerCertificadosPorCurso(
            @PathVariable Long cursoId) {
        try {
            List<CertificadoDTO> certificados = certificadoService.obtenerCertificadosPorCurso(cursoId);
            return ResponseBuilder.success(certificados, "Certificados obtenidos exitosamente");
        } catch (Exception e) {
            return ResponseBuilder.error("Error interno del servidor");
        }
    }

    @GetMapping("/verificar/{usuarioId}/{cursoId}")
    public ResponseEntity<GenericResponse<Map<String, Object>>> verificarElegibilidad(
            @PathVariable Long usuarioId, @PathVariable Long cursoId) {
        try {
            Map<String, Object> progreso = certificadoService.obtenerProgresoDetallado(usuarioId, cursoId);
            return ResponseBuilder.success(progreso, "Progreso obtenido exitosamente");
        } catch (Exception e) {
            return ResponseBuilder.error("Error interno del servidor");
        }
    }

    @GetMapping("/elegibles/{cursoId}")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> obtenerUsuariosElegibles(
            @PathVariable Long cursoId) {
        try {
            User adminUser = obtenerUsuarioAutenticado();
            if (!esAdministrador(adminUser)) {
                return ResponseBuilder.error("Solo los administradores pueden acceder a esta información");
            }

            List<Map<String, Object>> usuarios = certificadoService.obtenerUsuariosElegibles(cursoId);
            return ResponseBuilder.success(usuarios, "Usuarios elegibles obtenidos exitosamente");
        } catch (Exception e) {
            return ResponseBuilder.error("Error interno del servidor");
        }
    }

    @GetMapping
    public ResponseEntity<GenericResponse<Page<CertificadoDTO>>> obtenerCertificadosPaginados(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "fechaCreacion") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String estado) {
        try {
            User adminUser = obtenerUsuarioAutenticado();
            if (!esAdministrador(adminUser)) {
                return ResponseBuilder.error("Solo los administradores pueden acceder a esta información");
            }

            Sort sort = sortDir.equalsIgnoreCase("desc") ?
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<CertificadoDTO> certificados = certificadoService.obtenerCertificadosPaginados(search, estado, pageable);
            return ResponseBuilder.success(certificados, "Certificados obtenidos exitosamente");
        } catch (Exception e) {
            return ResponseBuilder.error("Error interno del servidor");
        }
    }

    @GetMapping("/codigo/{codigo}")
    public ResponseEntity<GenericResponse<CertificadoDTO>> obtenerCertificadoPorCodigo(@PathVariable String codigo) {
        try {
            CertificadoDTO certificado = certificadoService.obtenerCertificadoPorCodigo(codigo);
            return ResponseBuilder.success(certificado, "Certificado obtenido exitosamente");
        } catch (MidasExceptions e) {
            return ResponseBuilder.error(e.getDisplayMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("Error interno del servidor");
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<String>> eliminarCertificado(@PathVariable Long id) {
        try {
            User adminUser = obtenerUsuarioAutenticado();
            if (!esAdministrador(adminUser)) {
                return ResponseBuilder.error("Solo los administradores pueden eliminar certificados");
            }

            certificadoService.eliminarCertificado(id);
            return ResponseBuilder.success("Certificado eliminado exitosamente", "Certificado eliminado exitosamente");
        } catch (MidasExceptions e) {
            return ResponseBuilder.error(e.getDisplayMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("Error interno del servidor");
        }
    }

    private User obtenerUsuarioAutenticado() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        return userService.findByUsername(username)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));
    }

    private boolean esAdministrador(User user) {
        return user.getRole() == Role.ADMIN;
    }
}
