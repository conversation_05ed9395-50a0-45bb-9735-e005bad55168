package com.midas.crm.service.serviceImpl;

import com.google.api.client.http.ByteArrayContent;
import com.google.api.client.http.InputStreamContent;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.model.File;
import com.google.api.services.drive.model.FileList;
import com.google.api.services.drive.model.Permission;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.service.GoogleDriveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.midas.crm.utils.AgentNormalizationUtils.normalizeAgentNumber;

/**
 * Implementación del servicio de Google Drive
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GoogleDriveServiceImpl implements GoogleDriveService {
    private final Drive driveService;
    private final ClienteResidencialRepository clienteResidencialRepository;
    @Value("${google.drive.shared.email:}")
    private String sharedEmail;

    @Value("${google.drive.auto-share.enabled:false}")
    private boolean autoShareEnabled;

    @Override
    public String uploadFile(MultipartFile file, String fileName, String folderId) throws IOException {
        // Your existing implementation...
        File fileMetadata = new File().setName(fileName);
        if (folderId != null && !folderId.isEmpty()) {
            fileMetadata.setParents(Collections.singletonList(folderId));
        }
        InputStreamContent mediaContent = new InputStreamContent(file.getContentType(), file.getInputStream());
        mediaContent.setLength(file.getSize());
        File uploadedFile = driveService.files().create(fileMetadata, mediaContent).setFields("id").execute();
        return uploadedFile.getId();
    }

    @Override
    public String uploadFile(MultipartFile file, String fileName) throws IOException {
        return uploadFile(file, fileName, null);
    }

    @Override
    public byte[] downloadFile(String fileId) throws IOException {
        //    log.info("Descargando archivo con ID: {}", fileId);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        driveService.files().get(fileId).executeMediaAndDownloadTo(outputStream);

        byte[] content = outputStream.toByteArray();
        //   log.info("Archivo descargado exitosamente. Tamaño: {} bytes", content.length);

        return content;
    }

    @Override
    public void deleteFile(String fileId) throws IOException {
        //    log.info("Eliminando archivo con ID: {}", fileId);
        driveService.files().delete(fileId).execute();
        //  log.info("Archivo eliminado exitosamente");
    }

    @Override
    public List<Map<String, Object>> listFiles(String folderId, int pageSize) throws IOException {
        Map<String, Object> paginatedResult = listFilesWithPagination(folderId, pageSize, null, true);
        return (List<Map<String, Object>>) paginatedResult.get("content");
    }

    /**
     * Lista archivos con paginación completa y soporte para archivos compartidos
     */
    public Map<String, Object> listFilesWithPagination(String folderId, int pageSize, String pageToken, boolean includeShared) throws IOException {


        List<Map<String, Object>> allFiles = new ArrayList<>();
        String nextPageToken = null;
        int totalElements = 0;

        // Construir query base
        String query = "trashed=false";
        if (folderId != null && !folderId.isEmpty()) {
            query += " and '" + folderId + "' in parents";
        }

        if (includeShared && (folderId == null || folderId.isEmpty())) {
            // Para archivos en raíz con archivos compartidos, usar corpora=allDrives
            FileList result = driveService.files().list()
                    .setQ(query)
                    .setPageSize(pageSize)
                    .setPageToken(pageToken)
                    .setFields("nextPageToken, files(id,name,size,mimeType,createdTime,modifiedTime,parents,shared,ownedByMe,owners,webViewLink,webContentLink)")
                    .setOrderBy("name")
                    .setCorpora("allDrives")
                    .setIncludeItemsFromAllDrives(true)
                    .setSupportsAllDrives(true)
                    .execute();

            if (result.getFiles() != null) {
                for (File file : result.getFiles()) {
                    String source = file.getOwnedByMe() ? "own" : "shared";
                    Map<String, Object> fileInfo = createFileInfoMap(file, source);
                    allFiles.add(fileInfo);
                }
            }

            nextPageToken = result.getNextPageToken();

            // Calcular total solo en primera página
            if (pageToken == null || pageToken.isEmpty()) {
                try {
                    int ownCount = 0;
                    int sharedCount = 0;

                    // Contar archivos propios
                    FileList ownCountResult = driveService.files().list()
                            .setQ("trashed=false and 'me' in owners")
                            .setPageSize(1000)
                            .setFields("files(id)")
                            .execute();
                    ownCount = ownCountResult.getFiles() != null ? ownCountResult.getFiles().size() : 0;

                    // Contar archivos compartidos
                    FileList sharedCountResult = driveService.files().list()
                            .setQ("trashed=false and sharedWithMe=true")
                            .setPageSize(1000)
                            .setFields("files(id)")
                            .execute();
                    sharedCount = sharedCountResult.getFiles() != null ? sharedCountResult.getFiles().size() : 0;

                    totalElements = ownCount + sharedCount;

                } catch (Exception e) {
                    totalElements = allFiles.size();
                }
            } else {
                totalElements = allFiles.size();
            }

        } else {
            // Para carpetas específicas o solo archivos propios
            FileList result = driveService.files().list()
                    .setQ(query)
                    .setPageSize(pageSize)
                    .setPageToken(pageToken)
                    .setFields("nextPageToken, files(id,name,size,mimeType,createdTime,modifiedTime,parents,shared,ownedByMe,owners,webViewLink,webContentLink)")
                    .setOrderBy("name")
                    .execute();

            if (result.getFiles() != null) {
                for (File file : result.getFiles()) {
                    String source = file.getOwnedByMe() ? "own" : "shared";
                    Map<String, Object> fileInfo = createFileInfoMap(file, source);
                    allFiles.add(fileInfo);
                }
            }

            nextPageToken = result.getNextPageToken();

            // Calcular total solo en primera página
            if (pageToken == null || pageToken.isEmpty()) {
                try {
                    FileList countResult = driveService.files().list()
                            .setQ(query)
                            .setPageSize(1000)
                            .setFields("files(id)")
                            .execute();
                    totalElements = countResult.getFiles() != null ? countResult.getFiles().size() : 0;
                } catch (Exception e) {
                    totalElements = allFiles.size();
                }
            } else {
                totalElements = allFiles.size();
            }
        }

        // Crear respuesta paginada
        Map<String, Object> response = new HashMap<>();
        response.put("content", allFiles);
        response.put("nextPageToken", nextPageToken);
        response.put("hasNext", nextPageToken != null);
        response.put("pageSize", pageSize);
        response.put("totalElements", totalElements);
        response.put("folderId", folderId);
        response.put("includeShared", includeShared);

        return response;
    }

    /**
     * Crea un mapa con información de archivo
     */
    private Map<String, Object> createFileInfoMap(File file, String source) {
        Map<String, Object> fileInfo = new HashMap<>();
        fileInfo.put("id", file.getId());
        fileInfo.put("name", file.getName());
        fileInfo.put("size", file.getSize());
        fileInfo.put("mimeType", file.getMimeType());
        fileInfo.put("createdTime", file.getCreatedTime());
        fileInfo.put("modifiedTime", file.getModifiedTime());
        fileInfo.put("parents", file.getParents());
        fileInfo.put("webViewLink", file.getWebViewLink());
        fileInfo.put("webContentLink", file.getWebContentLink());
        fileInfo.put("source", source); // "own" o "shared"
        fileInfo.put("shared", file.getShared());
        fileInfo.put("ownedByMe", file.getOwnedByMe());

        // Agregar información del propietario para archivos compartidos
        if ("shared".equals(source) && file.getOwners() != null && !file.getOwners().isEmpty()) {
            fileInfo.put("owner", file.getOwners().get(0).getDisplayName());
            fileInfo.put("ownerEmail", file.getOwners().get(0).getEmailAddress());
        }

        return fileInfo;
    }

    @Override
    public List<Map<String, Object>> listFiles(int pageSize) throws IOException {
        return listFiles(null, pageSize);
    }

    @Override
    public String createFolder(String folderName, String parentFolderId) throws IOException {
        //   log.info("Creando carpeta: {} en carpeta padre: {}", folderName, parentFolderId);

        File fileMetadata = new File();
        fileMetadata.setName(folderName);
        fileMetadata.setMimeType("application/vnd.google-apps.folder");

        if (parentFolderId != null && !parentFolderId.isEmpty()) {
            fileMetadata.setParents(Collections.singletonList(parentFolderId));
        }

        File folder = driveService.files().create(fileMetadata)
                .setFields("id,name")
                .execute();

        //    log.info("Carpeta creada exitosamente. ID: {}", folder.getId());

        // Intentar compartir automáticamente solo si está habilitado y configurado
        if (autoShareEnabled && sharedEmail != null && !sharedEmail.isEmpty()) {
            try {
                shareFileWithRetry(folder.getId(), sharedEmail, "writer");
                //   log.info("Carpeta compartida automáticamente con {}", sharedEmail);
            } catch (Exception e) {
                // log.warn("No se pudo compartir automáticamente la carpeta con {}: {}", sharedEmail, e.getMessage());
                // No lanzar excepción, solo registrar el warning
            }
        }

        return folder.getId();
    }

    @Override
    public String createFolder(String folderName) throws IOException {
        return createFolder(folderName, null);
    }

    /**
     * Crea una carpeta en una ubicación específica accesible para el usuario objetivo
     */
    public String createFolderInSharedLocation(String folderName, String sharedParentFolderId) throws IOException {
        //     log.info("Creando carpeta: {} en ubicación compartida: {}", folderName, sharedParentFolderId);

        File fileMetadata = new File();
        fileMetadata.setName(folderName);
        fileMetadata.setMimeType("application/vnd.google-apps.folder");

        if (sharedParentFolderId != null && !sharedParentFolderId.isEmpty()) {
            fileMetadata.setParents(Collections.singletonList(sharedParentFolderId));
        }

        File folder = driveService.files().create(fileMetadata)
                .setFields("id,name")
                .execute();

        //   log.info("Carpeta creada exitosamente en ubicación compartida. ID: {}", folder.getId());
        return folder.getId();
    }

    @Override
    public Map<String, Object> getFileInfo(String fileId) throws IOException {
        //    log.info("Obteniendo información del archivo: {}", fileId);

        File file = driveService.files().get(fileId)
                .setFields("id,name,size,mimeType,createdTime,modifiedTime,parents,webViewLink,webContentLink")
                .execute();

        Map<String, Object> fileInfo = new HashMap<>();
        fileInfo.put("id", file.getId());
        fileInfo.put("name", file.getName());
        fileInfo.put("size", file.getSize());
        fileInfo.put("mimeType", file.getMimeType());
        fileInfo.put("createdTime", file.getCreatedTime());
        fileInfo.put("modifiedTime", file.getModifiedTime());
        fileInfo.put("parents", file.getParents());
        fileInfo.put("webViewLink", file.getWebViewLink());
        fileInfo.put("webContentLink", file.getWebContentLink());

        return fileInfo;
    }

    @Override
    public String shareFile(String fileId, String email, String role) throws IOException {
        return shareFileWithRetry(fileId, email, role);
    }

    /**
     * Comparte un archivo con reintentos y mejor manejo de errores
     */
    private String shareFileWithRetry(String fileId, String email, String role) throws IOException {
        //   log.info("Compartiendo archivo {} con {} con rol: {}", fileId, email, role);

        try {
            Permission permission = new Permission();
            permission.setType("user");
            permission.setRole(role);
            permission.setEmailAddress(email);

            Permission createdPermission = driveService.permissions().create(fileId, permission)
                    .setFields("id")
                    .setSendNotificationEmail(false) // Evitar envío de notificaciones
                    .execute();

            //    log.info("Archivo compartido exitosamente. Permission ID: {}", createdPermission.getId());
            return createdPermission.getId();

        } catch (IOException e) {
            // Si falla el compartir directo, intentar hacer público primero
            if (e.getMessage().contains("insufficient permissions") ||
                    e.getMessage().contains("permission denied")) {

                //   log.warn("Permisos insuficientes para compartir directamente. Intentando hacer público...");
                return makeFilePublicAndGetLink(fileId);
            }
            throw e;
        }
    }

    /**
     * Hace un archivo público y retorna el enlace
     */
    private String makeFilePublicAndGetLink(String fileId) throws IOException {
        Permission permission = new Permission();
        permission.setType("anyone");
        permission.setRole("reader");

        Permission createdPermission = driveService.permissions().create(fileId, permission)
                .setFields("id")
                .execute();

        //log.info("Archivo hecho público. Permission ID: {}", createdPermission.getId());
        return createdPermission.getId();
    }

    @Override
    public String getPublicLink(String fileId) throws IOException {
        // log.info("Obteniendo enlace público para archivo: {}", fileId);

        // Hacer el archivo público
        Permission permission = new Permission();
        permission.setType("anyone");
        permission.setRole("reader");

        driveService.permissions().create(fileId, permission).execute();

        // Obtener el enlace
        File file = driveService.files().get(fileId)
                .setFields("webViewLink,webContentLink")
                .execute();

        String publicLink = file.getWebViewLink();
        //   log.info("Enlace público generado: {}", publicLink);

        return publicLink;
    }

    @Override
    public String findFolderByName(String folderName) throws IOException {
        //    log.info("Buscando carpeta con nombre: {}", folderName);

        String query = "mimeType='application/vnd.google-apps.folder' and name='" + folderName + "' and trashed=false";

        FileList result = driveService.files().list()
                .setQ(query)
                .setPageSize(1)
                .setFields("files(id,name)")
                .execute();

        List<File> folders = result.getFiles();
        if (folders != null && !folders.isEmpty()) {
            String folderId = folders.get(0).getId();
            //     log.info("Carpeta '{}' encontrada con ID: {}", folderName, folderId);
            return folderId;
        } else {
            //   log.warn("Carpeta '{}' no encontrada", folderName);
            return null;
        }
    }

    /**
     * Busca o crea una carpeta compartida
     */
    public String findOrCreateSharedFolder(String folderName, String userEmail) throws IOException {
        //  log.info("Buscando o creando carpeta compartida: {} para usuario: {}", folderName, userEmail);

        // Primero buscar si ya existe
        String existingFolderId = findFolderByName(folderName);
        if (existingFolderId != null) {
            //    log.info("Carpeta existente encontrada: {}", existingFolderId);
            return existingFolderId;
        }

        // Si no existe, crear nueva carpeta
        String folderId = createFolder(folderName);

        // Intentar compartir
        try {
            shareFile(folderId, userEmail, "writer");
            //   log.info("Carpeta compartida exitosamente con: {}", userEmail);
        } catch (Exception e) {
            // log.warn("No se pudo compartir la carpeta, pero fue creada exitosamente: {}", e.getMessage());
        }

        return folderId;
    }

    @Override
    public List<Map<String, Object>> listFolders(int pageSize) throws IOException {
        Map<String, Object> paginatedResult = listFoldersWithPagination(pageSize, null, true);
        return (List<Map<String, Object>>) paginatedResult.get("content");
    }

    /**
     * Lista carpetas con paginación completa y soporte para carpetas compartidas
     */
    public Map<String, Object> listFoldersWithPagination(int pageSize, String pageToken, boolean includeShared) throws IOException {

        List<Map<String, Object>> allFolders = new ArrayList<>();
        String nextPageToken = null;
        int totalElements = 0;

        if (includeShared) {
            // Estrategia: obtener carpetas propias y compartidas por separado, luego combinar
            List<Map<String, Object>> ownFolders = new ArrayList<>();
            List<Map<String, Object>> sharedFolders = new ArrayList<>();

            // 1. Obtener carpetas propias
            String ownQuery = "mimeType='application/vnd.google-apps.folder' and trashed=false and 'me' in owners";
            FileList ownResult = driveService.files().list()
                    .setQ(ownQuery)
                    .setPageSize(pageSize)
                    .setPageToken(pageToken)
                    .setFields("nextPageToken, files(id,name,createdTime,modifiedTime,shared,ownedByMe)")
                    .setOrderBy("name")
                    .execute();

            if (ownResult.getFiles() != null) {
                for (File folder : ownResult.getFiles()) {
                    Map<String, Object> folderInfo = createFolderInfoMap(folder, "own");
                    ownFolders.add(folderInfo);
                }
            }

            // 2. Si es la primera página, obtener también carpetas compartidas
            if (pageToken == null || pageToken.isEmpty()) {
                try {
                    String sharedQuery = "mimeType='application/vnd.google-apps.folder' and trashed=false and sharedWithMe=true";
                    FileList sharedResult = driveService.files().list()
                            .setQ(sharedQuery)
                            .setPageSize(Math.min(pageSize, 100)) // Limitar compartidas
                            .setFields("files(id,name,createdTime,modifiedTime,shared,ownedByMe,owners)")
                            .setOrderBy("name")
                            .execute();

                    if (sharedResult.getFiles() != null) {
                        for (File folder : sharedResult.getFiles()) {
                            Map<String, Object> folderInfo = createFolderInfoMap(folder, "shared");
                            sharedFolders.add(folderInfo);
                        }
                    }
                } catch (Exception e) {
                    log.warn("Error al obtener carpetas compartidas: {}", e.getMessage());
                }
            }

            // Combinar y ordenar por nombre
            allFolders.addAll(ownFolders);
            allFolders.addAll(sharedFolders);
            allFolders.sort((a, b) -> {
                String nameA = (String) a.get("name");
                String nameB = (String) b.get("name");
                return nameA.compareToIgnoreCase(nameB);
            });

            // Aplicar paginación manual si tenemos más elementos que el pageSize
            if (allFolders.size() > pageSize) {
                List<Map<String, Object>> paginatedFolders = allFolders.subList(0, pageSize);
                allFolders = new ArrayList<>(paginatedFolders);
                // Crear un token simple para la siguiente página
                nextPageToken = ownResult.getNextPageToken();
            } else {
                nextPageToken = ownResult.getNextPageToken();
            }

            // Calcular total solo en primera página
            if (pageToken == null || pageToken.isEmpty()) {
                try {
                    int ownCount = 0;
                    int sharedCount = 0;

                    // Contar carpetas propias
                    FileList ownCountResult = driveService.files().list()
                            .setQ("mimeType='application/vnd.google-apps.folder' and trashed=false and 'me' in owners")
                            .setPageSize(1000)
                            .setFields("files(id)")
                            .execute();
                    ownCount = ownCountResult.getFiles() != null ? ownCountResult.getFiles().size() : 0;

                    // Contar carpetas compartidas
                    FileList sharedCountResult = driveService.files().list()
                            .setQ("mimeType='application/vnd.google-apps.folder' and trashed=false and sharedWithMe=true")
                            .setPageSize(1000)
                            .setFields("files(id)")
                            .execute();
                    sharedCount = sharedCountResult.getFiles() != null ? sharedCountResult.getFiles().size() : 0;

                    totalElements = ownCount + sharedCount;
                    log.info("Total calculado: {} carpetas propias + {} compartidas = {}", ownCount, sharedCount, totalElements);

                } catch (Exception e) {
                    log.warn("Error al contar total de carpetas: {}", e.getMessage());
                    totalElements = allFolders.size();
                }
            } else {
                totalElements = allFolders.size();
            }

        } else {
            // Solo carpetas propias
            String ownQuery = "mimeType='application/vnd.google-apps.folder' and trashed=false and 'me' in owners";

            FileList ownResult = driveService.files().list()
                    .setQ(ownQuery)
                    .setPageSize(pageSize)
                    .setPageToken(pageToken)
                    .setFields("nextPageToken, files(id,name,createdTime,modifiedTime,shared,ownedByMe)")
                    .setOrderBy("name")
                    .execute();

            if (ownResult.getFiles() != null) {
                for (File folder : ownResult.getFiles()) {
                    Map<String, Object> folderInfo = createFolderInfoMap(folder, "own");
                    allFolders.add(folderInfo);
                }
            }

            nextPageToken = ownResult.getNextPageToken();

            // Contar total solo en primera página
            if (pageToken == null || pageToken.isEmpty()) {
                try {
                    FileList countResult = driveService.files().list()
                            .setQ(ownQuery)
                            .setPageSize(1000)
                            .setFields("files(id)")
                            .execute();
                    totalElements = countResult.getFiles() != null ? countResult.getFiles().size() : 0;
                } catch (Exception e) {
                    log.warn("Error al contar total de carpetas propias: {}", e.getMessage());
                    totalElements = allFolders.size();
                }
            } else {
                totalElements = allFolders.size();
            }
        }

        // Crear respuesta paginada
        Map<String, Object> response = new HashMap<>();
        response.put("content", allFolders);
        response.put("nextPageToken", nextPageToken);
        response.put("hasNext", nextPageToken != null);
        response.put("pageSize", pageSize);
        response.put("totalElements", totalElements);
        response.put("includeShared", includeShared);

        log.info("Se encontraron {} carpetas en esta página, total estimado: {}", allFolders.size(), totalElements);
        return response;
    }

    /**
     * Crea un mapa con información de carpeta
     */
    private Map<String, Object> createFolderInfoMap(File folder, String source) {
        Map<String, Object> folderInfo = new HashMap<>();
        folderInfo.put("id", folder.getId());
        folderInfo.put("name", folder.getName());
        folderInfo.put("createdTime", folder.getCreatedTime());
        folderInfo.put("modifiedTime", folder.getModifiedTime());
        folderInfo.put("type", "folder");
        folderInfo.put("source", source); // "own" o "shared"
        folderInfo.put("shared", folder.getShared());
        folderInfo.put("ownedByMe", folder.getOwnedByMe());

        // Agregar información del propietario para carpetas compartidas
        if ("shared".equals(source) && folder.getOwners() != null && !folder.getOwners().isEmpty()) {
            folderInfo.put("owner", folder.getOwners().get(0).getDisplayName());
            folderInfo.put("ownerEmail", folder.getOwners().get(0).getEmailAddress());
        }

        return folderInfo;
    }

    @Override
    public List<Map<String, Object>> findMp3FilesByAgent(String numeroAgente) throws IOException {
        // Buscar archivos MP3 que contengan el número de agente en el nombre
        String query = "trashed=false and mimeType='audio/mpeg' and name contains '" + numeroAgente + "'";

        FileList result = driveService.files().list()
                .setQ(query)
                .setPageSize(50) // Buscar hasta 50 archivos
                .setFields("files(id,name,size,mimeType,createdTime,modifiedTime,parents,webContentLink)")
                .execute();

        List<Map<String, Object>> files = new ArrayList<>();
        for (File file : result.getFiles()) {
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("id", file.getId());
            fileInfo.put("name", file.getName());
            fileInfo.put("size", file.getSize());
            fileInfo.put("mimeType", file.getMimeType());
            fileInfo.put("createdTime", file.getCreatedTime());
            fileInfo.put("modifiedTime", file.getModifiedTime());
            fileInfo.put("parents", file.getParents());
            fileInfo.put("webContentLink", file.getWebContentLink());
            files.add(fileInfo);
        }

        return files;
    }

    @Override
    public String findMp3UrlByAgent(String numeroAgente, String folderId) throws IOException {
        try {
            // Construir query de búsqueda base
            StringBuilder queryBuilder = new StringBuilder("trashed=false and (mimeType='audio/mpeg' or mimeType='audio/x-gsm')");

            // Agregar filtro de carpeta si se especifica
            if (folderId != null && !folderId.isEmpty()) {
                queryBuilder.append(" and '").append(folderId).append("' in parents");
            }

            // Buscar archivos que contengan el número de agente en diferentes formatos
            String[] searchPatterns = {
                    "agent" + numeroAgente,
                    "agent0" + numeroAgente,
                    "agent00" + numeroAgente,
                    "agen" + numeroAgente,
                    "agen0" + numeroAgente,
                    "agen00" + numeroAgente
            };

            for (String pattern : searchPatterns) {
                String query = queryBuilder.toString() + " and name contains '" + pattern + "'";

                FileList result = driveService.files().list()
                        .setQ(query)
                        .setPageSize(20)
                        .setFields("files(id,name,webContentLink,mimeType)")
                        .execute();

                if (!result.getFiles().isEmpty()) {
                    // Buscar el archivo más reciente que coincida exactamente
                    for (File file : result.getFiles()) {
                        String fileName = file.getName().toLowerCase();
                        if (isFileForAgentWithPattern(fileName, numeroAgente)) {
                            log.info("Archivo encontrado para agente {}: {}", numeroAgente, file.getName());
                            return getDirectDownloadUrl(file.getId());
                        }
                    }
                }
            }

            log.warn("No se encontró archivo de audio para agente: {}", numeroAgente);
            return null; // No se encontró archivo

        } catch (Exception e) {
            throw new IOException("Error al buscar archivo de audio para agente " + numeroAgente, e);
        }
    }

    /**
     * Busca archivo de audio por número móvil y agente (más preciso)
     */
    public String findAudioByMovilAndAgent(String numeroMovil, String numeroAgente, String folderId) throws IOException {
        try {
            // Si se especifica una carpeta específica, buscar solo en esa carpeta
            if (folderId != null && !folderId.isEmpty()) {
                return findAudioInSpecificFolder(numeroMovil, numeroAgente, folderId);
            }

            // Si no se especifica carpeta, buscar sistemáticamente en todas las carpetas
            return findAudioInAllFolders(numeroMovil, numeroAgente);

        } catch (Exception e) {
            throw new IOException("Error al buscar archivo de audio por móvil y agente", e);
        }
    }

    /**
     * Busca archivo de audio por número móvil, agente y fecha específica
     * Convierte la fecha del formato yyyy-MM-dd al formato dd/MM/yyyy para buscar la carpeta correspondiente
     */
    @Override
    public String findAudioByMovilAgentAndDate(String numeroMovil, String numeroAgente, String fechaCreacion, String folderId) throws IOException {
        try {
            // Si se especifica una carpeta específica, buscar solo en esa carpeta
            if (folderId != null && !folderId.isEmpty()) {
                return findAudioInSpecificFolder(numeroMovil, numeroAgente, folderId);
            }

            // Si se especifica fecha, buscar primero en la carpeta de fecha específica
            if (fechaCreacion != null && !fechaCreacion.isEmpty()) {
                String dateBasedFolderId = findFolderByDateFormat(fechaCreacion);
                if (dateBasedFolderId != null) {
                    log.info("🎯 Buscando en carpeta de fecha específica: {}", fechaCreacion);
                    String audioUrl = findAudioInSpecificFolder(numeroMovil, numeroAgente, dateBasedFolderId);
                    if (audioUrl != null) {
                        return audioUrl;
                    }
                    log.debug("🔍 No encontrado en carpeta de fecha, continuando con búsqueda general");
                }
            }

            // Fallback: buscar sistemáticamente en todas las carpetas
            return findAudioInAllFolders(numeroMovil, numeroAgente);

        } catch (Exception e) {
            throw new IOException("Error al buscar archivo de audio por móvil, agente y fecha", e);
        }
    }

    /**
     * Busca archivo de audio en una carpeta específica
     */
    private String findAudioInSpecificFolder(String numeroMovil, String numeroAgente, String folderId) throws IOException {
        // Construir query de búsqueda base
        StringBuilder queryBuilder = new StringBuilder("trashed=false and (mimeType='audio/mpeg' or mimeType='audio/x-gsm')");
        queryBuilder.append(" and '").append(folderId).append("' in parents");

        // 🎯 BÚSQUEDA ESTRICTA: Solo buscar archivos que contengan EXACTAMENTE móvil Y agente
        if (numeroMovil != null && !numeroMovil.trim().isEmpty()) {
            String cleanMovil = numeroMovil.replaceAll("[^0-9]", "");

            String query = queryBuilder.toString() + " and name contains '" + cleanMovil + "'";

            FileList result = driveService.files().list()
                    .setQ(query)
                    .setPageSize(50)
                    .setFields("files(id,name,webContentLink,mimeType,createdTime)")
                    .setOrderBy("createdTime desc")
                    .execute();

            if (!result.getFiles().isEmpty()) {
                // 🎯 VERIFICACIÓN ESTRICTA: Debe contener EXACTAMENTE el móvil Y el agente
                for (File file : result.getFiles()) {
                    String fileName = file.getName().toLowerCase();

                    // Verificar que contenga el móvil exacto
                    boolean containsExactMovil = containsExactMobileNumber(fileName, cleanMovil);

                    // Verificar que contenga el agente exacto
                    boolean containsExactAgent = isFileForAgentWithPattern(fileName, numeroAgente);

                    if (containsExactMovil && containsExactAgent) {
                        return getDirectDownloadUrl(file.getId());
                    }
                }
            }
        }

        // ❌ NO buscar solo por agente si no se encuentra por móvil exacto
        return null;
    }

    private String findAudioInAllFolders(String movil, String agente) throws IOException {

        List<Map<String, Object>> folders = listFolders(100);

        for (Map<String, Object> folder : folders) {
            String folderId   = (String) folder.get("id");
            String folderName = (String) folder.get("name");

            String url = findAudioInSpecificFolder(movil, agente, folderId);
            if (url != null) {
                return url;
            }
        }
        return null;
    }

    /**
     * Obtiene todos los archivos de audio de una fecha específica
     */
    @Override
    public List<Map<String, Object>> getAllAudioFilesByDate(String fechaCreacion) throws IOException {
        try {

            // Buscar la carpeta de la fecha específica
            String dateBasedFolderId = findFolderByDateFormat(fechaCreacion);
            if (dateBasedFolderId == null) {
                return new ArrayList<>();
            }

            // Obtener todos los archivos de audio de esa carpeta
            List<Map<String, Object>> audioFiles = new ArrayList<>();

            // Query para buscar solo archivos de audio en la carpeta específica
            String query = "trashed=false and (mimeType='audio/mpeg' or mimeType='audio/x-gsm') and '" + dateBasedFolderId + "' in parents";

            // 🔧 OBTENER TODOS LOS ARCHIVOS CON PAGINACIÓN
            String pageToken = null;
            do {
                FileList result = driveService.files().list()
                        .setQ(query)
                        .setPageSize(1000) // Máximo permitido por Google Drive API
                        .setPageToken(pageToken)
                        .setFields("nextPageToken, files(id,name,size,mimeType,createdTime,modifiedTime,parents,webContentLink)")
                        .setOrderBy("createdTime desc")
                        .execute();

                if (result.getFiles() != null) {
                    for (File file : result.getFiles()) {
                        Map<String, Object> fileInfo = createFileInfoMap(file);
                        audioFiles.add(fileInfo);
                    }
                }

                pageToken = result.getNextPageToken();

                // Log del progreso
                log.info("📁 Obtenidos {} archivos hasta ahora para fecha {}", audioFiles.size(), fechaCreacion);

            } while (pageToken != null);

            log.info("✅ Total de archivos de audio obtenidos para fecha {}: {}", fechaCreacion, audioFiles.size());
            return audioFiles;

        } catch (Exception e) {
            throw new IOException("Error al obtener archivos de audio por fecha", e);
        }
    }

    /**
     * Extrae información del nombre del archivo de audio
     * Formato esperado: 20250605-072314_665876797_TESTCAMP_agent010-all.gsm
     */
    private Map<String, String> extractInfoFromFileName(String fileName) {
        Map<String, String> info = new HashMap<>();

        try {
            // Patrón para extraer fecha, móvil y agente del nombre del archivo
            // Formato: YYYYMMDD-HHMMSS_MOVIL_CAMPAIGN_agentXXX-all.ext
            String[] parts = fileName.split("_");

            if (parts.length >= 3) {
                // Extraer fecha (primera parte antes del guión)
                String datePart = parts[0];
                if (datePart.contains("-")) {
                    String dateOnly = datePart.split("-")[0];
                    if (dateOnly.length() == 8) {
                        // Convertir YYYYMMDD a YYYY-MM-DD
                        String year = dateOnly.substring(0, 4);
                        String month = dateOnly.substring(4, 6);
                        String day = dateOnly.substring(6, 8);
                        info.put("fecha", year + "-" + month + "-" + day);
                    }
                }

                // Extraer móvil (segunda parte)
                String movilPart = parts[1];
                if (movilPart.matches("\\d+")) {
                    info.put("movil", movilPart);
                }

                // Extraer agente (buscar en todas las partes la que contenga "agent")
                for (String part : parts) {
                    if (part.toLowerCase().contains("agent")) {
                        // Extraer número del agente
                        String agentePart = part.toLowerCase();
                        String agenteNumero = agentePart.replaceAll("agent0*", "").replaceAll("-.*", "");
                        if (agenteNumero.matches("\\d+")) {
                            info.put("agente", agenteNumero);
                        }
                        break;
                    }
                }
            }

        } catch (Exception e) {
            log.debug("⚠️ Error al extraer información del archivo {}: {}", fileName, e.getMessage());
        }

        return info;
    }

    /**
     * Busca una carpeta por fecha, convirtiendo del formato yyyy-MM-dd al formato dd/MM/yyyy
     * @param fechaCreacion Fecha en formato yyyy-MM-dd (ej: 2025-06-04)
     * @return ID de la carpeta si se encuentra, null si no existe
     */
    private String findFolderByDateFormat(String fechaCreacion) throws IOException {
        try {
            // Convertir de yyyy-MM-dd a dd/MM/yyyy
            String[] parts = fechaCreacion.split("-");
            if (parts.length != 3) {
                return null;
            }

            String year = parts[0];
            String month = parts[1];
            String day = parts[2];

            // Formato esperado en Google Drive: dd/MM/yyyy
            String folderNameFormat = String.format("%s/%s/%s", day, month, year);

            // Buscar la carpeta por nombre exacto
            String folderId = findFolderByName(folderNameFormat);

            if (folderId != null) {
                return folderId;
            } else {
                return null;
            }

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Verifica si el archivo contiene exactamente el número móvil especificado
     */
    private boolean containsExactMobileNumber(String fileName, String mobileNumber) {
        try {
            // El número móvil debe aparecer como una secuencia completa en el nombre del archivo
            // Buscar patrones donde el número móvil esté separado por caracteres no numéricos

            // Patrón 1: Número móvil precedido y seguido por caracteres no numéricos
            String pattern1 = ".*[^0-9]" + mobileNumber + "[^0-9].*";

            // Patrón 2: Número móvil al inicio seguido por caracteres no numéricos
            String pattern2 = "^" + mobileNumber + "[^0-9].*";

            // Patrón 3: Número móvil al final precedido por caracteres no numéricos
            String pattern3 = ".*[^0-9]" + mobileNumber + "$";

            // Patrón 4: Número móvil exacto (todo el nombre)
            String pattern4 = "^" + mobileNumber + "$";

            boolean matches = fileName.matches(pattern1) ||
                    fileName.matches(pattern2) ||
                    fileName.matches(pattern3) ||
                    fileName.matches(pattern4);

            return matches;

        } catch (Exception e) {
            // Fallback: verificación simple
            return fileName.contains(mobileNumber);
        }
    }

    /**
     * Verifica si un archivo corresponde realmente al agente especificado
     */
    private boolean isFileForAgent(String fileName, String numeroAgente) {
        // Normalizar nombre del archivo
        fileName = fileName.replaceAll("[^a-z0-9]", "");

        // Patrones a buscar
        String[] patterns = {
                numeroAgente,
                "0" + numeroAgente,
                "00" + numeroAgente,
                "agen" + numeroAgente,
                "agen0" + numeroAgente,
                "agen00" + numeroAgente
        };

        for (String pattern : patterns) {
            if (fileName.contains(pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Verifica si un archivo corresponde al agente usando el patrón específico de los archivos
     * Formato: YYYYMMDD-HHMMSS_NUMEROMOVILCONTACTO_TESTCAMP_agentXXX-all.gsm
     */
    private boolean isFileForAgentWithPattern(String fileName, String numeroAgente) {
        try {
            // Normalizar nombre del archivo
            fileName = fileName.toLowerCase();

            // Buscar el patrón "agent" seguido del número
            String[] agentPatterns = {
                    "agent" + numeroAgente + "-",
                    "agent" + String.format("%02d", Integer.parseInt(numeroAgente)) + "-",
                    "agent" + String.format("%03d", Integer.parseInt(numeroAgente)) + "-"
            };

            for (String pattern : agentPatterns) {
                if (fileName.contains(pattern)) {
                    return true;
                }
            }

            return false;

        } catch (NumberFormatException e) {
            // Si no se puede parsear el número, usar método básico
            return isFileForAgent(fileName, numeroAgente);
        }
    }

    /**
     * Sube un archivo a Google Drive desde byte array
     */
    @Override
    public String uploadFile(byte[] fileContent, String fileName, String mimeType, String folderId) throws IOException {
        try {
            // Crear metadata del archivo
            File fileMetadata = new File();
            fileMetadata.setName(fileName);

            if (folderId != null && !folderId.isEmpty()) {
                fileMetadata.setParents(java.util.Collections.singletonList(folderId));
            }

            // Crear contenido del archivo
            ByteArrayContent mediaContent = new ByteArrayContent(mimeType, fileContent);

            // Subir archivo
            File uploadedFile = driveService.files().create(fileMetadata, mediaContent)
                    .setFields("id,name,size,mimeType,createdTime,modifiedTime")
                    .execute();

            return uploadedFile.getId();

        } catch (Exception e) {
            throw new IOException("Error al subir archivo: " + e.getMessage(), e);
        }
    }

    /**
     * Sube un archivo a Google Drive desde byte array en la carpeta raíz
     */
    @Override
    public String uploadFile(byte[] fileContent, String fileName, String mimeType) throws IOException {
        return uploadFile(fileContent, fileName, mimeType, null);
    }

    /**
     * Obtiene URL de descarga directa para un archivo
     */
    @Override
    public String getDirectDownloadUrl(String fileId) throws IOException {
        // Hacer el archivo público temporalmente para obtener URL de descarga
        try {
            Permission permission = new Permission()
                    .setType("anyone")
                    .setRole("reader");

            driveService.permissions().create(fileId, permission).execute();

            // Retornar URL de descarga directa
            return "https://drive.google.com/uc?export=download&id=" + fileId;

        } catch (Exception e) {
            // Si no se puede hacer público, retornar URL básica
            return "https://drive.google.com/file/d/" + fileId + "/view";
        }
    }

    /**
     * Obtiene el nombre de un archivo por su ID
     */
    @Override
    public String getFileName(String fileId) throws IOException {
        try {
            File file = driveService.files().get(fileId)
                    .setFields("name")
                    .execute();
            return file.getName();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Búsqueda profesional de audios con paginación
     * Formato esperado: YYYYMMDD-HHMMSS_NUMEROMOVILCONTACTO_TESTCAMP_agentXXX-all.gsm
     */
    @Override
    public Map<String, Object> searchAudiosProfessional(String fechaCreacion, String numeroMovil,
                                                        String numeroAgente, String folderId,
                                                        int page, int size) throws IOException {
        log.info("🔍 Búsqueda profesional de audios - Fecha: {}, Móvil: {}, Agente: {}, Carpeta: {}, Página: {}, Tamaño: {}",
                fechaCreacion, numeroMovil, numeroAgente, folderId, page, size);

        // Construir query base
        StringBuilder queryBuilder = new StringBuilder("trashed=false and (mimeType='audio/mpeg' or mimeType='audio/x-gsm')");

        // Filtrar por carpeta si se especifica
        if (folderId != null && !folderId.isEmpty()) {
            queryBuilder.append(" and '").append(folderId).append("' in parents");
        }

        // Construir patrones de búsqueda según los parámetros
        List<String> searchPatterns = buildSearchPatterns(fechaCreacion, numeroMovil, numeroAgente);

        List<Map<String, Object>> allResults = new ArrayList<>();

        // Buscar con cada patrón
        for (String pattern : searchPatterns) {
            String query = queryBuilder.toString();
            if (!pattern.isEmpty()) {
                query += " and name contains '" + pattern + "'";
            }

            log.debug("🔍 Ejecutando query: {}", query);

            FileList result = driveService.files().list()
                    .setQ(query)
                    .setPageSize(100) // Obtener más resultados para filtrar después
                    .setFields("files(id,name,size,mimeType,createdTime,modifiedTime,parents,webContentLink)")
                    .setOrderBy("createdTime desc")
                    .execute();

            // Procesar y filtrar resultados
            for (File file : result.getFiles()) {
                if (matchesExactCriteria(file.getName(), fechaCreacion, numeroMovil, numeroAgente)) {
                    Map<String, Object> fileInfo = createFileInfoMap(file);
                    allResults.add(fileInfo);
                }
            }
        }

        // Si no hay patrones específicos, buscar en todas las carpetas
        if (searchPatterns.isEmpty()) {
            allResults = searchInAllFolders(queryBuilder.toString());
        }

        // Eliminar duplicados por ID
        allResults = removeDuplicates(allResults);

        // Aplicar paginación
        return applyPagination(allResults, page, size);
    }

    /**
     * Construye patrones de búsqueda según los parámetros proporcionados
     */
    private List<String> buildSearchPatterns(String fechaCreacion, String numeroMovil, String numeroAgente) {
        List<String> patterns = new ArrayList<>();

        // Si tenemos fecha, usarla como patrón principal
        if (fechaCreacion != null && !fechaCreacion.trim().isEmpty()) {
            String cleanFecha = fechaCreacion.replaceAll("[^0-9]", "");
            patterns.add(cleanFecha);
        }

        // Si tenemos móvil, agregarlo como patrón
        if (numeroMovil != null && !numeroMovil.trim().isEmpty()) {
            String cleanMovil = numeroMovil.replaceAll("[^0-9]", "");
            patterns.add(cleanMovil);
        }

        // Si tenemos agente, agregar patrones de agente
        if (numeroAgente != null && !numeroAgente.trim().isEmpty()) {
            patterns.add("agent" + numeroAgente);
            patterns.add("agent" + String.format("%02d", Integer.parseInt(numeroAgente)));
            patterns.add("agent" + String.format("%03d", Integer.parseInt(numeroAgente)));
        }

        return patterns;
    }

    /**
     * Verifica si un archivo coincide exactamente con los criterios
     */
    private boolean matchesExactCriteria(String fileName, String fechaCreacion, String numeroMovil, String numeroAgente) {
        if (fileName == null) return false;

        String lowerFileName = fileName.toLowerCase();

        // Verificar fecha si se especifica
        if (fechaCreacion != null && !fechaCreacion.trim().isEmpty()) {
            String cleanFecha = fechaCreacion.replaceAll("[^0-9]", "");
            if (!lowerFileName.contains(cleanFecha)) {
                return false;
            }
        }

        // Verificar móvil si se especifica
        if (numeroMovil != null && !numeroMovil.trim().isEmpty()) {
            String cleanMovil = numeroMovil.replaceAll("[^0-9]", "");
            if (!containsExactMobileNumber(lowerFileName, cleanMovil)) {
                return false;
            }
        }

        // Verificar agente si se especifica
        if (numeroAgente != null && !numeroAgente.trim().isEmpty()) {
            if (!isFileForAgentWithPattern(lowerFileName, numeroAgente)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Crea un mapa con información del archivo
     */
    private Map<String, Object> createFileInfoMap(File file) {
        Map<String, Object> fileInfo = new HashMap<>();
        fileInfo.put("id", file.getId());
        fileInfo.put("name", file.getName());
        fileInfo.put("size", file.getSize());
        fileInfo.put("mimeType", file.getMimeType());
        fileInfo.put("createdTime", file.getCreatedTime());
        fileInfo.put("modifiedTime", file.getModifiedTime());
        fileInfo.put("webContentLink", file.getWebContentLink());
        fileInfo.put("parents", file.getParents());

        // Extraer información del nombre del archivo
        Map<String, String> parsedInfo = parseFileName(file.getName());
        fileInfo.put("parsedInfo", parsedInfo);

        // Mapear a campos esperados por TranscriptionQueueServiceImpl
        fileInfo.put("extractedMovil", parsedInfo.get("numeroMovil"));
        fileInfo.put("extractedAgente", parsedInfo.get("numeroAgente"));
        fileInfo.put("extractedFecha", parsedInfo.get("fecha"));

        return fileInfo;
    }

    private Map<String, String> parseFileName(String fileName) {
        Map<String, String> info = new HashMap<>();
        if (fileName == null || fileName.isEmpty()) {
            return info;
        }
        try {
            Pattern agentPattern = Pattern.compile("agent(\\d+)");
            Matcher agentMatcher = agentPattern.matcher(fileName.toLowerCase());
            if (agentMatcher.find()) {
                info.put("numeroAgente", agentMatcher.group(1));
            }
            Pattern mobilePattern = Pattern.compile("(?<!\\d)(\\d{9})(?!\\d)");
            Matcher mobileMatcher = mobilePattern.matcher(fileName);
            if (mobileMatcher.find()) {
                info.put("numeroMovil", mobileMatcher.group(1));
            }
            Pattern dateTimePattern = Pattern.compile("(\\d{8})-(\\d{6})");
            Matcher dateTimeMatcher = dateTimePattern.matcher(fileName);
            if (dateTimeMatcher.find()) {
                info.put("fecha", dateTimeMatcher.group(1));
                info.put("hora", dateTimeMatcher.group(2));
            }
        } catch (Exception e) {
            log.error("Error parsing fileName: {} - {}", fileName, e.getMessage());
        }
        return info;
    }

    /**
     * Busca en todas las carpetas cuando no hay patrones específicos
     */
    private List<Map<String, Object>> searchInAllFolders(String baseQuery) throws IOException {
        List<Map<String, Object>> results = new ArrayList<>();

        // Obtener todas las carpetas
        List<Map<String, Object>> folders = getFoldersRecursive(null, 3);

        for (Map<String, Object> folder : folders) {
            String folderId = (String) folder.get("id");
            String query = baseQuery + " and '" + folderId + "' in parents";

            FileList result = driveService.files().list()
                    .setQ(query)
                    .setPageSize(50)
                    .setFields("files(id,name,size,mimeType,createdTime,modifiedTime,parents,webContentLink)")
                    .setOrderBy("createdTime desc")
                    .execute();

            for (File file : result.getFiles()) {
                Map<String, Object> fileInfo = createFileInfoMap(file);
                results.add(fileInfo);
            }
        }

        return results;
    }

    /**
     * Elimina archivos duplicados por ID
     */
    private List<Map<String, Object>> removeDuplicates(List<Map<String, Object>> files) {
        Map<String, Map<String, Object>> uniqueFiles = new LinkedHashMap<>();

        for (Map<String, Object> file : files) {
            String id = (String) file.get("id");
            if (!uniqueFiles.containsKey(id)) {
                uniqueFiles.put(id, file);
            }
        }

        return new ArrayList<>(uniqueFiles.values());
    }


    /**
     * Obtiene carpetas de forma recursiva
     */
    @Override
    public List<Map<String, Object>> getFoldersRecursive(String parentId, int maxDepth) throws IOException {
        List<Map<String, Object>> allFolders = new ArrayList<>();
        getFoldersRecursiveHelper(parentId, maxDepth, 0, allFolders);
        return allFolders;
    }

    /**
     * Método auxiliar recursivo para obtener carpetas
     */
    private void getFoldersRecursiveHelper(String parentId, int maxDepth, int currentDepth,
                                           List<Map<String, Object>> allFolders) throws IOException {
        if (currentDepth >= maxDepth) {
            return;
        }

        String query = "trashed=false and mimeType='application/vnd.google-apps.folder'";
        if (parentId != null && !parentId.isEmpty()) {
            query += " and '" + parentId + "' in parents";
        }

        FileList result = driveService.files().list()
                .setQ(query)
                .setPageSize(100)
                .setFields("files(id,name,createdTime,modifiedTime,parents)")
                .setOrderBy("name")
                .execute();

        for (File folder : result.getFiles()) {
            Map<String, Object> folderInfo = new HashMap<>();
            folderInfo.put("id", folder.getId());
            folderInfo.put("name", folder.getName());
            folderInfo.put("type", "folder");
            folderInfo.put("depth", currentDepth);
            folderInfo.put("parentId", parentId);
            folderInfo.put("createdTime", folder.getCreatedTime());
            folderInfo.put("modifiedTime", folder.getModifiedTime());

            allFolders.add(folderInfo);

            // Recursión para subcarpetas
            getFoldersRecursiveHelper(folder.getId(), maxDepth, currentDepth + 1, allFolders);
        }
    }

    /**
     * 🔧 Expone el servicio de Drive para operaciones avanzadas
     * (Agregar este método en GoogleDriveServiceImpl.java)
     */
    public Drive getDriveService() {
        return driveService;
    }


    @Override
    public Map<String, Object> getAllAudioFiles(
            LocalDate fecha,
            LocalDate fechaInicio,
            LocalDate fechaFin,
            String numeroAgente,
            String numeroMovil,
            boolean incluirProcesados,
            int page,
            int size) throws IOException {

        log.info("🎵 Obteniendo TODOS los audios de Google Drive - fecha: {}, rango: {}-{}, agente: {}, móvil: {}",
                fecha, fechaInicio, fechaFin, numeroAgente, numeroMovil);

        try {
            // 1. Obtener archivos según filtros
            List<Map<String, Object>> allAudioFiles = new ArrayList<>();

            if (fecha != null) {
                // Búsqueda por fecha específica
                allAudioFiles = getAllAudioFilesByDate(fecha.toString());
            } else if (fechaInicio != null && fechaFin != null) {
                // Búsqueda por rango de fechas
                allAudioFiles = getAllAudioFilesByDateRange(fechaInicio, fechaFin);
            } else {
                // Obtener todos los audios (últimos 30 días por defecto)
                LocalDate endDate = LocalDate.now();
                LocalDate startDate = endDate.minusDays(30);
                allAudioFiles = getAllAudioFilesByDateRange(startDate, endDate);
            }

            log.info("📁 Total de archivos de audio encontrados: {}", allAudioFiles.size());

            // 2. Enriquecer cada archivo con información extraída y verificar leads
            List<Map<String, Object>> enrichedFiles = new ArrayList<>();

            for (Map<String, Object> audioFile : allAudioFiles) {
                Map<String, Object> enrichedFile = enrichAudioFileInfo(audioFile);

                // Aplicar filtros adicionales
                if (shouldIncludeAudio(enrichedFile, numeroAgente, numeroMovil, incluirProcesados)) {
                    enrichedFiles.add(enrichedFile);
                }
            }

            log.info("🔍 Archivos después de filtros: {}", enrichedFiles.size());

            // 3. Aplicar paginación manual
            int totalElements = enrichedFiles.size();
            int totalPages = (int) Math.ceil((double) totalElements / size);
            int startIndex = page * size;
            int endIndex = Math.min(startIndex + size, totalElements);

            List<Map<String, Object>> pageContent = new ArrayList<>();
            if (startIndex < totalElements) {
                pageContent = enrichedFiles.subList(startIndex, endIndex);
            }

            // 4. Calcular estadísticas
            Map<String, Integer> stats = calculateAudioStatistics(enrichedFiles);

            // 5. Construir respuesta
            Map<String, Object> response = new HashMap<>();
            response.put("content", pageContent);
            response.put("totalElements", totalElements);
            response.put("totalPages", totalPages);
            response.put("currentPage", page);
            response.put("pageSize", size);
            response.put("hasNext", page < totalPages - 1);
            response.put("hasPrevious", page > 0);

            // Estadísticas
            response.put("audiosConLead", stats.get("conLead"));
            response.put("audiosSinLead", stats.get("sinLead"));
            response.put("audiosProcesados", stats.get("procesados"));
            response.put("audiosPendientes", stats.get("pendientes"));

            // Metadatos adicionales
            response.put("fechaConsulta", LocalDateTime.now());
            response.put("filtrosAplicados", buildFilterSummary(fecha, fechaInicio, fechaFin, numeroAgente, numeroMovil));

            log.info("✅ Respuesta construida - Total: {}, Página: {}/{}", totalElements, page + 1, totalPages);

            return response;

        } catch (Exception e) {
            log.error("❌ Error obteniendo todos los audios: {}", e.getMessage(), e);
            throw new IOException("Error al obtener archivos de audio: " + e.getMessage(), e);
        }
    }

    /**
     * Obtiene archivos de audio por rango de fechas
     */
    private List<Map<String, Object>> getAllAudioFilesByDateRange(LocalDate fechaInicio, LocalDate fechaFin) throws IOException {
        List<Map<String, Object>> allFiles = new ArrayList<>();

        // Iterar por cada día en el rango
        LocalDate currentDate = fechaInicio;
        while (!currentDate.isAfter(fechaFin)) {
            try {
                List<Map<String, Object>> dailyFiles = getAllAudioFilesByDate(currentDate.toString());
                allFiles.addAll(dailyFiles);
            } catch (Exception e) {
                log.warn("⚠️ Error obteniendo archivos para fecha {}: {}", currentDate, e.getMessage());
            }
            currentDate = currentDate.plusDays(1);
        }

        return allFiles;
    }

    /**
     * Enriquece la información del archivo de audio
     */
    private Map<String, Object> enrichAudioFileInfo(Map<String, Object> audioFile) {
        String fileName = (String) audioFile.get("name");

        // Extraer móvil y agente del nombre
        Map<String, String> parsedInfo = parseFileName(fileName);
        audioFile.put("parsedInfo", parsedInfo);
        audioFile.put("extractedMovil", parsedInfo.get("numeroMovil"));
        audioFile.put("extractedAgente", parsedInfo.get("numeroAgente"));
        audioFile.put("extractedFecha", parsedInfo.get("fecha"));

        // Verificar si tiene lead asociado
        boolean hasLead = false;
        Long leadId = null;
        boolean isTranscribed = false;

        if (parsedInfo.get("numeroMovil") != null && parsedInfo.get("numeroAgente") != null) {
            ClienteResidencial lead = findLeadForAudio(
                    parsedInfo.get("numeroMovil"),
                    parsedInfo.get("numeroAgente"),
                    parsedInfo.get("fecha")
            );

            if (lead != null) {
                hasLead = true;
                leadId = lead.getId();
                isTranscribed = lead.getTextoTranscription() != null && !lead.getTextoTranscription().isEmpty();
            }
        }

        audioFile.put("hasLead", hasLead);
        audioFile.put("leadId", leadId);
        audioFile.put("isTranscribed", isTranscribed);

        // Agregar carpeta padre si está disponible
        List<String> parents = (List<String>) audioFile.get("parents");
        if (parents != null && !parents.isEmpty()) {
            try {
                File parentFolder = driveService.files().get(parents.get(0))
                        .setFields("name")
                        .execute();
                audioFile.put("parentFolder", parentFolder.getName());
            } catch (Exception e) {
                audioFile.put("parentFolder", "Desconocida");
            }
        }

        return audioFile;
    }

    /**
     * Busca un lead existente para el audio
     */
    private ClienteResidencial findLeadForAudio(String movil, String agente, String fecha) {
        try {
            if (fecha != null) {
                LocalDate fechaAudio = LocalDate.parse(fecha);
                LocalDateTime startOfDay = fechaAudio.atStartOfDay();
                LocalDateTime endOfDay = fechaAudio.atTime(LocalTime.MAX);

                List<ClienteResidencial> leads = clienteResidencialRepository
                        .findByMovilContactoAndNumeroAgenteAndFechaCreacionBetween(
                                movil, normalizeAgentNumber(agente), startOfDay, endOfDay);

                return leads.isEmpty() ? null : leads.get(0);
            }
            return null;
        } catch (Exception e) {
            log.debug("Error buscando lead para audio: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Determina si incluir el audio según los filtros
     */
    private boolean shouldIncludeAudio(Map<String, Object> audioFile,
                                       String numeroAgente,
                                       String numeroMovil,
                                       boolean incluirProcesados) {
        // Filtrar por agente
        if (numeroAgente != null && !numeroAgente.isEmpty()) {
            String extractedAgente = (String) audioFile.get("extractedAgente");
            if (extractedAgente == null || !extractedAgente.equals(normalizeAgentNumber(numeroAgente))) {
                return false;
            }
        }

        // Filtrar por móvil
        if (numeroMovil != null && !numeroMovil.isEmpty()) {
            String extractedMovil = (String) audioFile.get("extractedMovil");
            if (extractedMovil == null || !extractedMovil.contains(numeroMovil)) {
                return false;
            }
        }

        // Filtrar por estado de procesamiento
        if (!incluirProcesados) {
            Boolean isTranscribed = (Boolean) audioFile.get("isTranscribed");
            if (isTranscribed != null && isTranscribed) {
                return false;
            }
        }

        return true;
    }

    /**
     * Calcula estadísticas de los audios
     */
    private Map<String, Integer> calculateAudioStatistics(List<Map<String, Object>> audioFiles) {
        Map<String, Integer> stats = new HashMap<>();

        int conLead = 0;
        int sinLead = 0;
        int procesados = 0;
        int pendientes = 0;

        for (Map<String, Object> audio : audioFiles) {
            Boolean hasLead = (Boolean) audio.get("hasLead");
            Boolean isTranscribed = (Boolean) audio.get("isTranscribed");

            if (hasLead != null && hasLead) {
                conLead++;
                if (isTranscribed != null && isTranscribed) {
                    procesados++;
                } else {
                    pendientes++;
                }
            } else {
                sinLead++;
                pendientes++; // Los sin lead también están pendientes
            }
        }

        stats.put("conLead", conLead);
        stats.put("sinLead", sinLead);
        stats.put("procesados", procesados);
        stats.put("pendientes", pendientes);

        return stats;
    }

    /**
     * Construye resumen de filtros aplicados
     */
    private Map<String, Object> buildFilterSummary(LocalDate fecha, LocalDate fechaInicio,
                                                   LocalDate fechaFin, String numeroAgente,
                                                   String numeroMovil) {
        Map<String, Object> filters = new HashMap<>();

        if (fecha != null) {
            filters.put("fecha", fecha.toString());
        } else if (fechaInicio != null && fechaFin != null) {
            filters.put("fechaInicio", fechaInicio.toString());
            filters.put("fechaFin", fechaFin.toString());
        }

        if (numeroAgente != null && !numeroAgente.isEmpty()) {
            filters.put("numeroAgente", numeroAgente);
        }

        if (numeroMovil != null && !numeroMovil.isEmpty()) {
            filters.put("numeroMovil", numeroMovil);
        }

        return filters;
    }


    @Override
    public Map<String,Object> listarAudiosPorAgentes(
            List<String> agentes,
            LocalDate fecha,
            LocalDate fechaInicio,
            LocalDate fechaFin,
            int page,
            int size
    ) {
        if (agentes == null || agentes.isEmpty()) {
            return applyPagination(new ArrayList<>(), page, size);
        }

        StringBuilder q = new StringBuilder();
        q.append("(");
        for (int i = 0; i < agentes.size(); i++) {
            if (i > 0) q.append(" or ");
            q.append("name contains '").append(agentes.get(i)).append("'");
        }
        q.append(")");

        if (fecha != null) {
            LocalDateTime startOfDay = fecha.atStartOfDay();
            LocalDateTime endOfDay = fecha.plusDays(1).atStartOfDay();
            q.append(" and modifiedTime >= '").append(startOfDay).append("Z'");
            q.append(" and modifiedTime < '").append(endOfDay).append("Z'");
        } else if (fechaInicio != null && fechaFin != null) {
            LocalDateTime start = fechaInicio.atStartOfDay();
            LocalDateTime end = fechaFin.plusDays(1).atStartOfDay();
            q.append(" and modifiedTime >= '").append(start).append("Z'");
            q.append(" and modifiedTime < '").append(end).append("Z'");
        }

        try {
            // This is the logic that was missing, now restored
            List<Map<String,Object>> allFoundFiles = new ArrayList<>();
            String pageToken = null;

            do {
                FileList result = driveService.files().list()
                        .setQ(q.toString())
                        .setPageSize(1000)
                        .setPageToken(pageToken)
                        .setFields("nextPageToken, files(id, name, createdTime, webContentLink)")
                        .execute();

                if (result.getFiles() != null) {
                    for (File f : result.getFiles()) {
                        Map<String, Object> info = new HashMap<>();
                        info.put("fileId", f.getId());
                        info.put("fileName", f.getName());
                        info.put("createdTime", f.getCreatedTime().toStringRfc3339());
                        info.put("downloadLink", f.getWebContentLink());

                        Map<String, String> parsedInfo = parseFileName(f.getName());
                        info.put("extractedMovil", parsedInfo.get("numeroMovil"));
                        info.put("extractedAgente", parsedInfo.get("numeroAgente"));
                        allFoundFiles.add(info);
                    }
                }
                pageToken = result.getNextPageToken();
            } while (pageToken != null);

            return applyPagination(allFoundFiles, page, size);

        } catch (Exception e) {
            throw new RuntimeException("Error buscando audios en Drive: " + e.getMessage(), e);
        }
    }


    /**
     * Helper method to apply manual pagination. This should be the ONLY version.
     */
    private Map<String, Object> applyPagination(List<Map<String, Object>> allResults, int page, int size) {
        Map<String, Object> response = new HashMap<>();
        int totalElements = allResults.size();
        int totalPages = (int) Math.ceil((double) totalElements / size);
        int startIndex = page * size;
        int endIndex = Math.min(startIndex + size, totalElements);

        List<Map<String, Object>> pageContent = new ArrayList<>();
        if (startIndex < totalElements) {
            pageContent = allResults.subList(startIndex, endIndex);
        }

        response.put("content", pageContent);
        response.put("totalElements", totalElements);
        response.put("totalPages", totalPages);
        response.put("currentPage", page);
        response.put("pageSize", size);
        response.put("hasNext", page < totalPages - 1);
        response.put("hasPrevious", page > 0);

        return response;
    }
}