package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.auth.*;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.security.jwt.JwtProvider;
import com.midas.crm.service.AsistenciaService;
import com.midas.crm.service.AuthService;
import com.midas.crm.service.AuthenticationService;
import com.midas.crm.service.UserConnectionService;
import com.midas.crm.service.UserService;
import com.midas.crm.utils.MidasErrorMessage;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl implements AuthService {

    private final UserService userService;
    private final AuthenticationService authenticationService;
    private final UserConnectionService userConnectionService;
    private final AsistenciaService asistenciaService;
    private final JwtProvider jwtProvider;
    private final UserRepository userRepository;

    @Override
    public AuthResponse signIn(AuthRequest request) {
        if (request.getUsername().isBlank()) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_INVALID_DATA);
        }

        User toAuth = new User();
        toAuth.setUsername(request.getUsername());
        toAuth.setPassword(request.getPassword());

        User authUser;
        try {
            authUser = authenticationService.signInAndReturnJWT(toAuth);
        } catch (BadCredentialsException | UsernameNotFoundException ex) {
            throw new BadCredentialsException("Usuario o contraseña incorrectos");
        }

        userConnectionService.connectUser(authUser.getId());

        // 🔥 SIMPLIFICADO: Solo registrar entrada si no existe para hoy
        try {
            boolean yaRegistroHoy = asistenciaService.yaRegistroAsistenciaHoy(authUser.getId());

            if (!yaRegistroHoy) {
                asistenciaService.registrarAsistenciaAutomatica(
                        authUser.getId(),
                        "Sistema",
                        "Login",
                        "Sistema"
                );
                log.info("✅ Entrada registrada automáticamente para usuario ID: {}", authUser.getId());
            } else {
                log.info("ℹ️  Usuario ID: {} ya registró entrada hoy", authUser.getId());
            }
        } catch (Exception e) {
            log.error("❌ Error al registrar entrada automática: {}", e.getMessage());
        }

        User full = findByIdWithSedeAndCoordinador(authUser.getId())
                .orElseThrow(() -> new UsernameNotFoundException("Usuario no encontrado"));

        return buildAuthResponse(authUser, full, authUser.getToken());
    }

    @Override
    public AuthResponse refreshToken(HttpServletRequest request) {
        Claims claims = jwtProvider.extractClaims(request);
        if (claims == null || claims.getSubject() == null) {
            throw new MidasExceptions(MidasErrorMessage.TOKEN_INVALIDO);
        }

        String username = claims.getSubject();
        User user = userService.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("Usuario no encontrado"));

        if (!"A".equals(user.getEstado())) {
            throw new UsernameNotFoundException("Usuario inactivo");
        }

        String token = jwtProvider.generateToken(UserPrincipal.build(user));

        User full = findByIdWithSedeAndCoordinador(user.getId())
                .orElseThrow(() -> new UsernameNotFoundException("Usuario no encontrado"));

        return buildAuthResponse(user, full, token);
    }

    @Override
    public void signUp(SignUpRequest req) {
        boolean exists = userService.findByUsername(req.getUsername()).isPresent()
                || userService.findByDni(req.getDni()).isPresent()
                || (req.getEmail() != null && userService.findByEmail(req.getEmail()).isPresent());

        if (exists) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_ALREADY_EXISTS);
        }

        User u = new User();
        u.setUsername(req.getUsername());
        u.setPassword(req.getPassword());
        u.setNombre(req.getNombre());
        u.setApellido(req.getApellido());
        u.setDni(req.getDni());
        u.setTelefono(req.getTelefono());

        String email = req.getEmail();
        u.setEmail((email == null || email.isBlank()) ? req.getUsername() + "@midas.pe" : email);

        userService.saveUser(u);
    }

    @Override
    public void signOut(SignOutRequest req) {
        if (req.getUserId() == null || req.getUserId() <= 0) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_INVALID_DATA);
        }

        // 🔥 SIMPLIFICADO: Solo desconectar, la salida se marca manualmente en el frontend
        log.info("ℹ️  Sesión cerrada para usuario ID: {} (salida manual requerida)", req.getUserId());

        userConnectionService.disconnectUser(req.getUserId());
    }

    @Override
    public Optional<User> findByIdWithSede(Long id) {
        return userRepository.findByIdWithSede(id);
    }

    @Override
    public Optional<User> findByIdWithSedeAndCoordinador(Long id) {
        return userRepository.findByIdWithSedeAndCoordinador(id);
    }

    private AuthResponse buildAuthResponse(User user, User fullUser, String token) {
        AuthResponse resp = new AuthResponse();
        resp.setToken(token);
        resp.setUserId(user.getId());
        resp.setUsername(user.getUsername());
        resp.setNombre(user.getNombre());
        resp.setApellido(user.getApellido());
        resp.setEmail(user.getEmail());
        resp.setRole(user.getRole().name());
        resp.setPicture(fullUser.getPicture());

        if (fullUser.getSede() != null) {
            resp.setSedeId(fullUser.getSede().getId());
            resp.setSede(fullUser.getSede().getNombre());
        }

        if (user.getRole() == Role.ASESOR && fullUser.getCoordinador() != null) {
            resp.setCoordinador(new CoordinadorLoginDTO(
                    fullUser.getCoordinador().getUsername(),
                    fullUser.getCoordinador().getNombre(),
                    fullUser.getCoordinador().getApellido(),
                    fullUser.getCoordinador().getId()
            ));
        }

        return resp;
    }
}