package com.midas.crm.entity.DTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO para la respuesta paginada de estadísticas por sede
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EstadisticaSedePaginadaResponse {

    private List<EstadisticaSedeDTO> estadisticas;
    private int currentPage;
    private int totalPages;
    private long totalElements;
    private int pageSize;
    private boolean hasNext;
    private boolean hasPrevious;

    // Constructor adicional para facilitar la creación desde Page
    public EstadisticaSedePaginadaResponse(List<EstadisticaSedeDTO> estadisticas,
                                           int currentPage,
                                           int totalPages,
                                           long totalElements,
                                           int pageSize) {
        this.estadisticas = estadisticas;
        this.currentPage = currentPage;
        this.totalPages = totalPages;
        this.totalElements = totalElements;
        this.pageSize = pageSize;
        this.hasNext = currentPage < totalPages - 1;
        this.hasPrevious = currentPage > 0;
    }
}