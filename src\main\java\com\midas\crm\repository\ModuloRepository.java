package com.midas.crm.repository;

import com.midas.crm.entity.Curso;
import com.midas.crm.entity.Modulo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ModuloRepository extends JpaRepository<Modulo, Long> {
    List<Modulo> findByCursoIdOrderByOrdenAsc(Long cursoId);
    List<Modulo> findByCursoAndEstadoOrderByOrdenAsc(Curso curso, String estado);
    Optional<Modulo> findByTituloAndCursoId(String titulo, Long cursoId);
    boolean existsByTituloAndCursoId(String titulo, Long cursoId);
}
