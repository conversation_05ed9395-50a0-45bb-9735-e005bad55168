# 📊 Guía de Endpoints para Grafana - CRM Call Center

## 🎯 Identificación de Controladores en Métricas

### 📋 Endpoints Principales por Funcionalidad

#### 🔥 **CRÍTICOS - Alta Frecuencia de Uso**
```
/api/leads/**                    → LeadController (Gestión de leads)
/api/notifications/**            → NotificationController (Notificaciones)
/api/asesores/**                → AsesorController (Gestión de asesores)
/api/coordinadores/**           → CoordinadorController (Gestión de coordinadores)
```

#### 📚 **MODERADOS - Uso Regular**
```
/api/anuncios/**                → AnuncioController (Sistema de anuncios)
/api/manuales/**                → ManualController (Gestión de manuales)
/api/calendar/**                → CalendarController (Sistema de calendario)
/api/cursos/**                  → CursoController (Sistema de cursos)
```

#### ⚙️ **ESPECIALIZADOS - Uso Específico**
```
/api/transcription-queue/**     → TranscriptionQueueController (Transcripciones)
/api/audios-sin-lead/**        → AudioSinLeadController (Audios sin lead)
/api/cuestionarios/**          → CuestionarioController (Cuestionarios)
/api/preguntas/**              → PreguntaController (Preguntas)
```

## 🔍 Queries Específicas para tu Dashboard

### 1. **Usuarios/Conexiones Activas (Panel 50)**
```promql
# Threads activos de Tomcat
tomcat_threads_busy_threads{application="crm-leads"}

# O conexiones HTTP activas
http_server_requests_active_seconds_count{application="crm-leads"}

# O sesiones activas
tomcat_sessions_active_current_sessions{application="crm-leads"}
```

### 2. **Conexiones de Base de Datos (Panel 18→15)**
```promql
# Conexiones HikariCP activas
hikaricp_connections_active{application="crm-leads"}

# Conexiones HikariCP en uso
hikaricp_connections{application="crm-leads"}

# Pool de conexiones total
hikaricp_connections_max{application="crm-leads"}
```

### 3. **Errores/Fallos (Panel 0)**
```promql
# Errores HTTP 5xx
rate(http_server_requests_seconds_count{status=~"5..",application="crm-leads"}[5m])

# Errores HTTP 4xx
rate(http_server_requests_seconds_count{status=~"4..",application="crm-leads"}[5m])

# Total de errores
rate(http_server_requests_seconds_count{status=~"[45]..",application="crm-leads"}[5m])
```

### 4. **Latencia por Controlador**
```promql
# Tiempo promedio de respuesta por endpoint
rate(http_server_requests_seconds_sum[5m]) / rate(http_server_requests_seconds_count[5m])

# Filtrar por controlador específico:
rate(http_server_requests_seconds_sum{uri=~"/api/leads/.*"}[5m]) / rate(http_server_requests_seconds_count{uri=~"/api/leads/.*"}[5m])
```

### 2. **Throughput por Funcionalidad**
```promql
# Requests por segundo por controlador
rate(http_server_requests_seconds_count[5m])

# Top 5 endpoints más utilizados:
topk(5, rate(http_server_requests_seconds_count[5m]))
```

### 3. **Errores por Controlador**
```promql
# Tasa de errores 5xx por endpoint
rate(http_server_requests_seconds_count{status=~"5.."}[5m])

# Errores 4xx (problemas de cliente)
rate(http_server_requests_seconds_count{status=~"4.."}[5m])
```

### 4. **Peticiones Activas (Call Center)**
```promql
# Peticiones POST activas (crítico para call center)
http_server_requests_active_seconds_max{method="POST"}

# Peticiones largas (>30 segundos)
http_server_requests_active_seconds_max > 30
```

## 🎨 Configuración de Dashboard Grafana

### Panel 1: **Latencia por Controlador**
- **Tipo**: Time Series
- **Query**: `rate(http_server_requests_seconds_sum[5m]) / rate(http_server_requests_seconds_count[5m])`
- **Legend**: `{{uri}} - {{method}}`
- **Threshold**: Amarillo >200ms, Rojo >500ms

### Panel 2: **Throughput Call Center**
- **Tipo**: Stat
- **Query**: `sum(rate(http_server_requests_seconds_count[5m]))`
- **Unit**: req/sec
- **Threshold**: Verde >10, Amarillo >50, Rojo >100

### Panel 3: **Endpoints Más Lentos**
- **Tipo**: Table
- **Query**: `topk(10, avg_over_time(http_server_requests_seconds_max[5m]))`
- **Columns**: Endpoint, Latencia Máxima, Método

### Panel 4: **Errores por Funcionalidad**
- **Tipo**: Bar Chart
- **Query**: `sum by (uri) (rate(http_server_requests_seconds_count{status=~"[45].."}[5m]))`

## 🚨 Alertas Recomendadas para Call Center

### Alerta 1: **Latencia Alta en Leads**
```yaml
alert: HighLatencyLeads
expr: rate(http_server_requests_seconds_sum{uri=~"/api/leads/.*"}[5m]) / rate(http_server_requests_seconds_count{uri=~"/api/leads/.*"}[5m]) > 1
for: 2m
labels:
  severity: warning
annotations:
  summary: "Latencia alta en gestión de leads"
  description: "Los endpoints de leads tienen latencia > 1s por {{ $value }}s"
```

### Alerta 2: **Muchas Peticiones Activas**
```yaml
alert: TooManyActiveRequests
expr: http_server_requests_active_seconds_max{method="POST"} > 10
for: 1m
labels:
  severity: critical
annotations:
  summary: "Demasiadas peticiones POST activas"
  description: "{{ $value }} peticiones POST activas simultáneas"
```

## 📈 Variables de Dashboard Recomendadas

```
$controller = label_values(http_server_requests_seconds_count, uri)
$method = label_values(http_server_requests_seconds_count, method)
$status = label_values(http_server_requests_seconds_count, status)
$instance = label_values(http_server_requests_seconds_count, instance)
```

## 🎯 Filtros Útiles para Call Center

### Por Funcionalidad:
```
Leads: uri=~"/api/leads/.*"
Notificaciones: uri=~"/api/notifications/.*"
Asesores: uri=~"/api/asesores/.*"
Coordinadores: uri=~"/api/coordinadores/.*"
```

### Por Tipo de Operación:
```
Consultas: method="GET"
Creación: method="POST"
Actualización: method="PUT"
Eliminación: method="DELETE"
```

### Por Estado:
```
Exitosas: status=~"2.."
Errores Cliente: status=~"4.."
Errores Servidor: status=~"5.."
```

---

**💡 Tip**: Usa estas queries como base y ajústalas según las necesidades específicas de tu call center. Los endpoints de leads y notificaciones son los más críticos para el rendimiento.
