package com.midas.crm.controller;

import com.midas.crm.service.CacheWarmupService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Controlador para gestión y monitoreo del cache Redis
 * Solo accesible para administradores
 */
@RestController
@RequestMapping("/api/cache")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN') or hasRole('PROGRAMADOR')")
public class CacheManagementController {

    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheWarmupService cacheWarmupService;

    /**
     * Obtiene información general del cache
     */
    @GetMapping("/info")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getCacheInfo() {
        try {
            Map<String, Object> info = new HashMap<>();
            
            // Información del CacheManager
            info.put("cacheManagerType", cacheManager.getClass().getSimpleName());
            info.put("cacheNames", cacheManager.getCacheNames());
            
            // Información de Redis
            try {
                Set<String> keys = redisTemplate.keys("midas:crm:*");
                info.put("redisKeysCount", keys != null ? keys.size() : 0);
                info.put("redisConnected", true);

                // Muestra algunas claves de ejemplo (máximo 10)
                if (keys != null && !keys.isEmpty()) {
                    List<String> sampleKeys = keys.stream()
                            .limit(10)
                            .sorted()
                            .toList();
                    info.put("sampleKeys", sampleKeys);
                }
            } catch (Exception e) {
                info.put("redisConnected", false);
                info.put("redisError", e.getMessage());
            }
            
            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Información del cache obtenida correctamente",
                    info
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error obteniendo información del cache: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Lista todas las claves de Redis con el prefijo de la aplicación
     */
    @GetMapping("/keys")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getRedisKeys(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {
        try {
            Set<String> allKeys = redisTemplate.keys("*");

            if (allKeys == null || allKeys.isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("keys", Collections.emptyList());
                result.put("totalKeys", 0);
                result.put("page", page);
                result.put("size", size);

                return ResponseEntity.ok(new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "No se encontraron claves en Redis",
                        result
                ));
            }

            // Filtrar solo las claves de la aplicación
            List<String> appKeys = allKeys.stream()
                    .filter(key -> key.contains("midas") || key.contains("anuncio") || key.contains("notification"))
                    .sorted()
                    .toList();

            // Paginación manual
            int start = page * size;
            int end = Math.min(start + size, appKeys.size());
            List<String> paginatedKeys = appKeys.subList(start, end);

            Map<String, Object> result = new HashMap<>();
            result.put("keys", paginatedKeys);
            result.put("totalKeys", appKeys.size());
            result.put("allRedisKeys", allKeys.size());
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) appKeys.size() / size));

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Claves de Redis obtenidas correctamente",
                    result
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error obteniendo claves de Redis: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Ejecuta warmup manual del cache
     */
    @PostMapping("/warmup")
    public ResponseEntity<GenericResponse<String>> warmupCache() {
        try {
            cacheWarmupService.refreshAllCache();
            
            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Warmup del cache ejecutado correctamente",
                    "Cache pre-cargado con datos estáticos"
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error ejecutando warmup del cache: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Limpia una clave específica del cache
     */
    @DeleteMapping("/key/{keyName}")
    public ResponseEntity<GenericResponse<String>> evictCacheKey(@PathVariable String keyName) {
        try {
            // Buscar la clave con el prefijo
            String fullKey = "midas:crm:" + keyName;
            Boolean deleted = redisTemplate.delete(fullKey);
            
            if (Boolean.TRUE.equals(deleted)) {
                return ResponseEntity.ok(new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "Clave eliminada del cache correctamente",
                        "Clave: " + fullKey
                ));
            } else {
                return ResponseEntity.ok(new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "La clave no existía en el cache",
                        "Clave: " + fullKey
                ));
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error eliminando clave del cache: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Limpia todas las claves del cache de la aplicación
     */
    @DeleteMapping("/clear-all")
    public ResponseEntity<GenericResponse<String>> clearAllCache() {
        try {
            Set<String> keys = redisTemplate.keys("midas:crm:*");

            if (keys != null && !keys.isEmpty()) {
                Long deletedCount = redisTemplate.delete(keys);

                return ResponseEntity.ok(new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "Cache limpiado completamente",
                        "Claves eliminadas: " + deletedCount
                ));
            } else {
                return ResponseEntity.ok(new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "No había claves para eliminar",
                        "Cache ya estaba vacío"
                ));
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error limpiando el cache: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Obtiene estadísticas del cache
     */
    @GetMapping("/stats")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getCacheStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // Contar claves por tipo de cache
            Set<String> allKeys = redisTemplate.keys("midas:crm:*");
            Map<String, Integer> keysByType = new HashMap<>();

            if (allKeys != null) {
                for (String key : allKeys) {
                    String type = extractCacheType(key);
                    keysByType.put(type, keysByType.getOrDefault(type, 0) + 1);
                }
            }
            
            stats.put("totalKeys", allKeys != null ? allKeys.size() : 0);
            stats.put("keysByType", keysByType);
            stats.put("cacheNames", cacheManager.getCacheNames());
            
            cacheWarmupService.logCacheStats();
            
            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Estadísticas del cache obtenidas correctamente",
                    stats
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error obteniendo estadísticas del cache: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Extrae el tipo de cache de una clave Redis
     */
    private String extractCacheType(String key) {
        if (key.contains("anuncio")) return "anuncio";
        if (key.contains("notification")) return "notification";
        if (key.contains("cliente")) return "cliente";
        if (key.contains("manual")) return "manual";
        if (key.contains("curso")) return "curso";
        if (key.contains("asesor")) return "asesor";
        return "other";
    }

}
