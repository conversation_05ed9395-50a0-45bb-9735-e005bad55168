package com.midas.crm.listener;

import com.midas.crm.security.UserPrincipal;
import com.midas.crm.service.UserConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Listener para eventos de conexión/desconexión de WebSocket
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WebSocketEventListener {

    private final UserConnectionService userConnectionService;

    // Mapa para evitar procesar desconexiones duplicadas
    private final Map<Long, Long> processedDisconnections = new ConcurrentHashMap<>();

    // Tiempo de deduplicación para desconexiones (5 segundos)
    private static final long DISCONNECT_DEDUPLICATION_WINDOW = 5000;

    /**
     * Maneja el evento de conexión de WebSocket
     *
     * Nota: Ya no registramos automáticamente la conexión del usuario aquí,
     * ya que ahora lo hacemos explícitamente en el endpoint /app/user.connect
     * con el parámetro isRefresh para manejar mejor los refreshes.
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());

        // Obtener el usuario autenticado
        if (headerAccessor.getUser() instanceof UsernamePasswordAuthenticationToken auth) {
            if (auth.getPrincipal() instanceof UserPrincipal userPrincipal) {
                Long userId = userPrincipal.getId();
                String username = userPrincipal.getUsername();

                // Limpiar cualquier desconexión previa procesada para este usuario
                // Esto ayuda con las reconexiones después de reinicio de servidor
                processedDisconnections.remove(userId);

                // Solo registramos la sesión, pero no conectamos al usuario automáticamente
                // La conexión se hará explícitamente desde el cliente con /app/user.connect
                log.info("Sesión WebSocket establecida para usuario: {} (ID: {}). SessionId: {}",
                        username, userId, headerAccessor.getSessionId());
            }
        } else {
            // Conexión anónima
            log.info("Conexión WebSocket anónima establecida. SessionId: {}", headerAccessor.getSessionId());
        }
    }

    /**
     * Maneja el evento de desconexión de WebSocket
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();

        log.info("Evento de desconexión WebSocket recibido. SessionId: {}", sessionId);

        // Obtener el usuario autenticado
        if (headerAccessor.getUser() instanceof UsernamePasswordAuthenticationToken auth) {
            if (auth.getPrincipal() instanceof UserPrincipal userPrincipal) {
                Long userId = userPrincipal.getId();
                String username = userPrincipal.getUsername();

                // Verificar si esta desconexión ya fue procesada recientemente
                if (!isDuplicateDisconnection(userId)) {
                    log.info("Usuario autenticado desconectado. UserId: {}, Username: {}", userId, username);

                    try {
                        // Registrar la desconexión del usuario
                        userConnectionService.disconnectUser(userId);
                        log.info("Usuario desconectado de WebSocket: {}", username);
                    } catch (Exception e) {
                        log.error("Error al desconectar usuario {}: {}", userId, e.getMessage(), e);
                    }
                } else {
                    log.debug("Desconexión duplicada detectada para usuario: {} (ID: {}). Ignorando.", username,
                            userId);
                }
            }
        } else {
            // Desconexión anónima
            log.info("Conexión WebSocket anónima cerrada. SessionId: {}", sessionId);
        }
    }

    /**
     * Verifica si una desconexión de usuario es duplicada
     * Evita procesar múltiples desconexiones para el mismo usuario en un corto
     * período de tiempo
     *
     * @param userId ID del usuario que se está desconectando
     * @return true si es una desconexión duplicada, false en caso contrario
     */
    private boolean isDuplicateDisconnection(Long userId) {
        long now = System.currentTimeMillis();
        Long lastTime = processedDisconnections.put(userId, now);

        // Limpiar desconexiones antiguas periódicamente
        if (processedDisconnections.size() > 100) {
            processedDisconnections.entrySet()
                    .removeIf(entry -> now - entry.getValue() > DISCONNECT_DEDUPLICATION_WINDOW);
        }

        // Es duplicado si ya existe y fue procesado hace menos del tiempo de
        // deduplicación para desconexiones
        return lastTime != null && now - lastTime < DISCONNECT_DEDUPLICATION_WINDOW;
    }
}
