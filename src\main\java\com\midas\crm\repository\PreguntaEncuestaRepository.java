package com.midas.crm.repository;

import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.PreguntaEncuesta;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PreguntaEncuestaRepository extends JpaRepository<PreguntaEncuesta, Long> {

    // Buscar preguntas por encuesta ordenadas por orden
    List<PreguntaEncuesta> findByEncuestaOrderByOrdenAsc(Encuesta encuesta);
    List<PreguntaEncuesta> findByEncuestaIdOrderByOrdenAsc(Long encuestaId);

    // Buscar preguntas activas por encuesta
    List<PreguntaEncuesta> findByEncuestaAndEstadoOrderByOrdenAsc(Encuesta encuesta, String estado);
    List<PreguntaEncuesta> findByEncuestaIdAndEstadoOrderByOrdenAsc(Long encuestaId, String estado);

    // Contar preguntas por encuesta
    int countByEncuestaId(Long encuestaId);
}
