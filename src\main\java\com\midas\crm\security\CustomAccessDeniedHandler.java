package com.midas.crm.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.utils.MidasErrorMessage;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Manejador personalizado para errores de acceso denegado (403)
 * Devuelve respuestas en formato GenericResponse para mantener consistencia en la API
 */
@Component
@Slf4j
public class CustomAccessDeniedHandler implements AccessDeniedHandler {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                      AccessDeniedException accessDeniedException) throws IOException, ServletException {

        // Crear respuesta en formato GenericResponse
        GenericResponse<Object> errorResponse = new GenericResponse<>(
                GenericResponseConstants.ERROR,
                MidasErrorMessage.OPERACION_NO_PERMITIDA_LECTOR.getErrorMessage(),
                null
        );

        // Configurar la respuesta HTTP
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        // Escribir la respuesta JSON
        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
    }
}
