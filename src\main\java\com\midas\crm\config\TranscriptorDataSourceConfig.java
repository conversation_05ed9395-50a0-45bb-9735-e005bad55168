package com.midas.crm.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * Configuración de DataSources:
 * - DataSource principal (midascrm_db) para JPA/Hibernate
 * - DataSource secundario (transcriptor_db) para consultas SQL nativas
 *
 * ⚠️ IMPORTANTE: transcriptorDataSource es solo para consultas de LECTURA
 */
@Configuration
public class TranscriptorDataSourceConfig {

    // === DATASOURCE PRINCIPAL (midascrm_db) ===

    @Value("${spring.datasource.url}")
    private String primaryUrl;

    @Value("${spring.datasource.username}")
    private String primaryUsername;

    @Value("${spring.datasource.password}")
    private String primaryPassword;

    @Value("${spring.datasource.driver-class-name}")
    private String primaryDriverClassName;

    /**
     * DataSource principal para midascrm_db
     * Usado por JPA/Hibernate para todas las entidades (Anuncio, User, etc.)
     */
    @Bean
    @Primary
    public DataSource primaryDataSource() {
        HikariConfig config = new HikariConfig();

        // Configuración básica de conexión
        config.setJdbcUrl(primaryUrl);
        config.setUsername(primaryUsername);
        config.setPassword(primaryPassword);
        config.setDriverClassName(primaryDriverClassName);

        // Pool de conexiones para producción
        config.setConnectionTimeout(20000);
        config.setMinimumIdle(15);
        config.setMaximumPoolSize(50);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        config.setLeakDetectionThreshold(30000);

        return new HikariDataSource(config);
    }

    // === DATASOURCE SECUNDARIO (transcriptor_db) ===

    @Value("${transcriptor.datasource.url}")
    private String url;

    @Value("${transcriptor.datasource.username}")
    private String username;

    @Value("${transcriptor.datasource.password}")
    private String password;

    @Value("${transcriptor.datasource.driver-class-name}")
    private String driverClassName;

    /**
     * DataSource para la base de datos de transcripciones
     * Solo para consultas SQL nativas, no para entidades JPA
     */
    @Bean(name = "transcriptorDataSource")
    public DataSource transcriptorDataSource() {
        HikariConfig config = new HikariConfig();

        // Configuración básica de conexión
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName(driverClassName);

        // Pool de conexiones optimizado para consultas de lectura
        config.setConnectionTimeout(20000);
        config.setMinimumIdle(2);
        config.setMaximumPoolSize(10);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        config.setLeakDetectionThreshold(30000);

        // Configuraciones adicionales para MySQL
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");

        // Solo lectura para mayor seguridad
        config.setReadOnly(true);

        return new HikariDataSource(config);
    }
}
