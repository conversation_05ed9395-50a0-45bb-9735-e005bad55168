package com.midas.crm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

@Controller
@RequiredArgsConstructor
public class WebSocketController {

    private final SimpMessagingTemplate messagingTemplate;

    // Tópico para recibir y reenviar GETs
    @MessageMapping("/topic/get")
    public void handleGetTopic(String payload) {
        messagingTemplate.convertAndSend("/topic/public-get", payload);
    }

    // Tópico para recibir y reenviar POSTs
    @MessageMapping("/topic/post")
    public void handlePostTopic(String payload) {
        messagingTemplate.convertAndSend("/topic/public-post", payload);
    }
}
