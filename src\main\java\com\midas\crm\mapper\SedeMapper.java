package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.SedeDTO;
import com.midas.crm.entity.Sede;
import org.springframework.beans.BeanUtils;

public class SedeMapper {

    public static SedeDTO toDTO(Sede sede) {
        if (sede == null) {
            return null;
        }

        SedeDTO sedeDTO = new SedeDTO();
        BeanUtils.copyProperties(sede, sedeDTO);

        // Convertir estado (A/I) a activo (true/false)
        sedeDTO.setActivo("A".equals(sede.getEstado()));

        return sedeDTO;
    }

    public static Sede toEntity(SedeDTO sedeDTO) {
        if (sedeDTO == null) {
            return null;
        }

        Sede sede = new Sede();
        BeanUtils.copyProperties(sedeDTO, sede);

        // Convertir activo (true/false) a estado (A/I) si no se ha establecido el estado
        if (sede.getEstado() == null && sedeDTO.getActivo() != null) {
            sede.setEstado(sedeDTO.getActivo() ? "A" : "I");
        }

        return sede;
    }

    public static String getNombreSede(Sede sede) {
        return sede != null ? sede.getNombre() : null;
    }
}

