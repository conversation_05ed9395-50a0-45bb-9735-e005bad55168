package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Cuestionario;
import com.midas.crm.entity.DTO.cuestionario.PreguntaCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.PreguntaDTO;
import com.midas.crm.entity.DTO.cuestionario.PreguntaUpdateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaCreateDTO;
import com.midas.crm.entity.Pregunta;
import com.midas.crm.entity.Respuesta;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.PreguntaMapper;
import com.midas.crm.mapper.RespuestaMapper;
import com.midas.crm.repository.CuestionarioRepository;
import com.midas.crm.repository.PreguntaRepository;
import com.midas.crm.repository.RespuestaRepository;
import com.midas.crm.service.PreguntaService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PreguntaServiceImpl implements PreguntaService {

    private final PreguntaRepository preguntaRepository;
    private final CuestionarioRepository cuestionarioRepository;
    private final RespuestaRepository respuestaRepository;

    @Override
    @Transactional
    public PreguntaDTO createPregunta(PreguntaCreateDTO dto) {
        // Obtener el cuestionario
        Cuestionario cuestionario = cuestionarioRepository.findById(dto.getCuestionarioId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CUESTIONARIO_NOT_FOUND));

        // Crear la pregunta
        Pregunta pregunta = PreguntaMapper.toEntity(dto, cuestionario);
        Pregunta savedPregunta = preguntaRepository.save(pregunta);
        
        // Si hay respuestas, crearlas
        if (dto.getRespuestas() != null && !dto.getRespuestas().isEmpty()) {
            for (RespuestaCreateDTO respuestaDTO : dto.getRespuestas()) {
                respuestaDTO.setPreguntaId(savedPregunta.getId());
                Respuesta respuesta = RespuestaMapper.toEntity(respuestaDTO, savedPregunta);
                respuestaRepository.save(respuesta);
            }
        }
        
        // Recargar la pregunta con sus respuestas
        return PreguntaMapper.toDTO(preguntaRepository.findById(savedPregunta.getId()).orElse(savedPregunta));
    }

    @Override
    @Transactional(readOnly = true)
    public List<PreguntaDTO> listPreguntas() {
        return preguntaRepository.findAll().stream()
                .map(PreguntaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PreguntaDTO> listPreguntasByCuestionarioId(Long cuestionarioId) {
        return preguntaRepository.findByCuestionarioIdOrderByOrdenAsc(cuestionarioId).stream()
                .map(PreguntaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public PreguntaDTO getPreguntaById(Long id) {
        return preguntaRepository.findById(id)
                .map(PreguntaMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_NOT_FOUND));
    }

    @Override
    @Transactional
    public PreguntaDTO updatePregunta(Long id, PreguntaUpdateDTO dto) {
        Pregunta pregunta = preguntaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_NOT_FOUND));

        PreguntaMapper.updateEntity(pregunta, dto);
        
        return PreguntaMapper.toDTO(preguntaRepository.save(pregunta));
    }

    @Override
    @Transactional
    public void deletePregunta(Long id) {
        if (!preguntaRepository.existsById(id)) {
            throw new MidasExceptions(MidasErrorMessage.PREGUNTA_NOT_FOUND);
        }
        
        preguntaRepository.deleteById(id);
    }
}
