package com.midas.crm.config;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.gmail.Gmail;
import com.midas.crm.service.GoogleOAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.io.IOException;
import java.security.GeneralSecurityException;

/**
 * Configuración para el servicio de la API de Gmail.
 * Maneja la autenticación y construye el cliente de Gmail.
 */
@Configuration
@Slf4j
public class GmailConfig {

    private static final String APPLICATION_NAME = "MIDAS CRM Gmail";
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();

    private final GoogleOAuthService googleOAuthService;

    public GmailConfig(GoogleOAuthService googleOAuthService) {
        this.googleOAuthService = googleOAuthService;
    }

    /**
     * Crea el cliente del servicio de Gmail usando las credenciales OAuth existentes.
     * NOTA: Asegúrate de que los 'scopes' de Gmail (lectura, envío, etc.)
     * se hayan añadido a tu GoogleOAuthService y hayas vuelto a autorizar la aplicación.
     *
     * @return Una instancia autorizada del servicio de Gmail.
     * @throws GeneralSecurityException Si hay un problema de seguridad con el transporte.
     * @throws IOException Si las credenciales no se pueden leer o refrescar.
     */
    @Bean
    @Lazy
    public Gmail gmailService() throws GeneralSecurityException, IOException {

        // --- LÍNEA A AÑADIR ---
        // Esta línea crea el canal de comunicación HTTP seguro para la API.
        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();

        // --- El resto de tu código se queda igual ---
        if (!googleOAuthService.hasValidCredentials()) {
            log.error("La API de Gmail requiere credenciales OAuth válidas. " +
                    "Por favor, autoriza la aplicación a través del endpoint /oauth2/authorize.");
            throw new IllegalStateException("No se puede inicializar el servicio de Gmail: Faltan credenciales OAuth.");
        }

        Credential credential = googleOAuthService.getValidCredential();
        log.info("Credenciales OAuth para el servicio de Gmail obtenidas correctamente.");

        // Fíjate cómo se usa la variable HTTP_TRANSPORT aquí
        return new Gmail.Builder(HTTP_TRANSPORT, JSON_FACTORY, credential)
                .setApplicationName(APPLICATION_NAME)
                .build();
    }
}