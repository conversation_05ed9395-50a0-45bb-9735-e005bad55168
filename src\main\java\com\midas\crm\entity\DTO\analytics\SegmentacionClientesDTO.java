package com.midas.crm.entity.DTO.analytics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * DTO para representar la segmentación de clientes
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SegmentacionClientesDTO {
    // Segmentos principales
    private List<SegmentoDTO> segmentos;
    
    // Métricas por segmento
    private Map<String, MetricasSegmentoDTO> metricasPorSegmento;
    
    // Recomendaciones para cada segmento
    private Map<String, List<String>> recomendacionesPorSegmento;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class SegmentoDTO {
        private String nombre;
        private String descripcion;
        private long cantidadClientes;
        private double porcentajeTotal;
        private List<String> caracteristicasPrincipales;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MetricasSegmentoDTO {
        private double tasaConversion;
        private double valorPromedioVenta;
        private double frecuenciaCompra;
        private double tasaRetencion;
        private double potencialCrecimiento;
    }
}
