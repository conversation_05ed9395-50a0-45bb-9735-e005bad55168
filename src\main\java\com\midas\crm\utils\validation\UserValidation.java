package com.midas.crm.utils.validation;

import com.midas.crm.entity.User;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

public class UserValidation {

    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^[0-9]{9,15}$");

    public static List<String> validateUser(User user) {
        List<String> errors = new ArrayList<>();

        // Validar DNI
        if (user.getDni() != null && user.getDni().length() != 8) {
            errors.add("El DNI debe tener exactamente 8 caracteres");
        }

        // Validar username
        if (user.getUsername() != null && user.getUsername().length() < 3) {
            errors.add("El nombre de usuario debe tener al menos 3 caracteres");
        }

        // Validar nombre
        if (user.getNombre() != null && user.getNombre().length() < 2) {
            errors.add("El nombre debe tener al menos 2 caracteres");
        }

        // Validar apellido
        if (user.getApellido() != null && user.getApellido().length() < 2) {
            errors.add("El apellido debe tener al menos 2 caracteres");
        }

        // Validar contraseña
        if (user.getPassword() != null && user.getPassword().length() < 6) {
            errors.add("La contraseña debe tener al menos 6 caracteres");
        }

        // Validar sede
        if (user.getSede() == null) {
            errors.add("La sede es obligatoria");
        }

        return errors;
    }
}