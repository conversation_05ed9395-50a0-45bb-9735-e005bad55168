package com.midas.crm.controller;

import com.midas.crm.entity.DTO.queue.TranscriptionQueueMessage;
import com.midas.crm.entity.ClienteResidencial;
import org.springframework.data.domain.Page;
import com.midas.crm.service.TranscriptionQueueService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Controlador para gestionar las colas de transcripción automática
 */
@RestController
@RequestMapping("${api.route.transcription-queue}")
@RequiredArgsConstructor
@Slf4j
public class TranscriptionQueueController {

    private final TranscriptionQueueService transcriptionQueueService;

    /**
     * Procesa leads sin transcripción y los envía a la cola de transcripción
     * Permite filtrar por agente, traer todos los meses o solo un día concreto.
     */
    @PostMapping("/process-pending-leads")
    public ResponseEntity<GenericResponse<Map<String, Object>>> processPendingLeads(
            @RequestParam(defaultValue = "1") int batchSize,
            @RequestParam(required = false) String numeroAgente,
            @RequestParam(defaultValue = "false") boolean allDates,
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate selectedDate
    ) {
        log.info("▶ process-pending-leads → batchSize={}, agente='{}', allDates={}, selectedDate={}",
                batchSize, numeroAgente, allDates, selectedDate);

        try {
            CompletableFuture<Map<String, Object>> future = CompletableFuture
                    .supplyAsync(() -> transcriptionQueueService.processPendingLeads(
                            batchSize, numeroAgente, allDates, selectedDate
                    ));
            Map<String, Object> result = future.join();

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Procesamiento de leads iniciado correctamente",
                    result
            ));
        } catch (Exception e) {
            log.error("❌ Error en process-pending-leads", e);
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error al procesar leads: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Obtiene estadísticas de las colas (ahora sin parámetro currentMonthOnly,
     * se delega a la lógica del servicio con allDates/selectedDate si fuera necesario).
     */
    @GetMapping("/queue-stats")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getQueueStats(
            @RequestParam(defaultValue = "false") boolean allDates,
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate selectedDate
    ) {
        log.info("▶ queue-stats → allDates={}, selectedDate={}", allDates, selectedDate);
        try {
            Map<String, Object> stats = transcriptionQueueService.getQueueStatistics(allDates, selectedDate);
            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Estadísticas obtenidas correctamente",
                    stats
            ));
        } catch (Exception e) {
            log.error("❌ Error en queue-stats", e);
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error al obtener estadísticas: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Obtiene la lista de leads pendientes paginada, con filtros de agente,
     * rango completo o día concreto.
     */
    @GetMapping("/pending-leads")
    public ResponseEntity<GenericResponse<PendingLeadsPageDTO>> getPendingLeads(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String numeroAgente,
            @RequestParam(defaultValue = "false") boolean allDates,
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate selectedDate
    ) {
        log.info("▶ pending-leads → página={}, size={}, agente='{}', allDates={}, selectedDate={}",
                page, size, numeroAgente, allDates, selectedDate);

        try {
            Page<ClienteResidencial> pageDto = transcriptionQueueService
                    .getPendingLeadsFromDatabasePaginated(page, size, numeroAgente, allDates, selectedDate);

            PendingLeadsPageDTO dto = new PendingLeadsPageDTO(
                    pageDto.getContent(),
                    pageDto.getNumber(),
                    pageDto.getTotalElements(),
                    pageDto.getTotalPages()
            );

            String msg = pageDto.isEmpty()
                    ? "No se encontraron leads con esos criterios."
                    : "Leads pendientes obtenidos correctamente.";

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    msg,
                    dto
            ));
        } catch (Exception e) {
            log.error("❌ Error en pending-leads", e);
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error al obtener leads pendientes: " + e.getMessage(),
                    null
            ));
        }
    }


    /**
     * Reintenta el procesamiento de mensajes fallidos en la DLQ
     */
    @PostMapping("/retry-failed")
    public ResponseEntity<GenericResponse<Map<String, Object>>> retryFailedMessages(
            @RequestParam(defaultValue = "10") int maxRetries) {

        try {
            log.info("Reintentando mensajes fallidos. MaxRetries: {}", maxRetries);

            Map<String, Object> result = transcriptionQueueService.retryFailedMessages(maxRetries);

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Reintento de mensajes fallidos iniciado",
                    result
            ));

        } catch (Exception e) {
            log.error("Error al reintentar mensajes fallidos", e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al reintentar mensajes: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * Envía un lead específico a la cola de transcripción
     */
    @PostMapping("/send-to-queue/{leadId}")
    public ResponseEntity<GenericResponse<String>> sendLeadToQueue(@PathVariable Long leadId) {
        try {
            log.info("Enviando lead {} a la cola de transcripción", leadId);

            boolean sent = transcriptionQueueService.sendLeadToQueue(leadId);

            if (sent) {
                return ResponseEntity.ok(new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "Lead enviado a la cola correctamente",
                        "Lead ID: " + leadId
                ));
            } else {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "No se pudo enviar el lead a la cola",
                                null
                        ));
            }

        } catch (Exception e) {
            log.error("Error al enviar lead {} a la cola", leadId, e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al enviar lead a la cola: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * Procesa leads pendientes SOLO para transcripción (sin enviar a comparación)
     */
    @PostMapping("/process-transcription-only")
    public ResponseEntity<GenericResponse<Map<String, Object>>> processTranscriptionOnly(
            @RequestParam(defaultValue = "1") int batchSize,
            @RequestParam(required = false) String numeroAgente,
            @RequestParam(defaultValue = "false") boolean allDates,
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate selectedDate
    ) {
        log.info("▶ process-transcription-only → batchSize={}, agente='{}', allDates={}, selectedDate={}",
                batchSize, numeroAgente, allDates, selectedDate);

        try {
            CompletableFuture<Map<String, Object>> future = CompletableFuture
                    .supplyAsync(() -> transcriptionQueueService.processTranscriptionOnly(
                            batchSize, numeroAgente, allDates, selectedDate
                    ));
            Map<String, Object> result = future.join();

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Procesamiento de transcripción iniciado correctamente",
                    result
            ));
        } catch (Exception e) {
            log.error("❌ Error en process-transcription-only", e);
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error al procesar transcripciones: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Procesa leads que YA TIENEN transcripción SOLO para comparación
     */
    @PostMapping("/process-comparison-only")
    public ResponseEntity<GenericResponse<Map<String, Object>>> processComparisonOnly(
            @RequestParam(defaultValue = "1") int batchSize,
            @RequestParam(required = false) String numeroAgente,
            @RequestParam(defaultValue = "false") boolean allDates,
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate selectedDate
    ) {
        log.info("▶ process-comparison-only → batchSize={}, agente='{}', allDates={}, selectedDate={}",
                batchSize, numeroAgente, allDates, selectedDate);

        try {
            CompletableFuture<Map<String, Object>> future = CompletableFuture
                    .supplyAsync(() -> transcriptionQueueService.processComparisonOnly(
                            batchSize, numeroAgente, allDates, selectedDate
                    ));
            Map<String, Object> result = future.join();

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Procesamiento de comparación iniciado correctamente",
                    result
            ));
        } catch (Exception e) {
            log.error("❌ Error en process-comparison-only", e);
            return ResponseEntity.internalServerError().body(new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error al procesar comparaciones: " + e.getMessage(),
                    null
            ));
        }
    }

    /**
     * Pausa el procesamiento de las colas
     */
    @PostMapping("/pause")
    public ResponseEntity<GenericResponse<String>> pauseQueueProcessing() {
        try {
            transcriptionQueueService.pauseProcessing();

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Procesamiento de colas pausado",
                    "Las colas han sido pausadas correctamente"
            ));

        } catch (Exception e) {
            log.error("Error al pausar procesamiento de colas", e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al pausar procesamiento: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * Reanuda el procesamiento de las colas
     */
    @PostMapping("/resume")
    public ResponseEntity<GenericResponse<String>> resumeQueueProcessing() {
        try {
            transcriptionQueueService.resumeProcessing();

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Procesamiento de colas reanudado",
                    "Las colas han sido reanudadas correctamente"
            ));

        } catch (Exception e) {
            log.error("Error al reanudar procesamiento de colas", e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al reanudar procesamiento: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * 📊 Analiza la correspondencia entre archivos de audio y leads existentes
     * Útil para debugging y verificación de asociaciones correctas
     */
    @GetMapping("/analyze-audios-vs-leads")
    public ResponseEntity<GenericResponse<Map<String, Object>>> analyzeAudiosVsLeads(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha) {

        log.info("📊 Iniciando análisis de audios vs leads para fecha: {}", fecha);

        try {
            Map<String, Object> analysis = transcriptionQueueService.analizarAudiosVsLeads(fecha);

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Análisis de audios vs leads completado correctamente",
                    analysis
            ));

        } catch (Exception e) {
            log.error("❌ Error en análisis de audios vs leads para fecha {}", fecha, e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al analizar audios vs leads: " + e.getMessage(),
                            null
                    ));
        }
    }

    // DTO para la respuesta paginada de leads pendientes
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class PendingLeadsPageDTO {
        private List<ClienteResidencial> leads;
        private int currentPage;
        private long totalItems;
        private int totalPages;
    }
}
