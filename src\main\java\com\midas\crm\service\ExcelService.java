package com.midas.crm.service;

import com.midas.crm.entity.AudioSinLead;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;
import com.midas.crm.repository.SedeRepository;
import com.midas.crm.utils.validation.ExcelUserValidation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Service
public class ExcelService {

    @Autowired
    private SedeRepository sedeRepository;

    public List<User> leerUsuariosDesdeExcel(MultipartFile file, Role role) throws IOException {
        List<User> usuarios = new ArrayList<>();
        List<String> errores = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.iterator();

            // Saltar la primera fila (encabezados)
            if (rows.hasNext()) {
                rows.next();
            }

            int rowNumber = 2; // Comenzamos desde la fila 2 (después del encabezado)
            while (rows.hasNext()) {
                Row currentRow = rows.next();
                User user = new User();
                user.setFechaCreacion(LocalDateTime.now());
                user.setRole(role);
                user.setEstado("A");

                try {
                    // Leer cada celda según su posición
                    user.setUsername(getCellValueAsString(currentRow.getCell(0)));
                    user.setPassword(getCellValueAsString(currentRow.getCell(1)));
                    user.setDni(getCellValueAsString(currentRow.getCell(2)));
                    user.setNombre(getCellValueAsString(currentRow.getCell(3)));
                    user.setApellido(getCellValueAsString(currentRow.getCell(4)));

                    // Obtener y validar la sede
                    String sedeIdStr = getCellValueAsString(currentRow.getCell(5));
                    try {
                        Long sedeId = Long.parseLong(sedeIdStr);
                        Sede sede = sedeRepository.findById(sedeId)
                                .orElseThrow(() -> new RuntimeException("Sede no encontrada con ID: " + sedeId));
                        user.setSede(sede);
                    } catch (NumberFormatException e) {
                        errores.add("Fila " + rowNumber + ": El ID de la sede debe ser un número válido");
                        continue;
                    }

                    user.setEmail(getCellValueAsString(currentRow.getCell(6)));
                    user.setTelefono(getCellValueAsString(currentRow.getCell(7)));

                    // Validar el usuario
                    List<String> erroresUsuario = ExcelUserValidation.validateExcelUser(user, rowNumber);
                    if (!erroresUsuario.isEmpty()) {
                        errores.addAll(erroresUsuario);
                    } else {
                        usuarios.add(user);
                    }
                } catch (Exception e) {
                    errores.add("Fila " + rowNumber + ": Error al procesar la fila - " + e.getMessage());
                }
                rowNumber++;
            }
        }

        if (!errores.isEmpty()) {
            throw new RuntimeException("Errores en el archivo Excel:\n" + String.join("\n", errores));
        }

        return usuarios;
    }

    public List<User> leerUsuariosDesdeExcelBackoffice(MultipartFile file) throws IOException {
        List<User> usuarios = new ArrayList<>();
        List<String> errores = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.iterator();

            // Saltar la primera fila (encabezados)
            if (rows.hasNext()) {
                rows.next();
            }

            int rowNumber = 2; // Comenzamos desde la fila 2 (después del encabezado)
            while (rows.hasNext()) {
                Row currentRow = rows.next();
                User user = new User();
                user.setFechaCreacion(LocalDateTime.now());
                user.setRole(Role.BACKOFFICE);
                user.setEstado("A");

                try {
                    // Leer cada celda según su posición
                    user.setUsername(getCellValueAsString(currentRow.getCell(0)));
                    user.setPassword(getCellValueAsString(currentRow.getCell(1)));
                    user.setDni(getCellValueAsString(currentRow.getCell(2)));
                    user.setNombre(getCellValueAsString(currentRow.getCell(3)));
                    user.setApellido(getCellValueAsString(currentRow.getCell(4)));
                    user.setSedeNombre(getCellValueAsString(currentRow.getCell(5)));
                    user.setEmail(getCellValueAsString(currentRow.getCell(6)));
                    user.setTelefono(getCellValueAsString(currentRow.getCell(7)));

                    // Validar el usuario
                    List<String> erroresUsuario = ExcelUserValidation.validateExcelUser(user, rowNumber);
                    if (!erroresUsuario.isEmpty()) {
                        errores.addAll(erroresUsuario);
                    } else {
                        usuarios.add(user);
                    }
                } catch (Exception e) {
                    errores.add("Fila " + rowNumber + ": Error al procesar la fila - " + e.getMessage());
                }
                rowNumber++;
            }
        }

        if (!errores.isEmpty()) {
            throw new RuntimeException("Errores en el archivo Excel:\n" + String.join("\n", errores));
        }

        return usuarios;
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * Exporta una lista de AudioSinLead a un archivo Excel
     */
    public byte[] exportarAudiosSinLead(List<AudioSinLead> audios) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            Sheet sheet = workbook.createSheet("Audios Sin Lead");

            // Crear estilo para el encabezado
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // Crear encabezados
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                    "ID", "Nombre Archivo", "Móvil Extraído", "Agente Extraído",
                    "Fecha Extraída", "Estado", "Prioridad", "Carpeta Origen",
                    "Fecha Encontrado", "Fecha Procesamiento", "Observaciones",
                    "Requiere Procesamiento", "Búsqueda Lead Realizada", "Motivo Sin Lead"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Llenar datos
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");

            for (int i = 0; i < audios.size(); i++) {
                Row row = sheet.createRow(i + 1);
                AudioSinLead audio = audios.get(i);

                row.createCell(0).setCellValue(audio.getId() != null ? audio.getId() : 0);
                row.createCell(1).setCellValue(audio.getNombreArchivo() != null ? audio.getNombreArchivo() : "");
                row.createCell(2).setCellValue(audio.getMovilExtraido() != null ? audio.getMovilExtraido() : "");
                row.createCell(3).setCellValue(audio.getAgenteExtraido() != null ? audio.getAgenteExtraido() : "");
                row.createCell(4).setCellValue(audio.getFechaExtraida() != null ?
                        audio.getFechaExtraida().format(formatter) : "");
                row.createCell(5).setCellValue(audio.getEstado() != null ? audio.getEstado() : "");
                row.createCell(6).setCellValue(audio.getPrioridad() != null ? audio.getPrioridad() : "");
                row.createCell(7).setCellValue(audio.getCarpetaOrigen() != null ? audio.getCarpetaOrigen() : "");
                row.createCell(8).setCellValue(audio.getFechaEncontrado() != null ?
                        audio.getFechaEncontrado().format(formatter) : "");
                row.createCell(9).setCellValue(audio.getFechaProcesamiento() != null ?
                        audio.getFechaProcesamiento().format(formatter) : "");
                row.createCell(10).setCellValue(audio.getObservaciones() != null ? audio.getObservaciones() : "");
                row.createCell(11).setCellValue(audio.getRequiereProcesamiento() != null ?
                        (audio.getRequiereProcesamiento() ? "Sí" : "No") : "No");
                row.createCell(12).setCellValue(audio.getBusquedaLeadRealizada() != null ?
                        (audio.getBusquedaLeadRealizada() ? "Sí" : "No") : "No");
                row.createCell(13).setCellValue(audio.getMotivoSinLead() != null ? audio.getMotivoSinLead() : "");
            }

            // Ajustar ancho de columnas
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }
}
