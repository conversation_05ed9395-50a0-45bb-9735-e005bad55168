package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * Entidad que representa la respuesta de un usuario a una pregunta específica
 */
@Entity
@Table(name = "detalles_respuesta_usuario")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetalleRespuestaUsuario {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "respuesta_usuario_id", nullable = false)
    private RespuestaUsuario respuestaUsuario;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pregunta_id", nullable = false)
    private Pregunta pregunta;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "respuesta_id")
    private Respuesta respuesta; // Puede ser null en caso de respuesta de texto libre

    @Column(name = "texto_respuesta", columnDefinition = "TEXT")
    private String textoRespuesta; // Para preguntas de texto libre

    @Column(nullable = false)
    private Boolean esCorrecta = false; // Indica si la respuesta del usuario es correcta

    @Column(name = "puntaje_obtenido")
    private Integer puntajeObtenido = 0; // Puntaje obtenido en esta pregunta

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;
}
