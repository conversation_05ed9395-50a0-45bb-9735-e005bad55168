package com.midas.crm.entity.DTO.curso;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO mínimo para módulos en la vista de curso
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModuloMinimalDTO {
    private Long id;
    private String nombre;
    private String descripcion;
    private Integer orden;
    private String estado;
    private Boolean completado;
    private Integer porcentajeCompletado;
    private Integer totalLecciones;
    private Integer leccionesCompletadas;
}
