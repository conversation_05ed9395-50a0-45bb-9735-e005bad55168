# Configuración de RabbitMQ para desarrollo local

# Configuración de memoria
vm_memory_high_watermark.relative = 0.6

# Configuración de disco
disk_free_limit.relative = 2.0

# Configuración de heartbeat
heartbeat = 60

# Configuración de logs
log.console = true
log.console.level = info

# Configuración de gestión
management.tcp.port = 15672
management.tcp.ip = 0.0.0.0

# Configuración de clustering (deshabilitado para desarrollo)
cluster_formation.peer_discovery_backend = classic_config

# Configuración de SSL (deshabilitado para desarrollo)
listeners.ssl = none

# Configuración de TCP
listeners.tcp.default = 5672

# Configuración de políticas por defecto
# Configurar TTL para mensajes (opcional)
# default_user_tags.administrator = true
