package com.midas.crm.repository;

import com.midas.crm.entity.Asistencia;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 🔥 CONSULTAS ULTRA-OPTIMIZADAS usando los índices creados
 */
@Repository
public interface AsistenciaRepository extends JpaRepository<Asistencia, Long> {

    // ... (Todos tus otros métodos existentes van aquí, sin cambios) ...
    // [CORTADO PARA BREVEDAD]

    @Query("SELECT a FROM Asistencia a " +
            "JOIN FETCH a.usuario u " +
            "LEFT JOIN FETCH u.sede " +
            "WHERE a.usuario.id = :usuarioId " +
            "ORDER BY a.fechaHoraEntrada DESC")
    List<Asistencia> findByUsuarioIdOrderByFechaHoraEntradaDesc(@Param("usuarioId") Long usuarioId);

    @Query("SELECT a FROM Asistencia a " +
            "JOIN FETCH a.usuario u " +
            "LEFT JOIN FETCH u.sede " +
            "WHERE DATE(a.fechaHoraEntrada) = :fecha " +
            "ORDER BY a.fechaHoraEntrada DESC")
    List<Asistencia> findByFecha(@Param("fecha") LocalDate fecha);

    @Query("SELECT a FROM Asistencia a " +
            "JOIN FETCH a.usuario u " +
            "LEFT JOIN FETCH u.sede " +
            "WHERE a.usuario.id = :usuarioId " +
            "AND a.fechaHoraEntrada BETWEEN :fechaInicio AND :fechaFin " +
            "ORDER BY a.fechaHoraEntrada DESC")
    List<Asistencia> findByUsuarioIdAndFechaHoraEntradaBetween(
            @Param("usuarioId") Long usuarioId,
            @Param("fechaInicio") LocalDateTime fechaInicio,
            @Param("fechaFin") LocalDateTime fechaFin);

    @Query("SELECT COUNT(a) > 0 FROM Asistencia a " +
            "WHERE a.usuario.id = :usuarioId " +
            "AND DATE(a.fechaHoraEntrada) = CURRENT_DATE " +
            "AND a.tipoActividad = 'ENTRADA'")
    boolean existsAsistenciaHoyByUsuarioId(@Param("usuarioId") Long usuarioId);

    @Query("SELECT a FROM Asistencia a " +
            "JOIN FETCH a.usuario u " +
            "LEFT JOIN FETCH u.sede s " +
            "WHERE (:usuarioId IS NULL OR a.usuario.id = :usuarioId) " +
            "AND (:usuarioSearch IS NULL OR " +
            "     LOWER(u.nombre) LIKE LOWER(CONCAT('%', :usuarioSearch, '%')) OR " +
            "     LOWER(u.apellido) LIKE LOWER(CONCAT('%', :usuarioSearch, '%')) OR " +
            "     LOWER(u.username) LIKE LOWER(CONCAT('%', :usuarioSearch, '%')) OR " +
            "     LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :usuarioSearch, '%'))) " +
            "AND (:fechaInicio IS NULL OR a.fechaHoraEntrada >= :fechaInicio) " +
            "AND (:fechaFin IS NULL OR a.fechaHoraEntrada <= :fechaFin) " +
            "AND (:tipoActividad IS NULL OR a.tipoActividad = :tipoActividad) " +
            "AND (:estado IS NULL OR a.estado = :estado) " +
            "AND (:sedeId IS NULL OR s.id = :sedeId)")
    Page<Asistencia> findWithFilters(
            @Param("usuarioId") Long usuarioId,
            @Param("usuarioSearch") String usuarioSearch,
            @Param("fechaInicio") LocalDateTime fechaInicio,
            @Param("fechaFin") LocalDateTime fechaFin,
            @Param("tipoActividad") Asistencia.TipoActividad tipoActividad,
            @Param("estado") String estado,
            @Param("sedeId") Long sedeId,
            Pageable pageable
    );

    @Query("SELECT a FROM Asistencia a " +
            "JOIN FETCH a.usuario u " +
            "WHERE u.sede.id = :sedeId " +
            "AND DATE(a.fechaHoraEntrada) = :fecha " +
            "AND a.tipoActividad = 'ENTRADA' " +
            "ORDER BY a.fechaHoraEntrada DESC")
    List<Asistencia> findEntradasPorSedeYFecha(@Param("sedeId") Long sedeId,
                                               @Param("fecha") LocalDate fecha);

    @Query("SELECT COUNT(a) FROM Asistencia a " +
            "JOIN a.usuario u " +
            "WHERE u.sede.id = :sedeId " +
            "AND DATE(a.fechaHoraEntrada) = :fecha " +
            "AND a.breakActivo = true")
    long countBreaksActivosPorSede(@Param("sedeId") Long sedeId,
                                   @Param("fecha") LocalDate fecha);

    @Query("SELECT COUNT(a) FROM Asistencia a " +
            "JOIN a.usuario u " +
            "WHERE u.sede.id = :sedeId " +
            "AND DATE(a.fechaHoraEntrada) = :fecha " +
            "AND a.banoActivo = true")
    long countBanosActivosPorSede(@Param("sedeId") Long sedeId,
                                  @Param("fecha") LocalDate fecha);

    @Query("SELECT a FROM Asistencia a " +
            "JOIN FETCH a.usuario u " +
            "LEFT JOIN FETCH u.sede " +
            "WHERE a.usuario.id = :usuarioId " +
            "ORDER BY a.fechaHoraEntrada DESC " +
            "LIMIT 1")
    Optional<Asistencia> findFirstByUsuarioIdOrderByFechaHoraEntradaDesc(@Param("usuarioId") Long usuarioId);

    @Query(value =
            "SELECT " +
                    "    COUNT(DISTINCT CASE WHEN a.tipo_actividad = 'ENTRADA' AND DATE(a.fecha_hora_entrada) = :fecha THEN a.usuario_id END) as entradas, " +
                    "    COUNT(DISTINCT CASE WHEN a.tipo_actividad = 'ENTRADA' AND DATE(a.fecha_hora_entrada) = :fecha AND a.fecha_hora_salida IS NOT NULL THEN a.usuario_id END) as salidas, " +
                    "    COUNT(CASE WHEN a.break_activo = 1 AND DATE(a.fecha_hora_entrada) = :fecha THEN 1 END) as breaks_activos, " +
                    "    COUNT(CASE WHEN a.bano_activo = 1 AND DATE(a.fecha_hora_entrada) = :fecha THEN 1 END) as banos_activos, " +
                    "    COALESCE(AVG(CASE WHEN a.tiempo_neto_trabajado_minutos > 0 THEN a.tiempo_neto_trabajado_minutos/60.0 END), 0) as promedio_horas " +
                    "FROM asistencias a " +
                    "JOIN usuarios u ON a.usuario_id = u.codi_usuario " +
                    "WHERE u.sede_id = :sedeId " +
                    "  AND DATE(a.fecha_hora_entrada) = :fecha",
            nativeQuery = true)
    Object[] getEstadisticasDashboardPorSede(@Param("sedeId") Long sedeId, @Param("fecha") LocalDate fecha);

    @Query("SELECT a FROM Asistencia a " +
            "JOIN FETCH a.usuario u " +
            "WHERE u.sede.id = :sedeId " +
            "AND DATE(a.fechaHoraEntrada) = :fecha " +
            "AND a.tipoActividad = 'ENTRADA' " +
            "AND a.tiempoNetoTrabajadoMinutos > 0 " +
            "ORDER BY a.tiempoNetoTrabajadoMinutos DESC " +
            "LIMIT 5")
    List<Asistencia> findTop5UsuariosPorTiempoTrabajado(@Param("sedeId") Long sedeId,
                                                        @Param("fecha") LocalDate fecha);

    @Query(value =
            "SELECT HOUR(a.fecha_hora_entrada) as hora, COUNT(*) as cantidad " +
                    "FROM asistencias a " +
                    "JOIN usuarios u ON a.usuario_id = u.codi_usuario " +
                    "WHERE u.sede_id = :sedeId " +
                    "  AND DATE(a.fecha_hora_entrada) = :fecha " +
                    "  AND a.tipo_actividad = 'ENTRADA' " +
                    "GROUP BY HOUR(a.fecha_hora_entrada) " +
                    "ORDER BY hora",
            nativeQuery = true)
    List<Object[]> getDistribucionPorHora(@Param("sedeId") Long sedeId, @Param("fecha") LocalDate fecha);

    @Query("SELECT COUNT(a) > 0 FROM Asistencia a " +
            "WHERE a.usuario.id = :usuarioId " +
            "AND DATE(a.fechaHoraEntrada) = CURRENT_DATE " +
            "AND a.tipoActividad = 'ENTRADA' " +
            "AND a.fechaHoraSalida IS NOT NULL")
    boolean existsSalidaHoyByUsuarioId(@Param("usuarioId") Long usuarioId);

    @Query("SELECT COUNT(a) FROM Asistencia a " +
            "WHERE a.usuario.id = :usuarioId " +
            "AND DATE(a.fechaHoraEntrada) = CURRENT_DATE " +
            "AND a.tipoActividad = 'BREAK'")
    int contarBreaksHoy(@Param("usuarioId") Long usuarioId);

    @Query("SELECT COUNT(a) FROM Asistencia a " +
            "WHERE a.usuario.id = :usuarioId " +
            "AND DATE(a.fechaHoraEntrada) = CURRENT_DATE " +
            "AND a.tipoActividad = 'BANO'")
    int contarBanosHoy(@Param("usuarioId") Long usuarioId);

    @Query("SELECT a FROM Asistencia a " +
            "JOIN FETCH a.usuario u " +
            "LEFT JOIN FETCH u.sede " +
            "WHERE a.usuario.id = :usuarioId " +
            "AND a.tipoActividad = :tipoActividad " +
            "AND a.fechaHoraSalida IS NULL " +
            "AND DATE(a.fechaHoraEntrada) = CURRENT_DATE")
    Optional<Asistencia> findActividadActivaHoy(@Param("usuarioId") Long usuarioId,
                                                @Param("tipoActividad") Asistencia.TipoActividad tipoActividad);

    @Query("SELECT a FROM Asistencia a JOIN FETCH a.usuario WHERE a.usuario.id = :usuarioId AND a.tipoActividad = 'SESION_CRM' AND a.fechaHoraSalida IS NULL AND CAST(a.fechaHoraEntrada AS date) = CURRENT_DATE")
    Optional<Asistencia> findSesionCrmActiva(@Param("usuarioId") Long usuarioId);

    @Query("SELECT CAST(a.fechaHoraEntrada AS date) as fecha, COUNT(DISTINCT a.usuario.id) as totalUsuarios " +
            "FROM Asistencia a WHERE a.fechaHoraEntrada BETWEEN :fechaInicio AND :fechaFin " +
            "GROUP BY CAST(a.fechaHoraEntrada AS date)")
    List<Object[]> getEstadisticasPorFecha(@Param("fechaInicio") LocalDateTime fechaInicio, @Param("fechaFin") LocalDateTime fechaFin);


    /**
     * 🔥 NUEVO: Busca asistencias de un rango de fechas que no tienen hora de salida
     * y que no han sido cerradas previamente por el sistema.
     * Usado por la tarea programada de cierre diario.
     */
    @Query("SELECT a FROM Asistencia a " +
            "WHERE a.fechaHoraEntrada BETWEEN :fechaInicio AND :fechaFin " +
            "AND a.fechaHoraSalida IS NULL " +
            // 👇 CORRECCIÓN AQUÍ: Se debe usar la representación de texto del enum en JPQL
            "AND a.estadoActual != 'CERRADO_SISTEMA'")
    List<Asistencia> findAsistenciasSinSalidaPorFecha(
            @Param("fechaInicio") LocalDateTime fechaInicio,
            @Param("fechaFin") LocalDateTime fechaFin
    );

}