# 🏢 Monitor Call Center CRM - 50 Usuarios Concurrentes
# Script para monitorear el rendimiento del CRM en tiempo real

param(
    [string]$BaseUrl = "http://localhost:9039",
    [int]$IntervalSeconds = 30,
    [switch]$Continuous,
    [switch]$AlertsOnly,
    [string]$LogFile = "call-center-monitor.log"
)

# Colores para output
$Colors = @{
    Good = "Green"
    Warning = "Yellow" 
    Critical = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Get-Timestamp {
    return Get-Date -Format "yyyy-MM-dd HH:mm:ss"
}

function Write-ColorLog {
    param([string]$Message, [string]$Color = "White", [switch]$NoNewLine)
    
    if ($NoNewLine) {
        Write-Host $Message -ForegroundColor $Color -NoNewline
    } else {
        Write-Host $Message -ForegroundColor $Color
    }
    
    # Log to file
    "$(Get-Timestamp) - $Message" | Out-File -FilePath $LogFile -Append
}

function Test-CallCenterEndpoint {
    param([string]$Url, [string]$Description)
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method Get -TimeoutSec 10
        Write-ColorLog "✅ $Description - OK" -Color $Colors.Good
        return $true
    }
    catch {
        Write-ColorLog "❌ $Description - ERROR: $($_.Exception.Message)" -Color $Colors.Critical
        return $false
    }
}

function Get-CallCenterMetrics {
    param([string]$BaseUrl)
    
    try {
        $healthUrl = "$BaseUrl/api/call-center/health"
        $health = Invoke-RestMethod -Uri $healthUrl -Method Get -TimeoutSec 15
        
        if ($health.rpta -eq 1) {
            return $health.data
        } else {
            Write-ColorLog "⚠️ Error en respuesta de health: $($health.mensaje)" -Color $Colors.Warning
            return $null
        }
    }
    catch {
        Write-ColorLog "❌ Error obteniendo métricas del call center: $($_.Exception.Message)" -Color $Colors.Critical
        return $null
    }
}

function Get-SimpleMetrics {
    param([string]$BaseUrl)
    
    try {
        $metricsUrl = "$BaseUrl/api/call-center/metrics/simple"
        $metrics = Invoke-RestMethod -Uri $metricsUrl -Method Get -TimeoutSec 10
        return $metrics
    }
    catch {
        Write-ColorLog "⚠️ No se pudieron obtener métricas simples" -Color $Colors.Warning
        return $null
    }
}

function Show-CallCenterDashboard {
    param([hashtable]$Metrics)
    
    if (-not $Metrics) {
        Write-ColorLog "❌ No hay métricas disponibles" -Color $Colors.Critical
        return
    }
    
    Clear-Host
    Write-ColorLog "🏢 ===== DASHBOARD CALL CENTER CRM =====" -Color $Colors.Header
    Write-ColorLog "📅 $(Get-Timestamp)" -Color $Colors.Info
    Write-ColorLog "=" * 50 -Color $Colors.Header
    
    # Usuarios activos
    $activeUsers = $Metrics.activeConnections
    $maxUsers = $Metrics.maxConnections
    $usagePercent = $Metrics.connectionUsagePercent
    
    Write-ColorLog "👥 USUARIOS ACTIVOS" -Color $Colors.Info
    Write-ColorLog "   Activos: $activeUsers / $maxUsers" -Color $(if ($activeUsers -lt 40) { $Colors.Good } elseif ($activeUsers -lt 45) { $Colors.Warning } else { $Colors.Critical })
    Write-ColorLog "   Uso: $usagePercent%" -Color $(if ($usagePercent -lt 70) { $Colors.Good } elseif ($usagePercent -lt 85) { $Colors.Warning } else { $Colors.Critical })
    
    # Rendimiento
    $avgResponse = [math]::Round($Metrics.avgResponseTimeMs, 2)
    $maxResponse = [math]::Round($Metrics.maxResponseTimeMs, 2)
    $rps = [math]::Round($Metrics.requestsPerSecond, 2)
    
    Write-ColorLog "`n⚡ RENDIMIENTO" -Color $Colors.Info
    Write-ColorLog "   Tiempo Promedio: ${avgResponse}ms" -Color $(if ($avgResponse -lt 200) { $Colors.Good } elseif ($avgResponse -lt 500) { $Colors.Warning } else { $Colors.Critical })
    Write-ColorLog "   Tiempo Máximo: ${maxResponse}ms" -Color $(if ($maxResponse -lt 500) { $Colors.Good } elseif ($maxResponse -lt 1000) { $Colors.Warning } else { $Colors.Critical })
    Write-ColorLog "   Requests/seg: $rps" -Color $Colors.Good
    
    # Base de datos
    $dbConnections = $Metrics.dbConnections
    $dbMax = $Metrics.dbMaxConnections
    $dbUsage = $Metrics.dbConnectionUsagePercent
    
    Write-ColorLog "`n🗄️ BASE DE DATOS" -Color $Colors.Info
    Write-ColorLog "   Conexiones: $dbConnections / $dbMax" -Color $(if ($dbConnections -lt 35) { $Colors.Good } elseif ($dbConnections -lt 45) { $Colors.Warning } else { $Colors.Critical })
    Write-ColorLog "   Uso BD: $dbUsage%" -Color $(if ($dbUsage -lt 70) { $Colors.Good } elseif ($dbUsage -lt 85) { $Colors.Warning } else { $Colors.Critical })
    
    # Estado general
    $status = $Metrics.status
    $recommendation = $Metrics.recommendation
    
    Write-ColorLog "`n📊 ESTADO GENERAL" -Color $Colors.Info
    $statusColor = switch ($status) {
        "HEALTHY" { $Colors.Good }
        "WARNING" { $Colors.Warning }
        "CRITICAL" { $Colors.Critical }
        default { $Colors.Info }
    }
    Write-ColorLog "   Estado: $status" -Color $statusColor
    Write-ColorLog "   Recomendación: $recommendation" -Color $Colors.Info
    
    # Cache
    if ($Metrics.cacheHitRate) {
        $cacheHit = [math]::Round($Metrics.cacheHitRate, 2)
        Write-ColorLog "`n💾 CACHE" -Color $Colors.Info
        Write-ColorLog "   Hit Rate: $cacheHit%" -Color $(if ($cacheHit -gt 80) { $Colors.Good } elseif ($cacheHit -gt 60) { $Colors.Warning } else { $Colors.Critical })
    }
    
    Write-ColorLog "`n" + "=" * 50 -Color $Colors.Header
}

function Show-CallCenterAlerts {
    param([hashtable]$Metrics)
    
    if (-not $Metrics) { return }
    
    $alerts = @()
    
    # Verificar alertas críticas
    if ($Metrics.connectionUsagePercent -gt 90) {
        $alerts += "🚨 CRÍTICO: Uso de conexiones > 90% ($($Metrics.connectionUsagePercent)%)"
    }
    
    if ($Metrics.avgResponseTimeMs -gt 1000) {
        $alerts += "🚨 CRÍTICO: Tiempo de respuesta > 1s ($($Metrics.avgResponseTimeMs)ms)"
    }
    
    if ($Metrics.dbConnectionUsagePercent -gt 90) {
        $alerts += "🚨 CRÍTICO: Uso de BD > 90% ($($Metrics.dbConnectionUsagePercent)%)"
    }
    
    # Verificar alertas de advertencia
    if ($Metrics.connectionUsagePercent -gt 70 -and $Metrics.connectionUsagePercent -le 90) {
        $alerts += "⚠️ ADVERTENCIA: Uso de conexiones alto ($($Metrics.connectionUsagePercent)%)"
    }
    
    if ($Metrics.avgResponseTimeMs -gt 500 -and $Metrics.avgResponseTimeMs -le 1000) {
        $alerts += "⚠️ ADVERTENCIA: Tiempo de respuesta alto ($($Metrics.avgResponseTimeMs)ms)"
    }
    
    # Mostrar alertas
    if ($alerts.Count -gt 0) {
        Write-ColorLog "`n🚨 ALERTAS ACTIVAS:" -Color $Colors.Critical
        foreach ($alert in $alerts) {
            if ($alert.StartsWith("🚨")) {
                Write-ColorLog "   $alert" -Color $Colors.Critical
            } else {
                Write-ColorLog "   $alert" -Color $Colors.Warning
            }
        }
    } elseif (-not $AlertsOnly) {
        Write-ColorLog "`n✅ No hay alertas activas" -Color $Colors.Good
    }
}

function Show-CallCenterSummary {
    param([string]$BaseUrl)
    
    Write-ColorLog "`n🏥 $(Get-Timestamp) - RESUMEN CALL CENTER" -Color $Colors.Header
    Write-ColorLog "=" * 60 -Color $Colors.Header
    
    # Verificar endpoints básicos
    $healthOk = Test-CallCenterEndpoint "$BaseUrl/api/call-center/health" "Health Check Call Center"
    $capacityOk = Test-CallCenterEndpoint "$BaseUrl/api/call-center/capacity" "Verificación de Capacidad"
    $prometheusOk = Test-CallCenterEndpoint "$BaseUrl/actuator/prometheus" "Métricas Prometheus"
    
    if ($healthOk) {
        # Obtener y mostrar métricas
        $metrics = Get-CallCenterMetrics $BaseUrl
        if ($metrics) {
            Show-CallCenterDashboard $metrics
            Show-CallCenterAlerts $metrics
        }
    }
    
    Write-ColorLog "=" * 60 -Color $Colors.Header
}

# Función principal
function Start-CallCenterMonitoring {
    Write-ColorLog "🚀 Iniciando monitoreo del Call Center CRM..." -Color $Colors.Info
    Write-ColorLog "📍 URL Base: $BaseUrl" -Color $Colors.Info
    Write-ColorLog "⏱️ Intervalo: $IntervalSeconds segundos" -Color $Colors.Info
    Write-ColorLog "📝 Log: $LogFile" -Color $Colors.Info
    
    if ($Continuous) {
        Write-ColorLog "🔄 Modo continuo activado. Presiona Ctrl+C para detener." -Color $Colors.Info
        
        do {
            try {
                if ($AlertsOnly) {
                    $metrics = Get-CallCenterMetrics $BaseUrl
                    if ($metrics) {
                        Show-CallCenterAlerts $metrics
                    }
                } else {
                    Show-CallCenterSummary $BaseUrl
                }
                
                Start-Sleep -Seconds $IntervalSeconds
            }
            catch {
                Write-ColorLog "❌ Error en monitoreo: $($_.Exception.Message)" -Color $Colors.Critical
                Start-Sleep -Seconds 10
            }
        } while ($true)
    } else {
        Show-CallCenterSummary $BaseUrl
    }
}

# Ejecutar monitoreo
Start-CallCenterMonitoring
