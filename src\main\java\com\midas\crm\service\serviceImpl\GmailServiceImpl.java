package com.midas.crm.service.serviceImpl;

import com.google.api.client.googleapis.batch.BatchRequest;
import com.google.api.client.googleapis.batch.json.JsonBatchCallback;
import com.google.api.client.googleapis.json.GoogleJsonError;
import com.google.api.client.http.HttpHeaders;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.model.ListMessagesResponse;
import com.google.api.services.gmail.model.Message;
import com.google.api.services.gmail.model.MessagePart;
import com.google.api.services.gmail.model.MessagePartBody;
import com.google.api.services.gmail.model.MessagePartHeader;
import com.midas.crm.entity.DTO.gmail.EmailDtos;
import com.midas.crm.service.GmailService;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Properties;

@Service
@Slf4j
public class GmailServiceImpl implements GmailService {

    private final Gmail gmailApiClient;

    @Value("${spring.mail.username}")
    private String emailRemitente;

    private static final String ID_USUARIO = "me";

    public GmailServiceImpl(@Lazy Gmail gmailApiClient) {
        this.gmailApiClient = gmailApiClient;
    }

    @Override
    public void enviarEmail(EmailDtos.EnviarEmailRequest request) throws MessagingException {
        log.info("Intentando enviar email a: {} usando Gmail API", request.para());

        try {
            // Crear mensaje MIME usando Gmail API
            Message message = crearMensajeMime(request.para(), request.asunto(), request.cuerpo());

            // Enviar mensaje usando Gmail API
            Message sentMessage = gmailApiClient.users().messages().send(ID_USUARIO, message).execute();

            log.info("Email enviado exitosamente a {} usando Gmail API. ID del mensaje: {}",
                    request.para(), sentMessage.getId());

        } catch (IOException e) {
            log.error("Error al enviar email usando Gmail API a {}: {}", request.para(), e.getMessage(), e);
            throw new MessagingException("Error al enviar email usando Gmail API: " + e.getMessage(), e);
        }
    }

    /**
     * Crea un mensaje MIME para enviar usando Gmail API
     */
    private Message crearMensajeMime(String destinatario, String asunto, String cuerpo) throws MessagingException, IOException {
        try {
            // Crear propiedades para la sesión de correo
            Properties props = new Properties();
            Session session = Session.getDefaultInstance(props, null);

            // Crear mensaje MIME
            MimeMessage mimeMessage = new MimeMessage(session);
            mimeMessage.setFrom(new InternetAddress(emailRemitente));
            mimeMessage.addRecipient(jakarta.mail.Message.RecipientType.TO, new InternetAddress(destinatario));
            mimeMessage.setSubject(asunto, "UTF-8");
            mimeMessage.setText(cuerpo, "UTF-8", "html");

            // Convertir a bytes y codificar en Base64
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            mimeMessage.writeTo(buffer);
            byte[] bytes = buffer.toByteArray();
            String encodedEmail = Base64.getUrlEncoder().encodeToString(bytes);

            // Crear mensaje de Gmail API
            Message message = new Message();
            message.setRaw(encodedEmail);

            return message;

        } catch (Exception e) {
            log.error("Error al crear mensaje MIME: {}", e.getMessage(), e);
            throw new MessagingException("Error al crear mensaje MIME: " + e.getMessage(), e);
        }
    }

    /**
     * MEJORADO: Usa peticiones por lotes para un rendimiento óptimo.
     */
    @Override
    public List<EmailDtos.EmailMensajeResumen> getBandejaDeEntrada(int maxResults) throws IOException {
        log.info("🚀 Obteniendo hasta {} mensajes de la bandeja de entrada usando Batching.", maxResults);

        // 1. Obtener la lista de IDs de mensajes (una sola llamada a la API)
        ListMessagesResponse listResponse = gmailApiClient.users().messages().list(ID_USUARIO)
                .setLabelIds(List.of("INBOX")).setMaxResults((long) maxResults).execute();

        List<Message> messages = listResponse.getMessages();
        if (messages == null || messages.isEmpty()) {
            log.info("No se encontraron mensajes en la bandeja de entrada.");
            return Collections.emptyList();
        }

        // 2. Preparar una petición por lotes para obtener la metadata de todos los correos a la vez.
        BatchRequest batch = gmailApiClient.batch();
        List<EmailDtos.EmailMensajeResumen> summaries = new ArrayList<>();
        JsonBatchCallback<Message> callback = new JsonBatchCallback<>() {
            @Override
            public void onSuccess(Message message, HttpHeaders responseHeaders) {
                summaries.add(crearResumenDesdeMensaje(message));
            }
            @Override
            public void onFailure(GoogleJsonError e, HttpHeaders responseHeaders) {
                log.error("Fallo al obtener un mensaje en el lote: {}", e.getMessage());
            }
        };

        for (Message msg : messages) {
            gmailApiClient.users().messages().get(ID_USUARIO, msg.getId()).setFormat("metadata").queue(batch, callback);
        }

        // 3. Ejecutar el lote (una sola llamada a la API para todos los mensajes)
        long startTime = System.currentTimeMillis();
        batch.execute();
        log.info("Batch request completado en {} ms para {} mensajes.", (System.currentTimeMillis() - startTime), messages.size());

        return summaries;
    }

    @Override
    public EmailDtos.EmailMensajeDetalle getDetalleMensaje(String idMensaje) throws IOException {
        log.info("Obteniendo detalles completos para el mensaje con ID: {}", idMensaje);
        Message mensaje = gmailApiClient.users().messages().get(ID_USUARIO, idMensaje).setFormat("full").execute();

        String de = getCabecera(mensaje.getPayload().getHeaders(), "From").orElse("N/A");
        String para = getCabecera(mensaje.getPayload().getHeaders(), "To").orElse("N/A");
        String asunto = getCabecera(mensaje.getPayload().getHeaders(), "Subject").orElse("(Sin asunto)");
        String fecha = getCabecera(mensaje.getPayload().getHeaders(), "Date").orElse("N/A");

        ParsedPayload parsedPayload = parsearCuerpoYAdjuntos(mensaje.getPayload());

        return new EmailDtos.EmailMensajeDetalle(mensaje.getId(), de, para, asunto, parsedPayload.body(), fecha, parsedPayload.attachments());
    }

    /**
     * 🆕 Descarga el contenido de un archivo adjunto.
     */
    @Override
    public byte[] getAdjunto(String idMensaje, String idAdjunto) throws IOException {
        log.info("Descargando adjunto {} del mensaje {}", idAdjunto, idMensaje);
        MessagePartBody body = gmailApiClient.users().messages().attachments()
                .get(ID_USUARIO, idMensaje, idAdjunto).execute();
        return Base64.getUrlDecoder().decode(body.getData());
    }

    private EmailDtos.EmailMensajeResumen crearResumenDesdeMensaje(Message messageDetails) {
        String de = getCabecera(messageDetails.getPayload().getHeaders(), "From").orElse("N/A");
        String asunto = getCabecera(messageDetails.getPayload().getHeaders(), "Subject").orElse("(Sin asunto)");
        String fecha = getCabecera(messageDetails.getPayload().getHeaders(), "Date").orElse("N/A");
        return new EmailDtos.EmailMensajeResumen(messageDetails.getId(), de, asunto, messageDetails.getSnippet(), fecha);
    }

    private Optional<String> getCabecera(List<MessagePartHeader> cabeceras, String nombre) {
        return cabeceras.stream()
                .filter(h -> h.getName().equalsIgnoreCase(nombre))
                .map(MessagePartHeader::getValue)
                .findFirst();
    }

    /**
     * DTO interno para el resultado del parseo recursivo.
     */
    private record ParsedPayload(String body, List<EmailDtos.AdjuntoDetalle> attachments) {}

    /**
     * MEJORADO: Método principal que inicia el parseo recursivo de las partes de un mensaje.
     */
    private ParsedPayload parsearCuerpoYAdjuntos(MessagePart startPart) {
        StringBuilder body = new StringBuilder();
        List<EmailDtos.AdjuntoDetalle> attachments = new ArrayList<>();
        parsearParteRecursivamente(startPart, body, attachments);
        return new ParsedPayload(body.toString(), attachments);
    }

    /**
     * MEJORADO: Recorre recursivamente todas las partes de un mensaje para encontrar
     * el cuerpo (dando prioridad a multipart/alternative y text/html) y todos los adjuntos.
     */
    private void parsearParteRecursivamente(MessagePart part, StringBuilder body, List<EmailDtos.AdjuntoDetalle> attachments) {
        if (part == null) return;
        String mimeType = part.getMimeType();

        // Caso 1: Es un contenedor de múltiples partes (ej. multipart/mixed, multipart/related)
        if (part.getParts() != null) {
            // Caso especial: multipart/alternative, contiene el mismo contenido en diferentes formatos.
            // Buscamos la mejor versión (HTML) y la procesamos, ignorando las demás.
            if ("multipart/alternative".equalsIgnoreCase(mimeType)) {
                Optional<MessagePart> htmlPart = part.getParts().stream()
                        .filter(p -> "text/html".equalsIgnoreCase(p.getMimeType())).findFirst();
                if (htmlPart.isPresent()) {
                    parsearParteRecursivamente(htmlPart.get(), body, attachments);
                } else { // Si no hay HTML, buscamos texto plano como fallback.
                    part.getParts().stream().filter(p -> "text/plain".equalsIgnoreCase(p.getMimeType()))
                            .findFirst().ifPresent(p -> parsearParteRecursivamente(p, body, attachments));
                }
            } else { // Para otros multipart, procesamos cada parte.
                for (MessagePart subPart : part.getParts()) {
                    parsearParteRecursivamente(subPart, body, attachments);
                }
            }
        }

        // Caso 2: Es una parte de texto (el cuerpo)
        else if ("text/html".equalsIgnoreCase(mimeType) || "text/plain".equalsIgnoreCase(mimeType)) {
            if (part.getBody() != null && part.getBody().getData() != null) {
                body.append(new String(Base64.getUrlDecoder().decode(part.getBody().getData()), StandardCharsets.UTF_8));
            }
        }

        // Caso 3: Es un archivo adjunto
        else if (part.getFilename() != null && !part.getFilename().isEmpty() && part.getBody() != null && part.getBody().getAttachmentId() != null) {
            attachments.add(new EmailDtos.AdjuntoDetalle(
                    part.getBody().getAttachmentId(),
                    part.getFilename(),
                    part.getMimeType(),
                    part.getBody().getSize()
            ));
        }
    }
}