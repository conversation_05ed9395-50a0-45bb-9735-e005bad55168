package com.midas.crm.controller;


import com.midas.crm.entity.DTO.auth.AuthRequest;
import com.midas.crm.entity.DTO.auth.AuthResponse;
import com.midas.crm.entity.DTO.auth.SignOutRequest;
import com.midas.crm.entity.DTO.auth.SignUpRequest;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.service.AuthService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("${api.route.authentication}")
@RequiredArgsConstructor
@Slf4j
public class AuthenticationController {

    private final AuthService authService;

    @PostMapping("/refresh-token")
    public ResponseEntity<GenericResponse<AuthResponse>> refreshToken(HttpServletRequest request) {
        AuthResponse response = authService.refreshToken(request);
        return ResponseEntity.ok(
                new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "Token renovado exitosamente",
                        response
                )
        );
    }

    @PostMapping("/sign-up")
    public ResponseEntity<GenericResponse<Void>> signUp(
            @Valid @RequestBody SignUpRequest request
    ) {
        authService.signUp(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "Usuario creado exitosamente",
                        null
                ));
    }

    @PostMapping("/sign-in")
    public ResponseEntity<GenericResponse<AuthResponse>> login(@RequestBody AuthRequest request) {
        AuthResponse auth = authService.signIn(request);
        return ResponseEntity.ok(new GenericResponse<>(1, "Inicio de sesión exitoso", auth));
    }

    @PostMapping("/sign-out")
    public ResponseEntity<GenericResponse<Void>> signOut(
            @Valid @RequestBody SignOutRequest request
    ) {
        authService.signOut(request);
        return ResponseEntity.ok(
                new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "Sesión cerrada exitosamente",
                        null
                )
        );
    }
}