package com.midas.crm.service;

import com.midas.crm.entity.DTO.analytics.AnalyticsResumenDTO;
import com.midas.crm.entity.DTO.analytics.ClientesPorPeriodoDTO;
import com.midas.crm.entity.DTO.analytics.PrediccionVentasDTO;
import com.midas.crm.entity.DTO.analytics.SegmentacionClientesDTO;
import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.cliente.ClienteResidencialDTO;
import com.midas.crm.utils.GenericResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ClienteResidencialService {
        List<ClienteResidencial> listarTodos();

        // Cambiar para que retorne una lista en lugar de un Optional
        List<ClienteResidencial> buscarPorMovil(String movil);

        ClienteResidencial obtenerPorId(Long id);

        @Transactional
        ClienteResidencial guardar(ClienteResidencial cliente, Long usuarioId);

        ClienteResidencial actualizar(Long id, ClienteResidencial cliente);

        ClienteResidencial actualizarNotaAgenteComparadorIA(Long id, BigDecimal nota);

        void eliminar(Long id);

        ResponseEntity<GenericResponse<Map<String, Object>>> obtenerClientesConUsuario(int page, int size);

        // Nuevo método para buscar por móvil
        // Optional<ClienteResidencial> buscarPorMovil(String movilContacto);

        ResponseEntity<GenericResponse<Map<String, Object>>> obtenerClientesConUsuarioFiltrados(String dniAsesor,
                        String nombreAsesor, String numeroMovil, String fecha, int page, int size);

        ResponseEntity<GenericResponse<Map<String, Object>>> obtenerClientesConUsuarioFiltradosPorFechaActual(
                        String dniAsesor, String nombreAsesor, String numeroMovil, int page, int size);

        List<ClienteResidencialDTO> getClientesByAsesorId(Long asesorId);

        Long countClientesByAsesorId(Long asesorId);

        List<ClienteResidencialDTO> getVentasRealizadasByAsesorId(Long asesorId);

        ClienteResidencialDTO convertToDTO(ClienteResidencial cliente);

        List<ClienteResidencial> buscarPorDniMovilYFechaEntre(String dni, String nombreCompleto, String movil,
                        LocalDateTime inicio, LocalDateTime fin);

        ResponseEntity<byte[]> exportarExcel();

        ResponseEntity<byte[]> exportarExcelPorMovil(String movilContacto);

        ResponseEntity<byte[]> exportarExcelPorFecha(String fechaStr);

        ResponseEntity<byte[]> exportarExcelPorRangoFechas(String fechaInicioStr, String fechaFinStr);
}