global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "prometheus-alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

scrape_configs:
  - job_name: 'crm-spring-boot-app'
    # CORRECCIÓN 1: La ruta debe incluir el context path de tu backend
    metrics_path: '/BackendJava/actuator/prometheus'
    scrape_interval: 10s
    static_configs:
      # CORRECCIÓN 2: El target debe ser 'localhost', no 'host.docker.internal'
      - targets: ['localhost:9039']