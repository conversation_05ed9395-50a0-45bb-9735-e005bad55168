package com.midas.crm.config;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.ObjectUtils;
import com.midas.crm.utils.SecurityUtils;

import java.io.IOException;
import java.util.List;

@Slf4j
public class HeaderRequestInterceptor implements ClientHttpRequestInterceptor {
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        // Usar constantes de SecurityUtils en lugar de Constantes
        request.getHeaders().set("X-Request-ID", MDC.get("X-Request-ID"));

        List<String> authHeaders = request.getHeaders().get(SecurityUtils.AUTH_HEADER);
        if(ObjectUtils.isEmpty(authHeaders)){
            request.getHeaders().set(SecurityUtils.AUTH_HEADER, MDC.get(SecurityUtils.AUTH_HEADER));
        }

        return execution.execute(request, body);
    }
}
