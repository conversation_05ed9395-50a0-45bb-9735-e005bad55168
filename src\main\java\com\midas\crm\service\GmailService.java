package com.midas.crm.service;

import com.midas.crm.entity.DTO.gmail.EmailDtos;
import jakarta.mail.MessagingException;
import java.io.IOException;
import java.util.List;

public interface GmailService {

    void enviarEmail(EmailDtos.EnviarEmailRequest request) throws MessagingException;

    List<EmailDtos.EmailMensajeResumen> getBandejaDeEntrada(int maxResults) throws IOException;

    EmailDtos.EmailMensajeDetalle getDetalleMensaje(String idMensaje) throws IOException;

    /**
     * 🆕 Obtiene los bytes de un archivo adjunto específico.
     *
     * @param idMensaje El ID del mensaje que contiene el adjunto.
     * @param idAdjunto El ID del adjunto a descargar.
     * @return Un array de bytes con el contenido del archivo.
     * @throws IOException si hay un error al comunicarse con la API de Gmail.
     */
    byte[] getAdjunto(String idMensaje, String idAdjunto) throws IOException;
}