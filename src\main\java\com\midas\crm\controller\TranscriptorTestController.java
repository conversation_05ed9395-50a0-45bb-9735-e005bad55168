package com.midas.crm.controller;

import com.midas.crm.service.TranscriptionDatabaseServiceSimple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Controlador para probar la conexión directa a transcriptor_db
 * ⚠️ IMPORTANTE: Solo consultas de LECTURA - NO modificar tabla en producción
 */
@RestController
@RequestMapping("${api.route.baseDatosTranscripcion}")
public class TranscriptorTestController {
    @Autowired
    private TranscriptionDatabaseServiceSimple transcriptionService;

    /**
     * 🧪 ENDPOINT DE PRUEBA: Verificar conexión a transcriptor_db
     */
    @GetMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection() {
        try {
            long count = transcriptionService.countTodayTranscriptions();
            List<Map<String, Object>> latest = transcriptionService.getLatestTranscriptions(5);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Conexión a transcriptor_db exitosa");
            response.put("today_count", count);
            response.put("latest_transcriptions", latest.size());
            response.put("database", "transcriptor_db");
            response.put("table", "TRA_DATASET_AUDIO_001");
            response.put("host", "************:3306");
            response.put("current_date", LocalDate.now().toString());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            errorResponse.put("message", "Error conectando a transcriptor_db");
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔍 ENDPOINT DE BÚSQUEDA: Buscar transcripción por call_id
     */
    @GetMapping("/search/call-id/{callId}")
    public ResponseEntity<Map<String, Object>> searchByCallId(@PathVariable String callId) {
        try {
            Optional<Map<String, Object>> result = transcriptionService
                    .findTranscriptionIntelligent(callId, null, null, null);

            Map<String, Object> response = new HashMap<>();
            if (result.isPresent()) {
                Map<String, Object> record = result.get();
                response.put("success", true);
                response.put("found", true);
                response.put("call_id", record.get("call_id"));
                response.put("agent_id", record.get("agent_id"));
                response.put("caller_phone", record.get("caller_phone"));
                response.put("audio_file_name", record.get("audio_file_name"));
                response.put("created_at", record.get("created_at"));
                response.put("text_length", ((String) record.get("original_text")).length());

                String originalText = (String) record.get("original_text");
                String preview = originalText.substring(0, Math.min(200, originalText.length())) + "...";
                response.put("transcription_preview", preview);
            } else {
                response.put("success", true);
                response.put("found", false);
                response.put("message", "No se encontró transcripción con call_id: " + callId);
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔍 ENDPOINT ESPECÍFICO: Buscar tu transcripción de ejemplo
     */
    @GetMapping("/search/example")
    public ResponseEntity<Map<String, Object>> searchExample() {
        try {
            // Buscar la transcripción de ejemplo que mencionaste: CALL_C620FD55
            Optional<Map<String, Object>> result = transcriptionService
                    .findTranscriptionIntelligent("CALL_C620FD55", null, null, null);

            Map<String, Object> response = new HashMap<>();
            if (result.isPresent()) {
                Map<String, Object> record = result.get();
                response.put("success", true);
                response.put("found", true);
                response.put("message", "Transcripción de ejemplo encontrada");
                response.put("call_id", record.get("call_id"));
                response.put("agent_id", record.get("agent_id"));
                response.put("caller_phone", record.get("caller_phone"));
                response.put("audio_file_name", record.get("audio_file_name"));
                response.put("created_at", record.get("created_at"));
                response.put("processing_time", record.get("processing_time"));
                response.put("text_length", ((String) record.get("original_text")).length());
                response.put("mood", record.get("mood_analysis"));
                response.put("device_used", record.get("device_used"));

                String originalText = (String) record.get("original_text");
                String preview = originalText.substring(0, Math.min(300, originalText.length())) + "...";
                response.put("transcription_preview", preview);
            } else {
                response.put("success", true);
                response.put("found", false);
                response.put("message", "No se encontró la transcripción de ejemplo CALL_C620FD55");
                response.put("suggestion", "Verifica que la transcripción existe en la base de datos");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            errorResponse.put("message", "Error buscando transcripción de ejemplo");
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 📊 ENDPOINT DE ESTADÍSTICAS: Información general de la base de datos
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        try {
            long todayCount = transcriptionService.countTodayTranscriptions();
            List<Map<String, Object>> latest = transcriptionService.getLatestTranscriptions(10);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("today_transcriptions", todayCount);
            response.put("latest_count", latest.size());
            response.put("current_date", LocalDate.now());

            Map<String, Object> dbInfo = new HashMap<>();
            dbInfo.put("database", "transcriptor_db");
            dbInfo.put("table", "TRA_DATASET_AUDIO_001");
            dbInfo.put("host", "************:3306");
            response.put("database_info", dbInfo);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔍 ENDPOINT DE BÚSQUEDA: Buscar por múltiples criterios
     */
    @GetMapping("/search/multi")
    public ResponseEntity<Map<String, Object>> searchMultiCriteria(
            @RequestParam(required = false) String callId,
            @RequestParam(required = false) String agentId,
            @RequestParam(required = false) String callerPhone,
            @RequestParam(required = false) String audioFileName) {

        try {
            Optional<Map<String, Object>> result = transcriptionService
                    .findTranscriptionIntelligent(callId, agentId, callerPhone, audioFileName);

            Map<String, Object> response = new HashMap<>();
            Map<String, Object> searchCriteria = new HashMap<>();
            searchCriteria.put("call_id", callId != null ? callId : "null");
            searchCriteria.put("agent_id", agentId != null ? agentId : "null");
            searchCriteria.put("caller_phone", callerPhone != null ? callerPhone : "null");
            searchCriteria.put("audio_file_name", audioFileName != null ? audioFileName : "null");

            response.put("success", true);
            response.put("search_criteria", searchCriteria);
            response.put("found", result.isPresent());

            if (result.isPresent()) {
                Map<String, Object> record = result.get();
                Map<String, Object> resultData = new HashMap<>();
                resultData.put("call_id", record.get("call_id"));
                resultData.put("agent_id", record.get("agent_id"));
                resultData.put("caller_phone", record.get("caller_phone"));
                resultData.put("audio_file_name", record.get("audio_file_name"));
                resultData.put("created_at", record.get("created_at"));
                resultData.put("text_length", ((String) record.get("original_text")).length());
                response.put("result", resultData);
            } else {
                response.put("message", "No se encontró transcripción con los criterios especificados");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.ok(errorResponse);
        }
    }
}
