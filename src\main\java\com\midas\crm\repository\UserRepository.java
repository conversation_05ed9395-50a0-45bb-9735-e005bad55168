package com.midas.crm.repository;

import com.midas.crm.entity.DTO.asesor.AsesorDisponibleDTO;
import com.midas.crm.entity.DTO.coordinador.CoordinadorListDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /* ---------- BÚSQUEDAS BÁSICAS ---------- */
    Optional<User> findByUsername(String username);

    Optional<User> findByEmail(String email);

    Optional<User> findByDni(String dni);

    boolean existsByUsername(String username);

    boolean existsByDni(String dni);

    /* ---------- PAGINADAS / FILTRADAS ---------- */
    Page<User> findAllBySede_Id(Long sedeId, Pageable pageable);

    Page<User> findAllByRole(Role role, Pageable pageable);

    Optional<User> findByIdAndRole(Long id, Role role);

    /* ---------- CONSULTAS OPTIMIZADAS ---------- */
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.sede LEFT JOIN FETCH u.coordinador WHERE u.id IN :ids")
    List<User> findAllWithSedeAndCoordinadorByIds(@Param("ids") List<Long> ids);

    @Query("SELECT u FROM User u LEFT JOIN FETCH u.sede LEFT JOIN FETCH u.coordinador WHERE u.id IN :ids AND u.sede.id = :sedeId")
    List<User> findAllWithSedeAndCoordinadorByIdsAndSedeId(@Param("ids") List<Long> ids, @Param("sedeId") Long sedeId);

    @Query(value = "SELECT u FROM User u", countQuery = "SELECT COUNT(u) FROM User u")
    Page<User> findAllPaginated(Pageable pageable);

    @Query(value = "SELECT u FROM User u WHERE u.sede.id = :sedeId", countQuery = "SELECT COUNT(u) FROM User u WHERE u.sede.id = :sedeId")
    Page<User> findAllBySede_IdPaginated(@Param("sedeId") Long sedeId, Pageable pageable);

    /**
     * Consulta optimizada para obtener usuarios ordenados alfabéticamente por
     * nombre y apellido
     */
    @Query(value = "SELECT u FROM User u ORDER BY u.nombre ASC, u.apellido ASC", countQuery = "SELECT COUNT(u) FROM User u")
    Page<User> findAllOrderedByNombreApellido(Pageable pageable);

    /**
     * Consulta optimizada para obtener usuarios de una sede específica ordenados
     * alfabéticamente por nombre y apellido
     */
    @Query(value = "SELECT u FROM User u WHERE u.sede.id = :sedeId ORDER BY u.nombre ASC, u.apellido ASC", countQuery = "SELECT COUNT(u) FROM User u WHERE u.sede.id = :sedeId")
    Page<User> findAllBySede_IdOrderedByNombreApellido(@Param("sedeId") Long sedeId, Pageable pageable);

    /**
     * Consulta optimizada para obtener usuarios filtrados por estado ordenados
     * alfabéticamente por nombre y apellido
     */
    @Query(value = "SELECT u FROM User u WHERE u.estado = :estado ORDER BY u.nombre ASC, u.apellido ASC", countQuery = "SELECT COUNT(u) FROM User u WHERE u.estado = :estado")
    Page<User> findAllByEstadoOrderedByNombreApellido(@Param("estado") String estado, Pageable pageable);

    /**
     * Consulta optimizada para obtener usuarios de una sede específica filtrados por estado
     * ordenados alfabéticamente por nombre y apellido
     */
    @Query(value = "SELECT u FROM User u WHERE u.sede.id = :sedeId AND u.estado = :estado ORDER BY u.nombre ASC, u.apellido ASC", countQuery = "SELECT COUNT(u) FROM User u WHERE u.sede.id = :sedeId AND u.estado = :estado")
    Page<User> findAllBySede_IdAndEstadoOrderedByNombreApellido(@Param("sedeId") Long sedeId, @Param("estado") String estado, Pageable pageable);

    /* ---------- ROL & COORDINADOR ---------- */
    @Modifying
    @Query("UPDATE User u SET u.role = :role WHERE u.username = :username")
    void updateUserRole(@Param("username") String username, @Param("role") Role role);

    @Modifying
    @Query("UPDATE User u SET u.role = :role WHERE u.id = :userId")
    void updateUserRoleById(@Param("userId") Long userId, @Param("role") Role role);

    List<User> findByRole(Role role);

    long countByRole(Role role);

    /**
     * Consulta para obtener usuarios por rol ordenados alfabéticamente por nombre y
     * apellido
     */
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.sede WHERE u.role = :role ORDER BY u.nombre ASC, u.apellido ASC")
    List<User> findByRoleOrderByNombreAscApellidoAsc(@Param("role") Role role);

    /**
     * Consulta paginada para obtener usuarios por rol ordenados alfabéticamente por
     * nombre y apellido
     */
    @Query(value = "SELECT u FROM User u WHERE u.role = :role ORDER BY u.nombre ASC, u.apellido ASC", countQuery = "SELECT COUNT(u) FROM User u WHERE u.role = :role")
    Page<User> findAllByRoleOrderedByNombreApellido(@Param("role") Role role, Pageable pageable);

    List<User> findByCoordinadorIdAndRole(Long coordinadorId, Role role);

    List<User> findByCoordinadorId(Long coordinadorId);

    @Query("SELECT CAST(u.role AS string) FROM User u WHERE u.id = :userId")
    String findRoleById(@Param("userId") Long userId);

    /* == UserRepository.java ================================================== */
    /* Mismo nombre de método, pero ahora devuelve solo los 5 campos necesarios  */
    /* mediante proyección DTO. No se toca nada más.                            */

    @Query(
            value = """
        SELECT new com.midas.crm.entity.DTO.asesor.AsesorDisponibleDTO(
                 u.id,
                 u.nombre,
                 u.apellido,
                 u.username,
                 COALESCE(u.sedeNombre, s.nombre)
               )
        FROM User u
        LEFT JOIN u.sede s
        WHERE u.role = :role
          AND u.coordinador IS NULL
    """,
            countQuery = """
        SELECT COUNT(u)
        FROM User u
        WHERE u.role = :role
          AND u.coordinador IS NULL
    """
    )
    Page<AsesorDisponibleDTO> findByRoleAndCoordinadorIsNull(
            @Param("role") Role role,
            Pageable pageable
    );




    @Query(value = """
       SELECT u
       FROM   User u
       WHERE  u.role = :role
         AND (
               LOWER(u.username)            LIKE LOWER(CONCAT('%', :q, '%'))
            OR LOWER(u.nombre)              LIKE LOWER(CONCAT('%', :q, '%'))
            OR LOWER(u.apellido)            LIKE LOWER(CONCAT('%', :q, '%'))
            OR LOWER(CONCAT(u.nombre,' ',u.apellido)) LIKE LOWER(CONCAT('%', :q, '%'))
         )
       ORDER BY u.nombre, u.apellido
""",
            countQuery = """
       SELECT COUNT(u)
       FROM   User u
       WHERE  u.role = :role
         AND ( LOWER(u.username) LIKE LOWER(CONCAT('%', :q, '%'))
            OR LOWER(u.nombre)   LIKE LOWER(CONCAT('%', :q, '%'))
            OR LOWER(u.apellido) LIKE LOWER(CONCAT('%', :q, '%'))
            OR LOWER(CONCAT(u.nombre,' ',u.apellido)) LIKE LOWER(CONCAT('%', :q, '%'))
         )
""")
    Page<User> searchCoordinadores(@Param("role") Role role,
                                   @Param("q")    String q,
                                   Pageable pageable);


    /* UserRepository.java */
    @Query(
            value = """
        SELECT new com.midas.crm.entity.DTO.asesor.AsesorDisponibleDTO(
                 u.id,
                 u.nombre,
                 u.apellido,
                 u.username,
                 COALESCE(u.sedeNombre, s.nombre)
               )
        FROM User u
        LEFT JOIN u.sede s
        WHERE u.role = :role
          AND u.coordinador IS NULL
          AND (
               LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :term, '%'))
            OR LOWER(u.username) LIKE LOWER(CONCAT('%', :term, '%'))
            OR LOWER(u.dni)      LIKE LOWER(CONCAT('%', :term, '%'))
          )
    """,
            countQuery = """
        SELECT COUNT(u)
        FROM User u
        WHERE u.role = :role
          AND u.coordinador IS NULL
          AND (
               LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :term, '%'))
            OR LOWER(u.username) LIKE LOWER(CONCAT('%', :term, '%'))
            OR LOWER(u.dni)      LIKE LOWER(CONCAT('%', :term, '%'))
          )
    """
    )
    Page<AsesorDisponibleDTO> findDisponiblesByTerm(
            @Param("role") Role role,
            @Param("term") String term,
            Pageable pageable);


    /**
     * Obtiene usuarios por sede y rol
     */
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.sede WHERE u.sede.id = :sedeId AND u.role = :role ORDER BY u.nombre ASC, u.apellido ASC")
    List<User> findBySedeIdAndRole(@Param("sedeId") Long sedeId, @Param("role") Role role);

    /**
     * Obtiene usuarios por rol y estado
     */
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.sede WHERE u.role = :role AND u.estado = :estado ORDER BY u.nombre ASC, u.apellido ASC")
    List<User> findByRoleAndEstado(@Param("role") Role role, @Param("estado") String estado);

    /**
     * Obtiene usuarios por sede y estado
     */
    @Query("SELECT u FROM User u " +
            "WHERE u.sede.id = :sedeId " +
            "AND u.estado = :estado " +
            "ORDER BY u.nombre ASC, u.apellido ASC")
    List<User> findBySedeIdAndEstado(@Param("sedeId") Long sedeId, @Param("estado") String estado);

    /**
     * Obtiene usuarios por sede y estado con paginación
     */
    @Query("SELECT u FROM User u " +
            "WHERE u.sede.id = :sedeId " +
            "AND u.estado = :estado " +
            "ORDER BY u.nombre ASC, u.apellido ASC")
    Page<User> findBySedeIdAndEstado(@Param("sedeId") Long sedeId, @Param("estado") String estado, Pageable pageable);

    /**
     * Obtiene usuarios por sede, estado y búsqueda con paginación
     */
    @Query("SELECT u FROM User u " +
            "WHERE u.sede.id = :sedeId " +
            "AND u.estado = :estado " +
            "AND (LOWER(u.nombre) LIKE LOWER(CONCAT('%', :search, '%')) " +
            "OR LOWER(u.apellido) LIKE LOWER(CONCAT('%', :search, '%')) " +
            "OR LOWER(u.username) LIKE LOWER(CONCAT('%', :search, '%'))) " +
            "ORDER BY u.nombre ASC, u.apellido ASC")
    Page<User> findBySedeIdAndEstadoAndSearch(@Param("sedeId") Long sedeId, @Param("estado") String estado,
                                              @Param("search") String search, Pageable pageable);

    /**
     * Obtiene usuarios por sede, rol y estado
     */
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.sede WHERE u.sede.id = :sedeId AND u.role = :role AND u.estado = :estado ORDER BY u.nombre ASC, u.apellido ASC")
    List<User> findBySedeIdAndRoleAndEstado(@Param("sedeId") Long sedeId, @Param("role") Role role,
                                            @Param("estado") String estado);

    /* ---------- BÚSQUEDA FULL-TEXT LIGERA ---------- */
    @Query("""
                SELECT u FROM User u
                WHERE (LOWER(u.username)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.nombre)      LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.apellido)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.email)       LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.telefono)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.dni)         LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.sedeNombre)  LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :q, '%')))
            """)
    Page<User> searchAllFields(@Param("q") String query, Pageable pageable);

    @Query("""
                SELECT u FROM User u
                WHERE (LOWER(u.username)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.nombre)      LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.apellido)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.email)       LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.telefono)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.dni)         LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.sedeNombre)  LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :q, '%')))
                  AND u.sede.id = :sedeId
            """)
    Page<User> searchAllFieldsBySede(@Param("q") String query,
                                     @Param("sedeId") Long sedeId,
                                     Pageable pageable);

    /**
     * Busca usuarios por todos los campos filtrados por estado
     */
    @Query("""
                SELECT u FROM User u
                WHERE (LOWER(u.username)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.nombre)      LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.apellido)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.email)       LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.telefono)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.dni)         LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.sedeNombre)  LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :q, '%')))
                  AND u.estado = :estado
            """)
    Page<User> searchAllFieldsByEstado(@Param("q") String query,
                                       @Param("estado") String estado,
                                       Pageable pageable);

    /**
     * Busca usuarios por todos los campos filtrados por sede y estado
     */
    @Query("""
                SELECT u FROM User u
                WHERE (LOWER(u.username)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.nombre)      LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.apellido)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.email)       LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.telefono)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.dni)         LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.sedeNombre)  LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :q, '%')))
                  AND u.sede.id = :sedeId
                  AND u.estado = :estado
            """)
    Page<User> searchAllFieldsBySedeAndEstado(@Param("q") String query,
                                              @Param("sedeId") Long sedeId,
                                              @Param("estado") String estado,
                                              Pageable pageable);

    /* ---------- BÚSQUEDA DE USUARIOS NO ASIGNADOS A UN CURSO ---------- */
    @Query("""
                SELECT u FROM User u
                WHERE u.id NOT IN (
                    SELECT cu.usuario.id FROM CursoUsuario cu WHERE cu.curso.id = :cursoId
                )
                AND (LOWER(u.username)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.nombre)      LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.apellido)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.email)       LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.telefono)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.dni)         LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.sedeNombre)  LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :q, '%')))
                ORDER BY u.nombre ASC, u.apellido ASC
            """)
    Page<User> searchUsuariosDisponiblesParaCurso(@Param("q") String query,
                                                  @Param("cursoId") Long cursoId,
                                                  Pageable pageable);

    @Query("""
                SELECT u FROM User u
                WHERE u.id NOT IN (
                    SELECT cu.usuario.id FROM CursoUsuario cu WHERE cu.curso.id = :cursoId
                )
                ORDER BY u.nombre ASC, u.apellido ASC
            """)
    Page<User> findUsuariosDisponiblesParaCurso(@Param("cursoId") Long cursoId, Pageable pageable);

    @Query("SELECT u FROM User u LEFT JOIN FETCH u.sede WHERE u.id = :id")
    Optional<User> findByIdWithSede(@Param("id") Long id);

    @Query("SELECT u FROM User u " +
            "LEFT JOIN FETCH u.sede s " +
            "LEFT JOIN FETCH u.coordinador c " +
            "WHERE u.id = :id")
    Optional<User> findByIdWithSedeAndCoordinador(@Param("id") Long id);

    @Query(
            value = """
    SELECT new com.midas.crm.entity.DTO.coordinador.CoordinadorListDTO(
      u.id,
      u.nombre,
      u.apellido,
      u.dni,
      COALESCE(u.sedeNombre, s.nombre),
      (SELECT COUNT(a) FROM User a WHERE a.coordinador.id = u.id AND a.role = com.midas.crm.entity.Role.ASESOR)
    )
    FROM User u
    LEFT JOIN u.sede s
    WHERE u.role = :role
      AND (   :term IS NULL
           OR LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :term, '%'))
           OR LOWER(u.dni) LIKE LOWER(CONCAT('%', :term, '%'))
          )
    ORDER BY u.nombre, u.apellido
  """,
            countQuery = """
    SELECT COUNT(u)
    FROM User u
    WHERE u.role = :role
      AND (   :term IS NULL
           OR LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :term, '%'))
           OR LOWER(u.dni) LIKE LOWER(CONCAT('%', :term, '%'))
          )
  """
    )
    Page<CoordinadorListDTO> findCoordinadorListByRoleAndTerm(
            @Param("role") Role role,
            @Param("term") String term,
            Pageable pageable
    );


    // ===== AGREGAR EN UserRepository.java =====

    /**
     * Obtiene los números de agente únicos utilizados por un usuario específico
     * CORREGIDO: numeroAgente está en ClienteResidencial
     */
    @Query("SELECT DISTINCT c.numeroAgente FROM ClienteResidencial c WHERE c.usuario.id = :usuarioId AND c.numeroAgente IS NOT NULL AND c.numeroAgente != ''")
    List<String> findNumerosAgenteByUsuarioId(@Param("usuarioId") Long usuarioId);

    /**
     * Obtiene todos los asesores de un coordinador con sus números de agente
     * CORREGIDO: Solucionado problema de collation
     */
    @Query(value = """
    SELECT DISTINCT u.codi_usuario, CONCAT(u.nombre, ' ', u.apellido), c.numero_agente 
    FROM usuarios u 
    INNER JOIN cliente_residencial c ON c.usuario_id = u.codi_usuario 
    WHERE u.coordinador_id = :coordinadorId 
    AND u.role COLLATE utf8mb4_unicode_ci = 'ASESOR' 
    AND c.numero_agente IS NOT NULL 
    AND c.numero_agente != ''
    ORDER BY u.nombre, u.apellido
    """, nativeQuery = true)
    List<Object[]> findAsesoresByCoordinadorConNumeroAgente(@Param("coordinadorId") Long coordinadorId);

    /**
     * Verifica si un número de agente existe y a qué asesor pertenece
     * CORREGIDO: Solucionado problema de collation
     */
    @Query(value = """
    SELECT DISTINCT u.codi_usuario, CONCAT(u.nombre, ' ', u.apellido), c.numero_agente 
    FROM usuarios u 
    INNER JOIN cliente_residencial c ON c.usuario_id = u.codi_usuario 
    WHERE c.numero_agente COLLATE utf8mb4_unicode_ci = :numeroAgente COLLATE utf8mb4_unicode_ci
    AND u.role COLLATE utf8mb4_unicode_ci = 'ASESOR'
    LIMIT 1
    """, nativeQuery = true)
    Optional<Object[]> findAsesorByNumeroAgente(@Param("numeroAgente") String numeroAgente);

    /**
     * 🔥 OPTIMIZADO: Usa idx_usuarios_sede_estado para COUNT
     */
    @Query("SELECT COUNT(u) FROM User u " +
            "WHERE u.sede.id = :sedeId " +
            "AND u.estado = :estado")
    long countBySedeIdAndEstado(@Param("sedeId") Long sedeId, @Param("estado") String estado);


    /**
     * 🔥 OPTIMIZADO: Buscar por username con EAGER loading
     */
    @Query("SELECT u FROM User u " +
            "LEFT JOIN FETCH u.sede s " +
            "LEFT JOIN FETCH u.coordinador c " +
            "WHERE u.username = :username")
    Optional<User> findByUsernameWithSedeAndCoordinador(@Param("username") String username);

}
