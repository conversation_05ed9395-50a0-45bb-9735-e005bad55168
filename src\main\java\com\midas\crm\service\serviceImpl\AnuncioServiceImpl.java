package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.*;
import com.midas.crm.entity.DTO.anuncio.*;
import com.midas.crm.entity.DTO.cache.CacheablePageDTO;
import com.midas.crm.event.*;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.AnuncioMapper;
import com.midas.crm.repository.AnuncioRepository;
import com.midas.crm.repository.SedeRepository;
import com.midas.crm.service.AnuncioService;
import com.midas.crm.service.UserService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.*;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AnuncioServiceImpl implements AnuncioService {

    /* ---------- L1 cache ---------- */
    private final Map<Long, Anuncio> anuncioL1 = new ConcurrentHashMap<>();
    private final Map<String, Page<AnuncioListDTO>> pageL1 = new ConcurrentHashMap<>();
    private final Map<String, CacheablePageDTO<AnuncioListDTO>> pageL1Cache = new ConcurrentHashMap<>();

    /* ---------- dependencias ---------- */
    private final AnuncioRepository repo;
    private final SedeRepository    sedeRepo;
    private final UserService       userService;
    private final ApplicationEventPublisher publisher;

    /* =============================================================== */
    /*                           CREATE                                */
    /* =============================================================== */
    @Override
    @Transactional
    @CacheEvict(cacheNames = { "anuncioById" }, allEntries = true)
    public Anuncio guardar(AnuncioDTO dto) {
        Anuncio a = build(dto);
        a = repo.save(a);
        publishAsync(new AnuncioCreatedEvent(this, a));
        clearL1();
        return a;
    }

    /* =============================================================== */
    /*                             READ                                */
    /* =============================================================== */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "anuncioById", key = "#id")
    public Anuncio obtenerPorId(Long id) {
        return anuncioL1.computeIfAbsent(id,
                k -> repo.findById(k)
                        .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ANUNCIO_NOT_FOUND)));
    }

    /* ---------- Helpers ---------- */
    private String k(int p,int s,Long sede,String tag){ return tag+":"+p+":"+s+":"+(sede==null?"*":sede); }
    private Page<AnuncioListDTO> toDto(Page<Anuncio> src){
        return src.map(AnuncioMapper::toListDTO);
    }
    private Page<AnuncioListDTO> toDtoProj(Page<AnuncioListProjection> src){
        return src.map(mapperProj());
    }

    /* ---------- listados básicos ---------- */
    @Override @Transactional(readOnly = true)
    @Cacheable(cacheNames = "anuncioPage", key = "{#page,#size}")
    public List<AnuncioListDTO> listarPaginado(int page,int size){
        String key = k(page,size,null,"GEN");
        return pageL1.computeIfAbsent(key,
                        k -> toDto(repo.findAll(PageRequest.of(page,size))))
                .getContent();
    }

    @Override @Transactional(readOnly = true)
    @Cacheable(cacheNames = "anuncioPageSede", key = "{#page,#size,#sedeId}")
    public List<AnuncioListDTO> listarPaginadoPorSede(int page,int size,Long sedeId){
        String key = k(page,size,sedeId,"GENS");
        return pageL1.computeIfAbsent(key,
                        k -> toDto(repo.findAll(PageRequest.of(page,size)))
                                .map(dto -> dto) /* Page reference */
                ).stream()
                .filter(dto -> dto.getSedeId()==null || sedeId.equals(dto.getSedeId()))
                .collect(Collectors.toList());
    }

    /* ---------- recientes ---------- */
    @Override
    @Transactional(readOnly = true)
    // Removido @Cacheable para evitar problemas de serialización con Page en Redis
    public Page<AnuncioListDTO> listarMasRecientesPaginado(int page,int size){
        String cacheKey = "REC:" + page + ":" + size;
        CacheablePageDTO<AnuncioListDTO> cached = pageL1Cache.computeIfAbsent(cacheKey, k -> {
            Page<AnuncioListDTO> result = toDtoProj(repo.listarAnunciosRecientes(PageRequest.of(page,size)));
            return CacheablePageDTO.from(result);
        });
        return cached.toPage();
    }

    @Override
    @Transactional(readOnly = true)
    // Removido @Cacheable para evitar problemas de serialización con Page en Redis
    public Page<AnuncioListDTO> listarMasRecientesPaginadoPorSede(int page,int size,Long sedeId){
        String cacheKey = "REC_SEDE:" + page + ":" + size + ":" + sedeId;
        CacheablePageDTO<AnuncioListDTO> cached = pageL1Cache.computeIfAbsent(cacheKey, k -> {
            Page<AnuncioListDTO> result = toDtoProj(repo.listarAnunciosRecientesPorSede(sedeId, PageRequest.of(page,size)));
            return CacheablePageDTO.from(result);
        });
        return cached.toPage();
    }

    /* ---------- activos/vigentes ---------- */
    @Override
    @Transactional(readOnly = true)
    // Removido @Cacheable para evitar problemas de serialización con Page en Redis
    public Page<AnuncioListDTO> listarAnunciosActivosYVigentes(int page,int size){
        String cacheKey = "ACT:" + page + ":" + size;
        CacheablePageDTO<AnuncioListDTO> cached = pageL1Cache.computeIfAbsent(cacheKey, k -> {
            Page<AnuncioListDTO> result = toDto(repo.findByEstadoAndFechaFinGreaterThanEqualOrFechaFinIsNullOrderByOrdenAsc(
                    "ACTIVO", LocalDateTime.now(), PageRequest.of(page,size)));
            return CacheablePageDTO.from(result);
        });
        return cached.toPage();
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "anuncioPageSede", key = "{'ACT',#page,#size,#sedeId}")
    public Page<AnuncioListDTO> listarAnunciosActivosYVigentesPorSede(int page,int size,Long sedeId){
        Page<Anuncio> raw = repo.findByEstadoAndFechaFinGreaterThanEqualOrFechaFinIsNullOrderByOrdenAsc(
                "ACTIVO", LocalDateTime.now(), PageRequest.of(page,size));

        List<AnuncioListDTO> filtrados = raw.stream()
                .filter(a -> a.getSede()==null || sedeId.equals(a.getSede().getId()))
                .map(AnuncioMapper::toListDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(filtrados, PageRequest.of(page,size), filtrados.size());
    }

    /* ---------- todos ---------- */
    @Override
    @Transactional(readOnly = true)
    // Removido @Cacheable para evitar problemas de serialización con Page en Redis
    public Page<AnuncioListDTO> listarTodosLosAnuncios(int page,int size){
        String cacheKey = "ALL:" + page + ":" + size;
        CacheablePageDTO<AnuncioListDTO> cached = pageL1Cache.computeIfAbsent(cacheKey, k -> {
            Page<AnuncioListDTO> result = toDtoProj(repo.listarTodosLosAnuncios(PageRequest.of(page,size)));
            return CacheablePageDTO.from(result);
        });
        return cached.toPage();
    }

    @Override
    @Transactional(readOnly = true)
    // Removido @Cacheable para evitar problemas de serialización con Page en Redis
    public Page<AnuncioListDTO> listarTodosLosAnunciosPorSede(int page,int size,Long sedeId){
        String cacheKey = "ALL_SEDE:" + page + ":" + size + ":" + sedeId;
        CacheablePageDTO<AnuncioListDTO> cached = pageL1Cache.computeIfAbsent(cacheKey, k -> {
            Page<AnuncioListDTO> result = toDtoProj(repo.listarTodosLosAnunciosPorSede(sedeId, PageRequest.of(page,size)));
            return CacheablePageDTO.from(result);
        });
        return cached.toPage();
    }

    /* =============================================================== */
    /*                UPDATE / DELETE / ORDEN                          */
    /* =============================================================== */
    @Override
    @Transactional
    @CacheEvict(cacheNames="anuncioById", key="#id")
    public AnuncioUpdateResponseDTO actualizarParcial(Long id, AnuncioDTO dto){
        Anuncio a = repo.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ANUNCIO_NOT_FOUND));
        patch(a,dto);
        a = repo.save(a);
        publishAsync(new AnuncioUpdatedEvent(this,a));
        invalidate(id);
        return AnuncioMapper.toUpdateResponseDTO(a);
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames="anuncioById", key="#id")
    public void eliminar(Long id){
        if(!repo.existsById(id))
            throw new MidasExceptions(MidasErrorMessage.ANUNCIO_NOT_FOUND);

        repo.deleteById(id);
        publishAsync(new AnuncioDeletedEvent(this,id));
        invalidate(id);
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames="anuncioById", key="#id")
    public AnuncioUpdateResponseDTO actualizarOrden(Long id,Integer orden){
        Anuncio a = repo.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ANUNCIO_NOT_FOUND));
        a.setOrden(orden);
        a = repo.save(a);
        publishAsync(new AnuncioUpdatedEvent(this,a));
        invalidate(id);
        return AnuncioMapper.toUpdateResponseDTO(a);
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames={ "anuncioById" }, allEntries=true)
    public int actualizarAnunciosExpirados(){
        List<Anuncio> exp = repo.findByEstadoAndFechaFinLessThan("ACTIVO", LocalDateTime.now());
        exp.forEach(a -> a.setEstado("INACTIVO"));
        repo.saveAll(exp);
        exp.forEach(a -> publishAsync(new AnuncioUpdatedEvent(this,a)));
        clearL1();
        return exp.size();
    }

    /* =============================================================== */
    /*                      OTROS / HELPERS                            */
    /* =============================================================== */
    @Override @Transactional(readOnly = true)
    public List<Sede> obtenerTodasLasSedes(){ return sedeRepo.findAllActive(); }

    private void clearL1(){
        anuncioL1.clear();
        pageL1.clear();
        pageL1Cache.clear();
    }

    private void invalidate(Long id){
        anuncioL1.remove(id);
        pageL1.clear();
        pageL1Cache.clear();
    }

    private Anuncio build(AnuncioDTO d){
        Anuncio a = Anuncio.builder()
                .titulo(d.getTitulo())
                .descripcion(d.getDescripcion())
                .imagenUrl(d.getImagenUrl())
                .categoria(d.getCategoria())
                .fechaPublicacion(LocalDateTime.now())
                .fechaInicio(d.getFechaInicio())
                .fechaFin(d.getFechaFin())
                .orden(Optional.ofNullable(d.getOrden()).orElse(0))
                .estado(Optional.ofNullable(d.getEstado()).orElse("ACTIVO"))
                .usuario(userService.findUserById(d.getUsuarioId()))
                .build();
        if(d.getSedeId()!=null) sedeRepo.findById(d.getSedeId()).ifPresent(a::setSede);
        return a;
    }
    private void patch(Anuncio a, AnuncioDTO d){
        Optional.ofNullable(d.getTitulo())     .ifPresent(a::setTitulo);
        Optional.ofNullable(d.getDescripcion()).ifPresent(a::setDescripcion);
        Optional.ofNullable(d.getImagenUrl())  .ifPresent(a::setImagenUrl);
        Optional.ofNullable(d.getCategoria())  .ifPresent(a::setCategoria);
        Optional.ofNullable(d.getFechaInicio()).ifPresent(a::setFechaInicio);
        Optional.ofNullable(d.getFechaFin())   .ifPresent(a::setFechaFin);
        Optional.ofNullable(d.getOrden())      .ifPresent(a::setOrden);
        Optional.ofNullable(d.getEstado())     .ifPresent(a::setEstado);
        if(d.getSedeId()!=null)
            sedeRepo.findById(d.getSedeId()).ifPresent(a::setSede);
        else a.setSede(null);
    }

    private Function<AnuncioListProjection,AnuncioListDTO> mapperProj(){
        return p -> AnuncioListDTO.builder()
                .id(p.getId()).titulo(p.getTitulo()).descripcion(p.getDescripcion())
                .imagenUrl(p.getImagenUrl()).categoria(p.getCategoria())
                .fechaPublicacion(p.getFechaPublicacion()).fechaInicio(p.getFechaInicio())
                .fechaFin(p.getFechaFin()).orden(p.getOrden()).estado(p.getEstado())
                .nombreUsuario(p.getNombreUsuario()).sedeId(p.getSedeId()).nombreSede(p.getNombreSede())
                .build();
    }

    @Async void publishAsync(Object e){ publisher.publishEvent(e); }
}
