package com.midas.crm.controller;

import com.midas.crm.entity.DTO.transcription.TAListadoDTO;
import com.midas.crm.entity.TranscriptionAnalysis;
import com.midas.crm.service.TranscriptionAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Controlador para manejar análisis de transcripción
 */
@RestController
@RequestMapping("${api.route.transcriptionanalysis}")
@Slf4j
@CrossOrigin(origins = "*")
public class TranscriptionAnalysisController {

    private final TranscriptionAnalysisService transcriptionAnalysisService;

    public TranscriptionAnalysisController(TranscriptionAnalysisService transcriptionAnalysisService) {
        this.transcriptionAnalysisService = transcriptionAnalysisService;
    }

    @GetMapping
    public ResponseEntity<Page<TAListadoDTO>> getAllAnalysisDTO(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        try {
            log.info("🔍 Obteniendo análisis DTO - Página: {}, Tamaño: {}", page, size);

            Pageable pageable = PageRequest.of(page, size, Sort.by("fechaCreacion").descending());

            LocalDateTime fin = LocalDateTime.now();
            LocalDateTime ini = fin.minusYears(1);

            Page<TAListadoDTO> listado = transcriptionAnalysisService.findListadoDTO(ini, fin, pageable);
            return ResponseEntity.ok(listado);
        } catch (Exception e) {
            log.error("❌ Error al obtener análisis con DTO: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }


    /**
     * Obtiene análisis por ID del cliente residencial
     */
    @GetMapping("/cliente/{clienteId}")
    public ResponseEntity<TranscriptionAnalysis> getAnalysisByClienteId(@PathVariable Long clienteId) {
        try {
            log.info("🔍 Buscando análisis para cliente ID: {}", clienteId);
            Optional<TranscriptionAnalysis> analysis = transcriptionAnalysisService.findByClienteResidencialId(clienteId);

            if (analysis.isPresent()) {
                return ResponseEntity.ok(analysis.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("❌ Error al buscar análisis para cliente {}: {}", clienteId, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene todos los análisis de un cliente residencial
     */
    @GetMapping("/cliente/{clienteId}/all")
    public ResponseEntity<List<TranscriptionAnalysis>> getAllAnalysisByClienteId(@PathVariable Long clienteId) {
        try {
            log.info("🔍 Buscando todos los análisis para cliente ID: {}", clienteId);
            List<TranscriptionAnalysis> analysisList = transcriptionAnalysisService.findAllByClienteResidencialId(clienteId);
            return ResponseEntity.ok(analysisList);
        } catch (Exception e) {
            log.error("❌ Error al buscar análisis para cliente {}: {}", clienteId, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene análisis por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<TranscriptionAnalysis> getAnalysisById(@PathVariable Long id) {
        try {
            log.info("🔍 Buscando análisis ID: {}", id);
            Optional<TranscriptionAnalysis> analysis = transcriptionAnalysisService.findById(id);

            if (analysis.isPresent()) {
                return ResponseEntity.ok(analysis.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("❌ Error al buscar análisis {}: {}", id, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene análisis por rango de fechas con paginación
     */
    @GetMapping("/fecha-rango")
    public ResponseEntity<Page<TranscriptionAnalysis>> getAnalysisByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "fechaCreacion") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        try {
            log.info("🔍 Buscando análisis entre {} y {} - Página: {}, Tamaño: {}",
                    fechaInicio, fechaFin, page, size);

            LocalDateTime inicio = fechaInicio.atStartOfDay();
            LocalDateTime fin = fechaFin.atTime(LocalTime.MAX);

            Sort sort = sortDir.equalsIgnoreCase("desc") ?
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();

            Pageable pageable = PageRequest.of(page, size, sort);

            Page<TranscriptionAnalysis> analysisPage = transcriptionAnalysisService
                    .findByFechaCreacionBetween(inicio, fin, pageable);

            return ResponseEntity.ok(analysisPage);
        } catch (Exception e) {
            log.error("❌ Error al buscar análisis por rango de fechas: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene análisis por nivel de confianza
     */
    @GetMapping("/nivel-confianza/{nivel}")
    public ResponseEntity<List<TranscriptionAnalysis>> getAnalysisByNivelConfianza(@PathVariable String nivel) {
        try {
            log.info("🔍 Buscando análisis con nivel de confianza: {}", nivel);
            List<TranscriptionAnalysis> analysisList = transcriptionAnalysisService.findByNivelConfianza(nivel);
            return ResponseEntity.ok(analysisList);
        } catch (Exception e) {
            log.error("❌ Error al buscar análisis por nivel de confianza {}: {}", nivel, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene análisis por número de agente
     */
    @GetMapping("/agente/{numeroAgente}")
    public ResponseEntity<List<TranscriptionAnalysis>> getAnalysisByNumeroAgente(@PathVariable String numeroAgente) {
        try {
            log.info("🔍 Buscando análisis para agente: {}", numeroAgente);
            List<TranscriptionAnalysis> analysisList = transcriptionAnalysisService.findByNumeroAgente(numeroAgente);
            return ResponseEntity.ok(analysisList);
        } catch (Exception e) {
            log.error("❌ Error al buscar análisis para agente {}: {}", numeroAgente, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene análisis por móvil de contacto
     */
    @GetMapping("/movil/{movilContacto}")
    public ResponseEntity<List<TranscriptionAnalysis>> getAnalysisByMovilContacto(@PathVariable String movilContacto) {
        try {
            log.info("🔍 Buscando análisis para móvil: {}", movilContacto);
            List<TranscriptionAnalysis> analysisList = transcriptionAnalysisService.findByMovilContacto(movilContacto);
            return ResponseEntity.ok(analysisList);
        } catch (Exception e) {
            log.error("❌ Error al buscar análisis para móvil {}: {}", movilContacto, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene análisis con porcentaje promedio mayor o igual al especificado
     */
    @GetMapping("/porcentaje-minimo/{porcentaje}")
    public ResponseEntity<List<TranscriptionAnalysis>> getAnalysisByPorcentajeMinimo(@PathVariable Double porcentaje) {
        try {
            log.info("🔍 Buscando análisis con porcentaje >= {}", porcentaje);
            List<TranscriptionAnalysis> analysisList = transcriptionAnalysisService
                    .findByPorcentajePromedioGreaterThanEqual(porcentaje);
            return ResponseEntity.ok(analysisList);
        } catch (Exception e) {
            log.error("❌ Error al buscar análisis por porcentaje mínimo {}: {}", porcentaje, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene análisis con porcentaje promedio menor al especificado
     */
    @GetMapping("/porcentaje-maximo/{porcentaje}")
    public ResponseEntity<List<TranscriptionAnalysis>> getAnalysisByPorcentajeMaximo(@PathVariable Double porcentaje) {
        try {
            log.info("🔍 Buscando análisis con porcentaje < {}", porcentaje);
            List<TranscriptionAnalysis> analysisList = transcriptionAnalysisService
                    .findByPorcentajePromedioLessThan(porcentaje);
            return ResponseEntity.ok(analysisList);
        } catch (Exception e) {
            log.error("❌ Error al buscar análisis por porcentaje máximo {}: {}", porcentaje, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene estadísticas generales de análisis
     */
    @GetMapping("/estadisticas")
    public ResponseEntity<Map<String, Object>> getEstadisticas(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin) {

        try {
            log.info("📊 Obteniendo estadísticas generales");

            Map<String, Object> estadisticas = new java.util.HashMap<>();

            // Estadísticas básicas
            long totalAnalisis = transcriptionAnalysisService.countByEstado("COMPLETED") +
                    transcriptionAnalysisService.countByEstado("PROCESSING") +
                    transcriptionAnalysisService.countByEstado("ERROR");

            estadisticas.put("totalAnalisis", totalAnalisis);
            estadisticas.put("completados", transcriptionAnalysisService.countByEstado("COMPLETED"));
            estadisticas.put("altaConfianza", transcriptionAnalysisService.countByNivelConfianza("ALTO"));
            estadisticas.put("promedioGeneral", 85.5); // Valor por defecto

            return ResponseEntity.ok(estadisticas);
        } catch (Exception e) {
            log.error("❌ Error al obtener estadísticas: {}", e.getMessage());

            // Devolver estadísticas por defecto en caso de error
            Map<String, Object> defaultStats = new java.util.HashMap<>();
            defaultStats.put("totalAnalisis", 0);
            defaultStats.put("completados", 0);
            defaultStats.put("altaConfianza", 0);
            defaultStats.put("promedioGeneral", 0.0);

            return ResponseEntity.ok(defaultStats);
        }
    }

    /**
     * Obtiene resumen general de análisis
     */
    @GetMapping("/resumen")
    public ResponseEntity<Map<String, Object>> getResumen() {
        try {
            log.info("📊 Obteniendo resumen general");

            Map<String, Object> resumen = new java.util.HashMap<>();
            resumen.put("totalAnalisis", 0);
            resumen.put("promedioCalidad", 0.0);
            resumen.put("analisesRecientes", 0);

            return ResponseEntity.ok(resumen);
        } catch (Exception e) {
            log.error("❌ Error al obtener resumen: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene los últimos análisis
     */
    @GetMapping("/latest")
    public ResponseEntity<List<TranscriptionAnalysis>> getLatestAnalysis() {
        try {
            log.info("🔍 Obteniendo últimos análisis");
            List<TranscriptionAnalysis> analysisList = transcriptionAnalysisService.findLatestAnalysis();
            return ResponseEntity.ok(analysisList);
        } catch (Exception e) {
            log.error("❌ Error al obtener últimos análisis: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Obtiene análisis con problemas
     */
    @GetMapping("/problematic")
    public ResponseEntity<List<TranscriptionAnalysis>> getProblematicAnalysis() {
        try {
            log.info("🔍 Obteniendo análisis problemáticos");
            List<TranscriptionAnalysis> analysisList = transcriptionAnalysisService.findProblematicAnalysis();
            return ResponseEntity.ok(analysisList);
        } catch (Exception e) {
            log.error("❌ Error al obtener análisis problemáticos: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Verifica si existe un análisis para un cliente
     */
    @GetMapping("/exists/cliente/{clienteId}")
    public ResponseEntity<Map<String, Boolean>> existsAnalysisForCliente(@PathVariable Long clienteId) {
        try {
            boolean exists = transcriptionAnalysisService.existsByClienteResidencialId(clienteId);
            return ResponseEntity.ok(Map.of("exists", exists));
        } catch (Exception e) {
            log.error("❌ Error al verificar existencia de análisis para cliente {}: {}", clienteId, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Elimina un análisis por ID
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, String>> deleteAnalysis(@PathVariable Long id) {
        try {
            log.info("🗑️ Eliminando análisis ID: {}", id);
            transcriptionAnalysisService.deleteById(id);
            return ResponseEntity.ok(Map.of("message", "Análisis eliminado exitosamente"));
        } catch (Exception e) {
            log.error("❌ Error al eliminar análisis {}: {}", id, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Cuenta análisis por estado
     */
    @GetMapping("/count/estado/{estado}")
    public ResponseEntity<Map<String, Long>> countByEstado(@PathVariable String estado) {
        try {
            long count = transcriptionAnalysisService.countByEstado(estado);
            return ResponseEntity.ok(Map.of("count", count));
        } catch (Exception e) {
            log.error("❌ Error al contar análisis por estado {}: {}", estado, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Cuenta análisis por nivel de confianza
     */
    @GetMapping("/count/nivel-confianza/{nivel}")
    public ResponseEntity<Map<String, Long>> countByNivelConfianza(@PathVariable String nivel) {
        try {
            long count = transcriptionAnalysisService.countByNivelConfianza(nivel);
            return ResponseEntity.ok(Map.of("count", count));
        } catch (Exception e) {
            log.error("❌ Error al contar análisis por nivel de confianza {}: {}", nivel, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }
}
