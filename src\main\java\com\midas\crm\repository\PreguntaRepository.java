package com.midas.crm.repository;

import com.midas.crm.entity.Cuestionario;
import com.midas.crm.entity.Pregunta;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PreguntaRepository extends JpaRepository<Pregunta, Long> {
    List<Pregunta> findByCuestionarioOrderByOrdenAsc(Cuestionario cuestionario);
    List<Pregunta> findByCuestionarioIdOrderByOrdenAsc(Long cuestionarioId);
    int countByCuestionarioId(Long cuestionarioId);
}
