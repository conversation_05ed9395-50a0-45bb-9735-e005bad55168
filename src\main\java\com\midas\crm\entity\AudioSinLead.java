package com.midas.crm.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * Entidad para registrar audios encontrados en Google Drive que no tienen un lead correspondiente
 * Esto permite identificar audios huérfanos para análisis futuro
 */
@Entity
@Table(name = "audio_sin_lead")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class AudioSinLead {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Nombre completo del archivo de audio
     */
    @Column(name = "nombre_archivo", length = 500, nullable = false)
    private String nombreArchivo;

    /**
     * URL del archivo en Google Drive
     */
    @Column(name = "url_google_drive", length = 1000, nullable = true)
    private String urlGoogleDrive;

    /**
     * ID del archivo en Google Drive
     */
    @Column(name = "google_drive_file_id", length = 100, nullable = true)
    private String googleDriveFileId;

    /**
     * Móvil extraído del nombre del archivo
     */
    @Column(name = "movil_extraido", length = 20, nullable = true)
    private String movilExtraido;

    /**
     * Agente extraído del nombre del archivo
     */
    @Column(name = "agente_extraido", length = 10, nullable = true)
    private String agenteExtraido;

    /**
     * Fecha extraída del nombre del archivo
     */
    @Column(name = "fecha_extraida", nullable = true)
    private LocalDateTime fechaExtraida;

    /**
     * Fecha en que se encontró el archivo
     */
    @Column(name = "fecha_encontrado", nullable = false)
    @Builder.Default
    private LocalDateTime fechaEncontrado = LocalDateTime.now();

    /**
     * Fecha de procesamiento de búsqueda
     */
    @Column(name = "fecha_procesamiento", nullable = false)
    private LocalDateTime fechaProcesamiento;

    /**
     * Estado del audio sin lead (ENCONTRADO, PROCESADO, IGNORADO)
     */
    @Column(name = "estado", length = 20, nullable = false)
    @Builder.Default
    private String estado = "ENCONTRADO";

    /**
     * Tamaño del archivo en bytes
     */
    @Column(name = "tamano_archivo", nullable = true)
    private Long tamanoArchivo;

    /**
     * Tipo MIME del archivo
     */
    @Column(name = "tipo_mime", length = 50, nullable = true)
    private String tipoMime;

    /**
     * Carpeta donde se encontró el archivo
     */
    @Column(name = "carpeta_origen", length = 200, nullable = true)
    private String carpetaOrigen;

    /**
     * Observaciones adicionales
     */
    @Column(name = "observaciones", length = 1000, nullable = true)
    private String observaciones;

    /**
     * Indica si se intentó buscar un lead para este audio
     */
    @Column(name = "busqueda_lead_realizada", nullable = false)
    @Builder.Default
    private Boolean busquedaLeadRealizada = false;

    /**
     * Criterios de búsqueda utilizados
     */
    @Column(name = "criterios_busqueda", length = 500, nullable = true)
    private String criteriosBusqueda;

    /**
     * Motivo por el cual no se encontró el lead
     */
    @Column(name = "motivo_sin_lead", length = 500, nullable = true)
    private String motivoSinLead;

    /**
     * Fecha de última actualización
     */
    @Column(name = "fecha_actualizacion", nullable = true)
    private LocalDateTime fechaActualizacion;

    /**
     * Usuario o proceso que registró este audio
     */
    @Column(name = "registrado_por", length = 100, nullable = true)
    private String registradoPor;

    /**
     * Prioridad para procesamiento futuro (ALTA, MEDIA, BAJA)
     */
    @Column(name = "prioridad", length = 10, nullable = false)
    @Builder.Default
    private String prioridad = "MEDIA";

    /**
     * Indica si este audio debe ser procesado en el futuro
     */
    @Column(name = "requiere_procesamiento", nullable = false)
    @Builder.Default
    private Boolean requiereProcesamiento = true;

    @PreUpdate
    protected void onUpdate() {
        fechaActualizacion = LocalDateTime.now();
    }

    /**
     * Genera un resumen del audio para logs
     */
    public String getResumen() {
        return String.format("Audio: %s | Móvil: %s | Agente: %s | Fecha: %s",
                nombreArchivo, movilExtraido, agenteExtraido,
                fechaExtraida != null ? fechaExtraida.toLocalDate() : "N/A");
    }

    /**
     * Verifica si la información extraída es válida
     */
    public boolean isInformacionValida() {
        return movilExtraido != null && !movilExtraido.trim().isEmpty() &&
               agenteExtraido != null && !agenteExtraido.trim().isEmpty();
    }

    /**
     * Genera criterios de búsqueda para el lead
     */
    public String generarCriteriosBusqueda() {
        return String.format("movil=%s, agente=%s, fecha=%s",
                movilExtraido, agenteExtraido,
                fechaExtraida != null ? fechaExtraida.toLocalDate() : "N/A");
    }
}
