package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.IndependentTranscriptionDTO;
import com.midas.crm.entity.IndependentTranscription;
import com.midas.crm.entity.IndependentTranscription.TranscriptionStatus;
import com.midas.crm.repository.IndependentTranscriptionRepository;
import com.midas.crm.service.IndependentTranscriptionService;
import com.midas.crm.service.GoogleDriveService;
import com.midas.crm.utils.GoogleDriveOrganizationHelper;
import com.midas.crm.utils.GoogleDriveOrganizationHelper.FolderType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Implementación del servicio para transcripciones independientes
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class IndependentTranscriptionServiceImpl implements IndependentTranscriptionService {

    private final IndependentTranscriptionRepository repository;
    private final GoogleDriveService googleDriveService;
    private final GoogleDriveOrganizationHelper driveOrganizationHelper;

    // Configuración
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    private static final Set<String> ALLOWED_MIME_TYPES = Set.of(
            "audio/mpeg", "audio/mp3", "audio/wav", "audio/m4a",
            "audio/aac", "audio/ogg", "audio/webm", "audio/flac"
    );
    private static final String TRANSCRIPTIONS_FOLDER_NAME = "Transcripciones_Independientes";

    @Override
    public IndependentTranscription createTranscription(MultipartFile audioFile, String fileName,
                                                        String whisperModel, String targetLanguage,
                                                        List<String> tags, String notes, String createdBy) {
        log.info("Creando nueva transcripción independiente: {}", fileName);

        // Validar archivo
        validateAudioFile(audioFile);

        // Crear entidad
        IndependentTranscription transcription = new IndependentTranscription();
        transcription.setFileName(fileName != null ? fileName : generateFileName(audioFile.getOriginalFilename()));
        transcription.setOriginalFileName(audioFile.getOriginalFilename());
        transcription.setFileSize(audioFile.getSize());
        transcription.setMimeType(audioFile.getContentType());
        transcription.setWhisperModel(whisperModel != null ? whisperModel : "base");
        transcription.setLanguage(targetLanguage);
        transcription.setCreatedBy(createdBy);
        transcription.setNotes(notes);
        transcription.setStatus(TranscriptionStatus.PENDING);

        // Establecer etiquetas
        if (tags != null && !tags.isEmpty()) {
            transcription.setTagsFromArray(tags.toArray(new String[0]));
        }

        // Guardar en base de datos
        transcription = repository.save(transcription);

        // Subir archivo de audio a Google Drive de forma asíncrona
        try {
            String audioFileId = uploadAudioToGoogleDrive(audioFile, transcription.getFileName());
            transcription.setAudioFileId(audioFileId);
            transcription.setAudioFileUrl("https://drive.google.com/file/d/" + audioFileId + "/view");
            repository.save(transcription);
        } catch (Exception e) {
            log.error("Error subiendo archivo de audio a Google Drive: {}", e.getMessage(), e);
            // No fallar la creación por error de subida
        }

        log.info("Transcripción independiente creada con ID: {}", transcription.getId());
        return transcription;
    }

    /**
     * 🔧 MÉTODO MEJORADO: createTranscriptionWithData con organización automática
     */
    @Override
    public IndependentTranscription createTranscriptionWithData(MultipartFile audioFile, String fileName,
                                                                String whisperModel, String targetLanguage,
                                                                List<String> tags, String notes,
                                                                String transcriptionText, Double confidence,
                                                                String language, Long processingTime,
                                                                Integer duration, String status,
                                                                String driveUrl, String callId, String createdBy) {
        log.info("📁 Creando transcripción independiente con organización mejorada: {}", fileName);

        // Validar archivo
        validateAudioFile(audioFile);

        // Crear entidad con todos los datos ya procesados
        IndependentTranscription transcription = new IndependentTranscription();
        transcription.setFileName(fileName != null ? fileName : generateFileName(audioFile.getOriginalFilename()));
        transcription.setOriginalFileName(audioFile.getOriginalFilename());
        transcription.setFileSize(audioFile.getSize());
        transcription.setMimeType(audioFile.getContentType());
        transcription.setWhisperModel(whisperModel != null ? whisperModel : "base");
        transcription.setCreatedBy(createdBy);
        transcription.setNotes(notes);

        // Datos de la transcripción ya procesados
        transcription.setTranscriptionText(transcriptionText);
        transcription.setConfidence(confidence);
        transcription.setLanguage(language != null ? language : targetLanguage);
        transcription.setProcessingTime(processingTime);
        transcription.setDuration(duration);

        // Guardar callId en las notas si no hay notas previas
        if (callId != null) {
            String existingNotes = transcription.getNotes();
            if (existingNotes == null || existingNotes.trim().isEmpty()) {
                transcription.setNotes("Call ID: " + callId);
            } else {
                transcription.setNotes(existingNotes + " | Call ID: " + callId);
            }
        }

        // Establecer estado basado en el parámetro o COMPLETED por defecto
        if (status != null) {
            try {
                transcription.setStatus(TranscriptionStatus.valueOf(status.toUpperCase()));
            } catch (IllegalArgumentException e) {
                transcription.setStatus(TranscriptionStatus.COMPLETED);
            }
        } else {
            transcription.setStatus(TranscriptionStatus.COMPLETED);
        }

        // Establecer etiquetas
        if (tags != null && !tags.isEmpty()) {
            transcription.setTagsFromArray(tags.toArray(new String[0]));
        }

        // Guardar en base de datos
        transcription = repository.save(transcription);

        // 📁 SUBIR ARCHIVO DE AUDIO CON ORGANIZACIÓN MEJORADA
        try {
            String audioFileId = uploadAudioToGoogleDriveEnhanced(audioFile, transcription.getFileName());
            transcription.setAudioFileId(audioFileId);
            transcription.setAudioFileUrl("https://drive.google.com/file/d/" + audioFileId + "/view");

            log.info("🎵 Audio subido con organización mejorada - ID: {}", audioFileId);
        } catch (Exception e) {
            log.error("❌ Error subiendo archivo de audio: {}", e.getMessage(), e);
            // Fallback al método original
            try {
                String audioFileId = uploadAudioToGoogleDrive(audioFile, transcription.getFileName());
                transcription.setAudioFileId(audioFileId);
                transcription.setAudioFileUrl("https://drive.google.com/file/d/" + audioFileId + "/view");
            } catch (Exception fallbackEx) {
                log.error("❌ Error en fallback de subida de audio: {}", fallbackEx.getMessage());
            }
        }

        // 📄 SUBIR TRANSCRIPCIÓN CON ORGANIZACIÓN MEJORADA
        if (transcriptionText != null && !transcriptionText.trim().isEmpty()) {
            try {
                String transcriptionFileUrl = uploadTranscriptionToGoogleDrive(
                        transcriptionText, transcription.getFileName(), transcription.getCreatedAt());

                transcription.setTranscriptionFileUrl(transcriptionFileUrl);
                log.info("📄 Transcripción subida con organización mejorada");
            } catch (Exception e) {
                log.error("❌ Error subiendo transcripción organizada: {}", e.getMessage(), e);
                // El driveUrl del parámetro puede ser usado como fallback
                if (driveUrl != null) {
                    transcription.setTranscriptionFileUrl(driveUrl);
                    log.info("📄 Usando URL de Drive del parámetro como fallback");
                }
            }
        } else if (driveUrl != null) {
            transcription.setTranscriptionFileUrl(driveUrl);
        }

        // Guardar cambios finales
        transcription = repository.save(transcription);

        log.info("✅ Transcripción independiente creada con organización mejorada - ID: {}", transcription.getId());
        return transcription;
    }

    /**
     * 🎵 SUBIDA DE AUDIO MEJORADA CON ORGANIZACIÓN POR FECHA
     */
    private String uploadAudioToGoogleDriveEnhanced(MultipartFile file, String fileName) throws IOException {
        try {
            log.info("🎵 Subiendo audio con organización mejorada: {}", fileName);

            // 1. 📁 OBTENER CARPETA ORGANIZADA (usar fecha actual)
            String folderId = getOrCreateDateFolder(LocalDateTime.now());

            // 2. 📝 GENERAR NOMBRE MEJORADO PARA EL AUDIO
            String enhancedAudioName = generateEnhancedAudioFileName(fileName);

            // 3. ☁️ SUBIR ARCHIVO
            String fileId = googleDriveService.uploadFile(file, enhancedAudioName, folderId);

            log.info("✅ Audio subido con organización mejorada - Archivo: {}", enhancedAudioName);
            return fileId;

        } catch (Exception e) {
            log.error("❌ Error en subida mejorada de audio: {}", e.getMessage());
            throw new IOException("Error subiendo audio con organización mejorada", e);
        }
    }

    /**
     * 📝 GENERA NOMBRE MEJORADO PARA ARCHIVOS DE AUDIO INDEPENDIENTES
     */
    private String generateEnhancedAudioFileName(String originalFileName) {
        StringBuilder fileName = new StringBuilder();

        // 🕐 TIMESTAMP
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter timestampFormatter = DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss");
        fileName.append(now.format(timestampFormatter));

        // 📄 NOMBRE ORIGINAL (limpio)
        String cleanOriginalName = originalFileName
                .replaceAll("[^a-zA-Z0-9_\\-]", "_")
                .replaceAll("_+", "_")
                .replaceAll("^\\_|\\._$", "")
                .replaceAll("\\.(mp3|wav|m4a|aac|ogg|flac)$", "");

        if (!cleanOriginalName.isEmpty()) {
            fileName.append("_").append(cleanOriginalName);
        }

        // 🏷️ TIPO
        fileName.append("_audio_independiente");

        // ✅ MANTENER EXTENSIÓN ORIGINAL
        String extension = getFileExtension(originalFileName);
        fileName.append(extension);

        return fileName.toString();
    }

    /**
     * 🔧 UTILITARIO: Obtiene extensión del archivo
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return ".mp3"; // Extensión por defecto
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<IndependentTranscription> getTranscriptionById(Long id) {
        return repository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<IndependentTranscriptionDTO> getAllTranscriptions(Pageable pageable) {
        Page<IndependentTranscription> transcriptions = repository.findAll(pageable);
        return transcriptions.map(this::convertToDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<IndependentTranscriptionDTO> searchTranscriptions(
            List<TranscriptionStatus> statuses, String fileName, String language, String createdBy,
            LocalDateTime startDate, LocalDateTime endDate, Integer minDuration, Integer maxDuration,
            Double minConfidence, Double maxConfidence, Pageable pageable) {

        Page<IndependentTranscription> transcriptions = repository.findWithFilters(
                statuses, fileName, language, createdBy, startDate, endDate,
                minDuration, maxDuration, minConfidence, maxConfidence, pageable);

        return transcriptions.map(this::convertToDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<IndependentTranscriptionDTO> searchByContent(String query, Pageable pageable) {
        Page<IndependentTranscription> transcriptions = repository.searchByContent(query, pageable);
        return transcriptions.map(this::convertToDTO);
    }

    @Override
    public IndependentTranscription updateTranscription(Long id, String fileName, List<String> tags, String notes) {
        IndependentTranscription transcription = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));

        if (fileName != null) {
            transcription.setFileName(fileName);
        }
        if (tags != null) {
            transcription.setTagsFromArray(tags.toArray(new String[0]));
        }
        if (notes != null) {
            transcription.setNotes(notes);
        }

        return repository.save(transcription);
    }

    @Override
    public void deleteTranscription(Long id) {
        IndependentTranscription transcription = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));

        // Eliminar archivos de Google Drive
        try {
            if (transcription.getAudioFileId() != null) {
                // googleDriveService.deleteFile(transcription.getAudioFileId());
            }
            if (transcription.getTranscriptionFileId() != null) {
                // googleDriveService.deleteFile(transcription.getTranscriptionFileId());
            }
        } catch (Exception e) {
            log.warn("Error eliminando archivos de Google Drive: {}", e.getMessage());
        }

        repository.delete(transcription);
        log.info("Transcripción eliminada: {}", id);
    }

    @Override
    public void processTranscription(Long id) {
        IndependentTranscription transcription = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));

        transcription.setStatus(TranscriptionStatus.PROCESSING);
        repository.save(transcription);

        // TODO: Implementar llamada al servicio de transcripción de IA
        // Esto debería ser manejado por un sistema de colas (RabbitMQ)
        log.info("Transcripción marcada para procesamiento: {}", id);
    }

    @Override
    public IndependentTranscription retryTranscription(Long id) {
        IndependentTranscription transcription = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));

        if (!transcription.isFailed()) {
            throw new RuntimeException("Solo se pueden reintentar transcripciones fallidas");
        }

        transcription.setStatus(TranscriptionStatus.PENDING);
        transcription = repository.save(transcription);

        // Procesar transcripción
        processTranscription(id);

        return transcription;
    }

    @Override
    public IndependentTranscription cancelTranscription(Long id) {
        IndependentTranscription transcription = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));

        if (!transcription.isProcessing()) {
            throw new RuntimeException("Solo se pueden cancelar transcripciones en proceso");
        }

        transcription.setStatus(TranscriptionStatus.CANCELLED);
        return repository.save(transcription);
    }

    @Override
    @Transactional(readOnly = true)
    public TranscriptionStatus getTranscriptionStatus(Long id) {
        return repository.findById(id)
                .map(IndependentTranscription::getStatus)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));
    }

    @Override
    public void updateTranscriptionStatus(Long id, TranscriptionStatus status) {
        IndependentTranscription transcription = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));

        transcription.setStatus(status);
        repository.save(transcription);
    }

    @Override
    public void saveTranscriptionResult(Long id, String transcriptionText, Double confidence,
                                        String language, Long processingTime) {
        IndependentTranscription transcription = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));

        transcription.setTranscriptionText(transcriptionText);
        transcription.setConfidence(confidence);
        transcription.setLanguage(language);
        transcription.setProcessingTime(processingTime);
        transcription.setStatus(TranscriptionStatus.COMPLETED);

        // Subir archivo de transcripción a Google Drive
        try {
            String transcriptionFileId = uploadTranscriptionToGoogleDrive(
                    transcriptionText, transcription.getFileName(), transcription.getCreatedAt());
            transcription.setTranscriptionFileId(transcriptionFileId);
            transcription.setTranscriptionFileUrl("https://drive.google.com/file/d/" + transcriptionFileId + "/view");
        } catch (Exception e) {
            log.error("Error subiendo transcripción a Google Drive: {}", e.getMessage(), e);
        }

        repository.save(transcription);
        log.info("Resultado de transcripción guardado para ID: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getStatistics() {
        try {
            log.info("📊 Obteniendo estadísticas de transcripciones independientes");

            Object[] stats = repository.getGeneralStatistics();
            List<Object[]> statusCounts = repository.countByStatus();
            List<Object[]> languageCounts = repository.countByLanguage();

            Map<String, Object> result = new HashMap<>();

            // La consulta getGeneralStatistics() devuelve un Object[] con 4 valores:
            // [COUNT(t), SUM(duration), AVG(confidence), SUM(fileSize)]
            if (stats != null && stats.length >= 4) {
                result.put("total", stats[0] != null ? ((Number) stats[0]).longValue() : 0L);
                result.put("totalDuration", stats[1] != null ? ((Number) stats[1]).longValue() : 0L);
                result.put("averageConfidence", stats[2] != null ? ((Number) stats[2]).doubleValue() : 0.0);
                result.put("totalFileSize", stats[3] != null ? ((Number) stats[3]).longValue() : 0L);

                log.info("📊 Estadísticas procesadas: total={}, duration={}, confidence={}, fileSize={}",
                        stats[0], stats[1], stats[2], stats[3]);
            } else {
                // Valores por defecto si no hay datos
                result.put("total", 0L);
                result.put("totalDuration", 0L);
                result.put("averageConfidence", 0.0);
                result.put("totalFileSize", 0L);

                log.warn("⚠️ No se obtuvieron estadísticas válidas, usando valores por defecto");
            }

            // Convertir conteos por estado
            Map<String, Long> byStatus = statusCounts.stream()
                    .collect(Collectors.toMap(
                            arr -> arr[0].toString(),
                            arr -> ((Number) arr[1]).longValue()
                    ));
            result.put("byStatus", byStatus);

            // Convertir conteos por idioma
            Map<String, Long> byLanguage = languageCounts.stream()
                    .collect(Collectors.toMap(
                            arr -> arr[0].toString(),
                            arr -> ((Number) arr[1]).longValue()
                    ));
            result.put("byLanguage", byLanguage);

            // Si el total de la consulta general es 0 pero hay conteos por estado, usar la suma de estados
            Long totalFromStatus = byStatus.values().stream().mapToLong(Long::longValue).sum();
            if (((Number) result.get("total")).longValue() == 0 && totalFromStatus > 0) {
                result.put("total", totalFromStatus);
                log.info("📊 Corrigiendo total desde conteos por estado: {}", totalFromStatus);
            }

            log.info("✅ Estadísticas obtenidas exitosamente: total={}", result.get("total"));
            return result;

        } catch (Exception e) {
            log.error("❌ Error obteniendo estadísticas de transcripciones independientes: {}", e.getMessage(), e);

            // Devolver estadísticas por defecto en caso de error
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("total", 0L);
            defaultStats.put("totalDuration", 0L);
            defaultStats.put("averageConfidence", 0.0);
            defaultStats.put("totalFileSize", 0L);
            defaultStats.put("byStatus", new HashMap<String, Long>());
            defaultStats.put("byLanguage", new HashMap<String, Long>());

            return defaultStats;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getPopularTags(int limit) {
        List<Object[]> tagCounts = repository.findPopularTags(limit);
        return tagCounts.stream()
                .map(arr -> {
                    Map<String, Object> tag = new HashMap<>();
                    tag.put("tag", arr[0]);
                    tag.put("count", arr[1]);
                    return tag;
                })
                .collect(Collectors.toList());
    }

    @Override
    public byte[] exportToWord(List<Long> transcriptionIds, boolean includeMetadata,
                               boolean includeTimestamps, String documentTitle, String documentAuthor) {
        // TODO: Implementar exportación a Word usando Apache POI
        throw new UnsupportedOperationException("Exportación a Word no implementada aún");
    }

    @Override
    @Transactional(readOnly = true)
    public String getDownloadUrl(Long id, String format) {
        IndependentTranscription transcription = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));

        if (!transcription.isCompleted()) {
            throw new RuntimeException("La transcripción debe estar completada para descargar");
        }

        // Retornar URL de Google Drive
        return transcription.getTranscriptionFileUrl();
    }

    @Override
    @Transactional(readOnly = true)
    public byte[] downloadTranscription(Long id, String format) {
        IndependentTranscription transcription = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transcripción no encontrada: " + id));

        if (!transcription.isCompleted()) {
            throw new RuntimeException("La transcripción debe estar completada para descargar");
        }

        // TODO: Implementar descarga desde Google Drive
        return transcription.getTranscriptionText().getBytes();
    }

    @Override
    public String uploadAudioToGoogleDrive(MultipartFile file, String fileName) {
        try {
            String folderId = getOrCreateDateFolder(LocalDateTime.now());
            return googleDriveService.uploadFile(file, fileName, folderId);
        } catch (Exception e) {
            log.error("Error subiendo archivo de audio a Google Drive: {}", e.getMessage(), e);
            throw new RuntimeException("Error subiendo archivo de audio", e);
        }
    }

    /**
     * 🔧 ACTUALIZACIÓN: Método para subir transcripción con contenido enriquecido
     */
    @Override
    public String uploadTranscriptionToGoogleDrive(String transcriptionText, String fileName, LocalDateTime date) {
        try {
            log.info("📁 Subiendo transcripción independiente organizada: {}", fileName);

            // 1. 📁 OBTENER CARPETA ORGANIZADA POR FECHA
            String folderId = getOrCreateDateFolder(date);

            // 2. 📝 CREAR NOMBRE DE ARCHIVO MEJORADO
            String enhancedFileName = generateEnhancedIndependentFileName(fileName, date);

            // 3. 📄 CREAR CONTENIDO ENRIQUECIDO
            String enrichedContent = createEnrichedIndependentContent(transcriptionText, fileName, date);

            // 4. ☁️ SUBIR ARCHIVO
            String fileId = googleDriveService.uploadFile(
                    enrichedContent.getBytes(StandardCharsets.UTF_8),
                    enhancedFileName,
                    "text/plain",
                    folderId
            );

            // 5. 🔗 OBTENER URL PÚBLICA
            String publicUrl = googleDriveService.getPublicLink(fileId);

            log.info("✅ Transcripción independiente guardada:");
            log.info("📁 Estructura: TRANSCRIPCIONES_INDEPENDIENTES/{}/{}/{}",
                    date.getYear(),
                    String.format("%02d-%s", date.getMonthValue(),
                            date.getMonth().getDisplayName(TextStyle.FULL, Locale.forLanguageTag("es-ES")).toUpperCase()),
                    date.format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
            log.info("📄 Archivo: {}", enhancedFileName);

            return publicUrl;

        } catch (Exception e) {
            log.error("❌ Error subiendo transcripción independiente: {}", e.getMessage(), e);
            throw new RuntimeException("Error subiendo transcripción independiente", e);
        }
    }

    /**
     * 📝 GENERA NOMBRE DE ARCHIVO MEJORADO PARA TRANSCRIPCIONES INDEPENDIENTES
     */
    private String generateEnhancedIndependentFileName(String originalFileName, LocalDateTime date) {
        StringBuilder fileName = new StringBuilder();

        // 🕐 TIMESTAMP: YYYYMMDD-HHMMSS
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter timestampFormatter = DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss");
        fileName.append(now.format(timestampFormatter));

        // 📄 NOMBRE ORIGINAL (limpio)
        String cleanOriginalName = originalFileName
                .replaceAll("[^a-zA-Z0-9_\\-]", "_")  // Caracteres seguros
                .replaceAll("_+", "_")                // Múltiples underscores a uno
                .replaceAll("^\\_|\\._$", "")         // Quitar al inicio/final
                .replaceAll("\\.txt$", "");           // Remover extensión

        if (!cleanOriginalName.isEmpty()) {
            fileName.append("_").append(cleanOriginalName);
        }

        // 📅 FECHA ORIGINAL (si es diferente de hoy)
        LocalDate fechaArchivo = date.toLocalDate();
        if (!fechaArchivo.equals(LocalDate.now())) {
            fileName.append("_").append(fechaArchivo.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        }

        // 🏷️ TIPO
        fileName.append("_independiente");

        // ✅ EXTENSIÓN
        fileName.append(".txt");

        return fileName.toString();
    }


    private String createEnrichedIndependentContent(String transcriptionText, String originalFileName, LocalDateTime date) {
        StringBuilder content = new StringBuilder();

        // 📋 ENCABEZADO
        content.append("TRANSCRIPCIÓN INDEPENDIENTE - CRM MIDAS\n");
        content.append(String.join("", Collections.nCopies(50, "=")));
        content.append("\n\n");

        // 📊 METADATOS
        content.append("📊 INFORMACIÓN DE LA TRANSCRIPCIÓN:\n");
        content.append("─".repeat(35)).append("\n");
        content.append("📄 Archivo Original: ").append(originalFileName).append("\n");
        content.append("📅 Fecha Archivo: ").append(date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))).append("\n");
        content.append("📅 Fecha Procesamiento: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))).append("\n");
        content.append("🔖 Tipo: Transcripción Independiente\n");
        content.append("🤖 Sistema: CRM Midas - Módulo de Transcripciones\n");

        // 📄 CONTENIDO
        content.append("\n").append(String.join("", Collections.nCopies(50, "=")));
        content.append("\n📄 CONTENIDO TRANSCRITO:\n");
        content.append(String.join("", Collections.nCopies(50, "=")));
        content.append("\n\n");
        content.append(transcriptionText != null ? transcriptionText : "No se pudo obtener la transcripción");
        content.append("\n\n");
        content.append(String.join("", Collections.nCopies(50, "=")));
        content.append("\nFin de la transcripción independiente - Generado automáticamente");

        return content.toString();
    }


    /**
     * 📁 VERSIÓN MEJORADA: Crea carpetas organizadas por fecha para transcripciones independientes
     * MANTIENE LA ESTRUCTURA EXISTENTE PERO LA DOCUMENTA MEJOR
     */
    @Override
    public String getOrCreateDateFolder(LocalDateTime date) {
        try {
            LocalDate fecha = date.toLocalDate();
            log.info("📁 Creando estructura organizada para transcripción independiente - Fecha: {}", fecha);

            // 📂 USAR HELPER CENTRALIZADO PARA ESTRUCTURA CONSISTENTE
            String folderId = driveOrganizationHelper.getOrCreateDateStructure(
                    FolderType.TRANSCRIPCIONES_INDEPENDIENTES,
                    fecha
            );

            log.info("✅ Estructura para transcripción independiente creada usando helper centralizado");
            return folderId;

        } catch (Exception e) {
            log.error("❌ Error creando carpeta por fecha para transcripción independiente: {}", e.getMessage(), e);
            throw new RuntimeException("Error creando carpeta", e);
        }
    }


    /**
     * 🔍 Busca o crea subcarpeta para transcripciones independientes
     */
    private String buscarOCrearSubcarpetaIndependiente(String carpetaPadreId, String nombreCarpeta) throws IOException {
        try {
            // Buscar subcarpeta existente
            List<Map<String, Object>> carpetas = googleDriveService.listFiles(carpetaPadreId, 100);

            for (Map<String, Object> carpeta : carpetas) {
                String mimeType = (String) carpeta.get("mimeType");
                String name = (String) carpeta.get("name");
                if ("application/vnd.google-apps.folder".equals(mimeType) && nombreCarpeta.equals(name)) {
                    String carpetaId = (String) carpeta.get("id");
                    log.debug("📁 Subcarpeta independiente encontrada: {} (ID: {})", nombreCarpeta, carpetaId);
                    return carpetaId;
                }
            }

            // Crear nueva subcarpeta
            String nuevaCarpetaId = googleDriveService.createFolder(nombreCarpeta, carpetaPadreId);
            log.info("📁 Subcarpeta independiente creada: {} (ID: {})", nombreCarpeta, nuevaCarpetaId);
            return nuevaCarpetaId;

        } catch (Exception e) {
            log.error("❌ Error con subcarpeta independiente '{}': {}", nombreCarpeta, e.getMessage());
            throw new IOException("Error con subcarpeta independiente: " + nombreCarpeta, e);
        }
    }

    /**
     * Busca una carpeta de fecha específica dentro de la carpeta padre
     */
    private String findDateFolderInParent(String parentFolderId, String dateFolder) {
        try {
            List<Map<String, Object>> folders = googleDriveService.listFiles(parentFolderId, 100);
            for (Map<String, Object> folder : folders) {
                String mimeType = (String) folder.get("mimeType");
                String name = (String) folder.get("name");
                if ("application/vnd.google-apps.folder".equals(mimeType) && dateFolder.equals(name)) {
                    return (String) folder.get("id");
                }
            }
            return null;
        } catch (Exception e) {
            log.warn("Error buscando carpeta de fecha: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public void validateAudioFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("El archivo no puede estar vacío");
        }

        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("El archivo es demasiado grande. Máximo: 100MB");
        }

        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_MIME_TYPES.contains(contentType)) {
            throw new IllegalArgumentException("Formato de archivo no soportado: " + contentType);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<IndependentTranscription> getPendingTranscriptions() {
        return repository.findByStatusOrderByCreatedAtAsc(TranscriptionStatus.PENDING);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IndependentTranscriptionDTO> getRecentTranscriptions(int hours) {
        LocalDateTime since = LocalDateTime.now().minusHours(hours);
        List<IndependentTranscription> transcriptions = repository.findRecentTranscriptions(since);
        return convertToDTOs(transcriptions);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countTranscriptionsByUser(String createdBy, LocalDateTime since) {
        return repository.countByCreatedByAndCreatedAtAfter(createdBy, since);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IndependentTranscription> findPotentialDuplicates(String originalFileName, Long fileSize) {
        return repository.findPotentialDuplicates(originalFileName, fileSize);
    }

    @Override
    public void cleanupOldTranscriptions(int daysOld) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);
        repository.deleteOldTranscriptions(cutoffDate);
        log.info("Transcripciones antiguas eliminadas antes de: {}", cutoffDate);
    }

    @Override
    public IndependentTranscriptionDTO convertToDTO(IndependentTranscription transcription) {
        IndependentTranscriptionDTO dto = new IndependentTranscriptionDTO();
        dto.setId(transcription.getId());
        dto.setFileName(transcription.getFileName());
        dto.setOriginalFileName(transcription.getOriginalFileName());
        dto.setTranscriptionText(transcription.getTranscriptionText());
        dto.setAudioFileUrl(transcription.getAudioFileUrl());
        dto.setTranscriptionFileUrl(transcription.getTranscriptionFileUrl());
        dto.setDuration(transcription.getDuration());
        dto.setFileSize(transcription.getFileSize());
        dto.setMimeType(transcription.getMimeType());
        dto.setWhisperModel(transcription.getWhisperModel());
        dto.setConfidence(transcription.getConfidence());
        dto.setLanguage(transcription.getLanguage());
        dto.setProcessingTime(transcription.getProcessingTime());
        dto.setStatus(transcription.getStatus());
        dto.setCreatedAt(transcription.getCreatedAt());
        dto.setUpdatedAt(transcription.getUpdatedAt());
        dto.setCreatedBy(transcription.getCreatedBy());
        dto.setNotes(transcription.getNotes());

        // Convertir etiquetas
        if (transcription.getTags() != null) {
            dto.setTags(Arrays.asList(transcription.getTagsArray()));
        }

        return dto;
    }

    @Override
    public List<IndependentTranscriptionDTO> convertToDTOs(List<IndependentTranscription> transcriptions) {
        return transcriptions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Genera un nombre de archivo único
     */
    private String generateFileName(String originalFileName) {
        if (originalFileName == null) {
            return "transcripcion_" + System.currentTimeMillis();
        }

        String nameWithoutExtension = originalFileName.replaceFirst("[.][^.]+$", "");
        return nameWithoutExtension + "_" + System.currentTimeMillis();
    }
}
