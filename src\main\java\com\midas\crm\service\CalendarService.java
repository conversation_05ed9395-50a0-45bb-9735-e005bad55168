package com.midas.crm.service;

import com.midas.crm.entity.Calendar;
import com.midas.crm.entity.DTO.calendar.CalendarDTO;
import com.midas.crm.entity.DTO.calendar.CalendarResponseDTO;
import com.midas.crm.utils.GenericResponse;

import java.util.List;

public interface CalendarService {
    GenericResponse<List<Calendar>> getAll();
    GenericResponse<List<Calendar>> getFilterByUser(Long userId);

    GenericResponse<List<CalendarResponseDTO>> getFilterByDates(CalendarDTO filter);

    GenericResponse<Calendar> getById(Long id);
    GenericResponse<?> create(CalendarDTO dto);
    GenericResponse<?> update(CalendarDTO dto, Long id);
    GenericResponse<String> delete(Long id);
    GenericResponse<String> restore(Long id);
}
