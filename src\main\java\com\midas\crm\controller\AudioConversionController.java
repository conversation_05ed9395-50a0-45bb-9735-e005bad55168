package com.midas.crm.controller;

import com.midas.crm.service.AudioConversionService;
import com.midas.crm.service.GoogleDriveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;



/**
 * Controlador REST para conversión de archivos de audio
 */
@RestController
@RequestMapping("/api/audio-conversion")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class AudioConversionController {

    private final AudioConversionService audioConversionService;
    private final GoogleDriveService googleDriveService;

    /**
     * Convierte un archivo GSM a MP3
     */
    @PostMapping(value = "/gsm-to-mp3", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> convertGsmToMp3(
            @RequestParam("gsmFile") MultipartFile gsmFile,
            @RequestParam(value = "outputFileName", required = false) String outputFileName) {

        try {

            // Validar que el archivo no esté vacío
            if (gsmFile.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body("El archivo GSM está vacío");
            }

            // Validar que sea un archivo GSM
            if (!isGsmFile(gsmFile)) {
                return ResponseEntity.badRequest()
                        .body("El archivo no es un formato GSM válido");
            }

            // Generar nombre de salida si no se proporciona
            if (outputFileName == null || outputFileName.trim().isEmpty()) {
                outputFileName = gsmFile.getOriginalFilename().replaceAll("\\.[^.]+$", "");
            }

            // Guardar archivo temporal
            String tempGsmPath = saveTemporaryFile(gsmFile);

            try {
                // Convertir GSM a MP3 usando el servicio existente pero con archivo local
                String mp3Url = convertLocalGsmToMp3(tempGsmPath, outputFileName);

                // Leer el archivo MP3 convertido
                byte[] mp3Data = readConvertedFile(mp3Url);

                // Preparar respuesta
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType("audio/mpeg"));
                headers.setContentDispositionFormData("attachment", outputFileName + ".mp3");
                headers.setContentLength(mp3Data.length);


                return ResponseEntity.ok()
                        .headers(headers)
                        .body(new ByteArrayResource(mp3Data));

            } finally {
                // Limpiar archivo temporal
                cleanupTempFile(tempGsmPath);
            }

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error interno del servidor: " + e.getMessage());
        }
    }

    /**
     * Verifica si un archivo es GSM
     */
    private boolean isGsmFile(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String contentType = file.getContentType();

        if (fileName != null && fileName.toLowerCase().endsWith(".gsm")) {
            return true;
        }

        if (contentType != null && (contentType.equals("audio/x-gsm") || contentType.equals("audio/gsm"))) {
            return true;
        }

        return false;
    }

    /**
     * Guarda un archivo temporal
     */
    private String saveTemporaryFile(MultipartFile file) throws IOException {
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
        String tempPath = tempDir + "/" + fileName;

        file.transferTo(Paths.get(tempPath));

        return tempPath;
    }

    /**
     * Lee el archivo MP3 convertido
     */
    private byte[] readConvertedFile(String mp3Url) throws IOException {
        if (mp3Url.startsWith("file://")) {
            String filePath = mp3Url.substring(7);
            return Files.readAllBytes(Paths.get(filePath));
        } else if (mp3Url.contains("drive.google.com")) {
            // Extraer el ID del archivo de Google Drive desde la URL
            String fileId = extractGoogleDriveFileId(mp3Url);
            if (fileId != null) {
                // Usar GoogleDriveService para descargar el archivo
                return downloadFromGoogleDrive(fileId);
            } else {
                throw new IOException("No se pudo extraer el ID del archivo de Google Drive desde: " + mp3Url);
            }
        } else {
            throw new IOException("URL no soportada para lectura: " + mp3Url);
        }
    }

    /**
     * Extrae el ID del archivo de Google Drive desde una URL
     */
    private String extractGoogleDriveFileId(String url) {
        // URL típica: https://drive.google.com/uc?export=download&id=FILE_ID
        if (url.contains("id=")) {
            String[] parts = url.split("id=");
            if (parts.length > 1) {
                String fileId = parts[1];
                // Remover cualquier parámetro adicional
                if (fileId.contains("&")) {
                    fileId = fileId.split("&")[0];
                }
                return fileId;
            }
        }
        return null;
    }

    /**
     * Descarga un archivo desde Google Drive usando el servicio
     */
    private byte[] downloadFromGoogleDrive(String fileId) throws IOException {
        try {
            // Usar GoogleDriveService para descargar el archivo
            return googleDriveService.downloadFile(fileId);
        } catch (Exception e) {
            throw new IOException("Error descargando archivo desde Google Drive: " + e.getMessage(), e);
        }
    }

    /**
     * Limpia archivos temporales
     */
    private void cleanupTempFile(String filePath) {
        try {
            Files.deleteIfExists(Paths.get(filePath));
        } catch (IOException e) {
        }
    }

    /**
     * Verifica si el servicio de conversión está disponible
     */
    @GetMapping("/status")
    public ResponseEntity<?> getConversionStatus() {
        try {
            boolean available = audioConversionService.isFFmpegAvailable();
            return ResponseEntity.ok()
                    .body(java.util.Map.of(
                            "available", available,
                            "message", available ? "Servicio de conversión disponible" : "FFmpeg no disponible"
                    ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error verificando estado del servicio");
        }
    }

    /**
     * Convierte un archivo GSM local a MP3 usando el servicio existente
     */
    private String convertLocalGsmToMp3(String gsmFilePath, String outputFileName) throws IOException {
        // Crear una URL file:// para el archivo local
        String fileUrl = "file://" + gsmFilePath.replace("\\", "/");

        // Usar el servicio existente pero configurado para no subir a Drive
        return audioConversionService.convertGsmToMp3(fileUrl, outputFileName);
    }
}
