package com.midas.crm.utils.errorHandler;

import com.midas.crm.service.serviceImpl.UserConnectionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.WebSocketHandlerDecorator;

/**
 * Decorador para manejar errores de transporte WebSocket
 * No es un componente Spring, se configura manualmente
 */
@Slf4j
public class WebSocketSessionHandler extends WebSocketHandlerDecorator {

    private final UserConnectionServiceImpl userConnectionService;

    public WebSocketSessionHandler(WebSocketHandler delegate, UserConnectionServiceImpl userConnectionService) {
        super(delegate);
        this.userConnectionService = userConnectionService;
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("Error de transporte WebSocket: {}", exception.getMessage());

        // Obtener el ID de sesión
        String sessionId = session.getId();

        // Notificar al servicio de conexiones para que limpie la sesión
        try {
            userConnectionService.removeSession(sessionId);
        } catch (Exception e) {
            log.error("Error al limpiar sesión después de error de transporte: {}", e.getMessage());
        }

        // Llamar al manejador delegado
        super.handleTransportError(session, exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        log.debug("Conexión WebSocket cerrada: {} con estado: {}", session.getId(), closeStatus);

        // Notificar al servicio de conexiones para que limpie la sesión
        try {
            userConnectionService.removeSession(session.getId());
        } catch (Exception e) {
            log.error("Error al limpiar sesión después de cierre de conexión: {}", e.getMessage());
        }

        // Llamar al manejador delegado
        super.afterConnectionClosed(session, closeStatus);
    }
}