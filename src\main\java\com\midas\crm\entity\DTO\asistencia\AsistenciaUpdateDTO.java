package com.midas.crm.entity.DTO.asistencia;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

public class AsistenciaUpdateDTO {

    @NotNull(message = "La nueva fecha y hora de salida no puede ser nula.")
    private LocalDateTime nuevaFechaHoraSalida;

    private String observacionAdmin;

    // Getters y Setters
    public LocalDateTime getNuevaFechaHoraSalida() { return nuevaFechaHoraSalida; }
    public void setNuevaFechaHoraSalida(LocalDateTime nuevaFechaHoraSalida) { this.nuevaFechaHoraSalida = nuevaFechaHoraSalida; }
    public String getObservacionAdmin() { return observacionAdmin; }
    public void setObservacionAdmin(String observacionAdmin) { this.observacionAdmin = observacionAdmin; }
}