package com.midas.crm.entity.DTO.queue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO para mensajes de cola de transcripciones independientes
 * Maneja múltiples archivos de audio para procesamiento en lotes
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IndependentTranscriptionQueueMessage {

    // Identificación del lote
    private String batchId;
    private String userId; // Usuario que inició el procesamiento
    private String userName;

    // Información de los archivos
    private List<AudioFileInfo> audioFiles;
    private Integer totalFiles;
    private Integer processedFiles;
    private Integer failedFiles;

    // Configuración de transcripción
    private String whisperModel; // small, base, large, etc.
    private String device; // cpu, gpu
    private String targetLanguage; // es, en, etc.
    private List<String> tags;
    private String notes;

    // Control de procesamiento
    private String estadoProcesamiento; // PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED
    private String mensajeError;
    private LocalDateTime fechaEnvio;
    private LocalDateTime fechaInicioProcesamiento;
    private LocalDateTime fechaFinProcesamiento;
    private Integer intentos;
    private Integer maxIntentos;

    // Información de la cola
    private String queueName;
    private String routingKey;

    // Configuración de procesamiento concurrente
    private Integer maxConcurrentProcessing; // Máximo 3 archivos simultáneos
    private Integer currentlyProcessing;

    /**
     * Información de un archivo de audio individual
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AudioFileInfo {
        private String fileId; // ID único del archivo
        private String fileName;
        private String originalFileName;
        private String googleDriveFileId; // ID del archivo en Google Drive
        private String googleDriveUrl;
        private Long fileSize;
        private String mimeType;

        // Estado del archivo individual
        private String status; // PENDING, PROCESSING, COMPLETED, FAILED
        private String errorMessage;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Integer duration; // Duración en segundos
        private Double confidence; // Confianza de la transcripción

        // Resultado de la transcripción
        private String transcriptionText;
        private String transcriptionLanguage;
        private String googleDriveTranscriptionUrl; // URL del archivo .txt en Google Drive

        // Metadatos adicionales
        private String extractedInfo; // Información extraída del nombre del archivo
    }

    /**
     * Verifica si se han agotado los intentos
     */
    public boolean intentosAgotados() {
        return intentos != null && maxIntentos != null && intentos >= maxIntentos;
    }

    /**
     * Incrementa el contador de intentos
     */
    public void incrementarIntentos() {
        if (intentos == null) {
            intentos = 1;
        } else {
            intentos++;
        }
    }

    /**
     * Obtiene el progreso del procesamiento como porcentaje
     */
    public double getProgreso() {
        if (totalFiles == null || totalFiles == 0) {
            return 0.0;
        }
        int completados = (processedFiles != null ? processedFiles : 0) + (failedFiles != null ? failedFiles : 0);
        return (double) completados / totalFiles * 100.0;
    }

    /**
     * Verifica si el procesamiento está completo
     */
    public boolean isCompleto() {
        if (totalFiles == null || totalFiles == 0) {
            return false;
        }
        int completados = (processedFiles != null ? processedFiles : 0) + (failedFiles != null ? failedFiles : 0);
        return completados >= totalFiles;
    }

    /**
     * Obtiene el siguiente archivo pendiente de procesamiento
     */
    public AudioFileInfo getNextPendingFile() {
        if (audioFiles == null) {
            return null;
        }
        return audioFiles.stream()
                .filter(file -> "PENDING".equals(file.getStatus()))
                .findFirst()
                .orElse(null);
    }

    /**
     * Obtiene los archivos que están siendo procesados actualmente
     */
    public List<AudioFileInfo> getCurrentlyProcessingFiles() {
        if (audioFiles == null) {
            return List.of();
        }
        return audioFiles.stream()
                .filter(file -> "PROCESSING".equals(file.getStatus()))
                .toList();
    }

    /**
     * Marca un archivo como completado
     */
    public void markFileAsCompleted(String fileId, String transcriptionText, String transcriptionLanguage,
                                    Double confidence, String googleDriveUrl) {
        if (audioFiles != null) {
            audioFiles.stream()
                    .filter(file -> fileId.equals(file.getFileId()))
                    .findFirst()
                    .ifPresent(file -> {
                        file.setStatus("COMPLETED");
                        file.setEndTime(LocalDateTime.now());
                        file.setTranscriptionText(transcriptionText);
                        file.setTranscriptionLanguage(transcriptionLanguage);
                        file.setConfidence(confidence);
                        file.setGoogleDriveTranscriptionUrl(googleDriveUrl);
                    });

            // Actualizar contadores
            processedFiles = (processedFiles != null ? processedFiles : 0) + 1;
        }
    }

    /**
     * Marca un archivo como fallido
     */
    public void markFileAsFailed(String fileId, String errorMessage) {
        if (audioFiles != null) {
            audioFiles.stream()
                    .filter(file -> fileId.equals(file.getFileId()))
                    .findFirst()
                    .ifPresent(file -> {
                        file.setStatus("FAILED");
                        file.setEndTime(LocalDateTime.now());
                        file.setErrorMessage(errorMessage);
                    });

            // Actualizar contadores
            failedFiles = (failedFiles != null ? failedFiles : 0) + 1;
        }
    }

    /**
     * Marca un archivo como en procesamiento
     */
    public void markFileAsProcessing(String fileId) {
        if (audioFiles != null) {
            audioFiles.stream()
                    .filter(file -> fileId.equals(file.getFileId()))
                    .findFirst()
                    .ifPresent(file -> {
                        file.setStatus("PROCESSING");
                        file.setStartTime(LocalDateTime.now());
                    });

            // Actualizar contador de archivos en procesamiento
            currentlyProcessing = (currentlyProcessing != null ? currentlyProcessing : 0) + 1;
        }
    }

    /**
     * Reduce el contador de archivos en procesamiento
     */
    public void decrementCurrentlyProcessing() {
        if (currentlyProcessing != null && currentlyProcessing > 0) {
            currentlyProcessing--;
        }
    }

    /**
     * Verifica si se puede procesar otro archivo (máximo 3 simultáneos)
     */
    public boolean canProcessMoreFiles() {
        int maxConcurrent = maxConcurrentProcessing != null ? maxConcurrentProcessing : 3;
        int current = currentlyProcessing != null ? currentlyProcessing : 0;
        return current < maxConcurrent && getNextPendingFile() != null;
    }
}
