package com.midas.crm.controller;

import com.midas.crm.entity.DTO.gmail.EmailDtos;
import com.midas.crm.service.GmailService;
import com.midas.crm.utils.GenericResponse;
// Asumo que tienes una clase similar a esta para las constantes.
// Si no la tienes, puedes reemplazar <PERSON>ricResponseConstants.SUCCESS por 1
// y GenericResponseConstants.ERROR por 0.
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("${api.route.gmail}") // Define esta ruta en application.properties
@RequiredArgsConstructor
@Slf4j
public class GmailController {

    private final GmailService gmailService;

    @PostMapping("/enviar")
    public ResponseEntity<GenericResponse<Void>> enviarEmail(@RequestBody EmailDtos.EnviarEmailRequest request) {
        try {
            log.info("Recibida solicitud para enviar email a: {}", request.para());

            // Validar datos de entrada
            if (request.para() == null || request.para().trim().isEmpty()) {
                log.warn("Intento de envío de email sin destinatario");
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(GenericResponseConstants.ERROR, "El destinatario es obligatorio", null));
            }

            if (request.asunto() == null || request.asunto().trim().isEmpty()) {
                log.warn("Intento de envío de email sin asunto");
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(GenericResponseConstants.ERROR, "El asunto es obligatorio", null));
            }

            gmailService.enviarEmail(request);
            log.info("Email enviado exitosamente a: {}", request.para());
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Email enviado exitosamente a " + request.para(), null)
            );
        } catch (MessagingException e) {
            log.error("Error de mensajería al enviar email a {}: {}", request.para(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Error de mensajería: " + e.getMessage(), null));
        } catch (IllegalArgumentException e) {
            log.error("Error de validación al enviar email a {}: {}", request.para(), e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Error de validación: " + e.getMessage(), null));
        } catch (Exception e) {
            log.error("Error inesperado al enviar email a {}: {}", request.para(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Error interno del servidor: " + e.getMessage(), null));
        }
    }

    @GetMapping("/bandeja-entrada")
    public ResponseEntity<GenericResponse<List<EmailDtos.EmailMensajeResumen>>> getBandejaDeEntrada(
            @RequestParam(defaultValue = "25") int limit) {
        try {
            List<EmailDtos.EmailMensajeResumen> mensajes = gmailService.getBandejaDeEntrada(limit);
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Bandeja de entrada obtenida exitosamente.", mensajes)
            );
        } catch (IOException e) {
            log.error("Error al obtener la bandeja de entrada de Gmail: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Fallo al obtener la bandeja de entrada: " + e.getMessage(), null));
        }
    }

    @GetMapping("/mensajes/{idMensaje}")
    public ResponseEntity<GenericResponse<EmailDtos.EmailMensajeDetalle>> getMensaje(
            @PathVariable String idMensaje) {
        try {
            EmailDtos.EmailMensajeDetalle detalleMensaje = gmailService.getDetalleMensaje(idMensaje);
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Detalles del mensaje obtenidos exitosamente.", detalleMensaje)
            );
        } catch (IOException e) {
            log.error("Error al obtener detalles del mensaje ID {}: {}", idMensaje, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Fallo al obtener los detalles del mensaje: " + e.getMessage(), null));
        }
    }

    @GetMapping("/mensajes/{idMensaje}/adjuntos/{idAdjunto}")
    public ResponseEntity<?> descargarAdjunto(
            @PathVariable String idMensaje,
            @PathVariable String idAdjunto) {
        try {
            byte[] data = gmailService.getAdjunto(idMensaje, idAdjunto);
            // Podrías obtener el nombre del archivo y mime-type para una respuesta más completa
            // pero para un ejemplo simple, devolvemos los bytes directamente.
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"descarga\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(data);
        } catch (IOException e) {
            log.error("Error al descargar adjunto {} del mensaje {}: {}", idAdjunto, idMensaje, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "No se pudo descargar el archivo adjunto.", null));
        }
    }
}