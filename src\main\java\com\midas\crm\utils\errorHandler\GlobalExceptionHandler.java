package com.midas.crm.utils.errorHandler;

import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.MidasErrorMessage;
import com.midas.crm.utils.ResponseBuilder;
import org.hibernate.LazyInitializationException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MidasExceptions.class)
    public ResponseEntity<GenericResponse<Object>> handleMidasException(MidasExceptions ex) {
        return ResponseBuilder.error(ex.getDisplayMessage());
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<GenericResponse<Object>> handleDataIntegrityViolation(DataIntegrityViolationException ex) {
        String errorMessage = ex.getMessage();
        if (errorMessage != null) {
            if (errorMessage.contains("dni") && errorMessage.contains("length")) {
                return ResponseBuilder.error("El DNI debe tener exactamente 8 caracteres");
            } else if (errorMessage.contains("unique constraint")) {
                if (errorMessage.contains("dni")) {
                    return ResponseBuilder.error("Ya existe un usuario con este DNI");
                } else if (errorMessage.contains("username")) {
                    return ResponseBuilder.error("Ya existe un usuario con este nombre de usuario");
                } else if (errorMessage.contains("email")) {
                    return ResponseBuilder.error("Ya existe un usuario con este email");
                }
                return ResponseBuilder.error("Ya existe un registro con los mismos datos");
            }
        }
        return ResponseBuilder.error(MidasErrorMessage.ERROR_INTERNAL);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<GenericResponse<Map<String, String>>> handleValidationExceptions(
            MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        return ResponseBuilder.error("Errores de validación", errors);
    }

    @ExceptionHandler(LazyInitializationException.class)
    public ResponseEntity<GenericResponse<String>> handleLazyInitializationException(LazyInitializationException ex) {
        return ResponseBuilder.error("Error de inicialización perezosa: " + ex.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<GenericResponse<Object>> handleGeneralException(Exception ex) {
        return ResponseBuilder.error(MidasErrorMessage.ERROR_INTERNAL);
    }

    @ExceptionHandler(UsernameNotFoundException.class)
    public ResponseEntity<GenericResponse<Object>> handleUsernameNotFoundException(UsernameNotFoundException ex) {
        return ResponseBuilder.error(MidasErrorMessage.USUARIO_NOT_FOUND);
    }

    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<GenericResponse<Object>> handleBadCredentialsException(BadCredentialsException ex) {
        return ResponseBuilder.error(MidasErrorMessage.USUARIO_INVALID_LOGIN);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<GenericResponse<Object>> handleAccessDeniedException(AccessDeniedException ex) {
        return ResponseBuilder.error(MidasErrorMessage.OPERACION_NO_PERMITIDA_LECTOR);
    }

    @ExceptionHandler(IOException.class)
    public ResponseEntity<GenericResponse<Object>> handleIOException(IOException ex) {
        return ResponseBuilder.error("Error de entrada/salida: " + ex.getMessage());
    }
}