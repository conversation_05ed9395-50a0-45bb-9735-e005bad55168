package com.midas.crm.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

@Configuration
@EnableAsync
public class ThreadConfig {

	@Value("${threadpool.corepoolsize}")
	private int corePoolSize;

	@Value("${threadpool.maxpoolsize}")
	private int maxPoolSize;

	@Value("${threadpool.queuecapacity}")
	private int queueCapacity;

	@Bean(name = "virtualTaskExecutor")
	public Executor virtualExecutor() {
		return Executors.newVirtualThreadPerTaskExecutor();
	}

	@Bean(name = "fixedTaskExecutor")
	public Executor fixedThreadExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(corePoolSize);
		executor.setMaxPoolSize(maxPoolSize);
		executor.setQueueCapacity(queueCapacity);
		executor.setThreadNamePrefix("FixedExecutor-");
		executor.setKeepAliveSeconds(60);
		executor.setAllowCoreThreadTimeOut(true);
		executor.initialize();
		return executor;
	}
}
