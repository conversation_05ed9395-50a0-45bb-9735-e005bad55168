package com.midas.crm.entity.DTO.cliente;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO para transportar información combinada de Clientes (Leads) y sus Usuarios (Asesores/Coordinadores).
 * Este DTO es flexible y se utiliza en varias consultas de repositorio.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor // Genera un constructor con todos los argumentos.
public class ClienteConUsuarioDTO {

    private String dni;
    private String asesor;
    private LocalDateTime fechaIngresado;
    private String numeroMovil;
    private String coordinador;
    private Long leadId;
    private String urlTranscripcion;

    /**
     * Constructor para mantener compatibilidad con consultas más antiguas que no devuelven al coordinador.
     * @param dni DNI del asesor.
     * @param asesor Nombre completo del asesor.
     * @param fechaIngresado Fecha de registro del lead.
     * @param numeroMovil Móvil de contacto del cliente.
     */
    public ClienteConUsuarioDTO(String dni, String asesor, LocalDateTime fechaIngresado, String numeroMovil) {
        this.dni = dni;
        this.asesor = asesor;
        this.fechaIngresado = fechaIngresado;
        this.numeroMovil = numeroMovil;
    }

    /**
     * Constructor para mantener compatibilidad con consultas que devuelven al coordinador pero no los nuevos campos.
     * @param dni DNI del asesor.
     * @param asesor Nombre completo del asesor.
     * @param fechaIngresado Fecha de registro del lead.
     * @param numeroMovil Móvil de contacto del cliente.
     * @param coordinador Nombre del coordinador.
     */
    public ClienteConUsuarioDTO(String dni, String asesor, LocalDateTime fechaIngresado, String numeroMovil, String coordinador) {
        this.dni = dni;
        this.asesor = asesor;
        this.fechaIngresado = fechaIngresado;
        this.numeroMovil = numeroMovil;
        this.coordinador = coordinador;
    }
}