package com.midas.crm.config;

import com.midas.crm.util.RequestDeduplicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Configuración para utilidades de la aplicación
 */
@Configuration
public class UtilConfig {

    /**
     * Crea un bean RequestDeduplicator para evitar solicitudes duplicadas
     * @return RequestDeduplicator configurado
     */
    @Bean
    public RequestDeduplicator requestDeduplicator() {
        // Configurar con un tiempo de expiración de 2 segundos
        return new RequestDeduplicator(2, TimeUnit.SECONDS);
    }
}
