package com.midas.crm.repository;

import com.midas.crm.entity.FaqRespuesta;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FaqRespuestaRepository extends JpaRepository<FaqRespuesta, Long> {
    // Buscar respuestas por ID de FAQ
    List<FaqRespuesta> findByFaqId(Long faqId);

    // Buscar respuestas por ID de usuario
    List<FaqRespuesta> findByUsuarioId(Long usuarioId);

    // Buscar respuestas por ID de FAQ y ID de usuario
    List<FaqRespuesta> findByFaqIdAndUsuarioId(Long faqId, Long usuarioId);
}
