package com.midas.crm.entity.DTO.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AuthResponse {
    private Long userId;
    private String username;
    private String nombre;
    private String apellido;
    private String email;
    private String token;
    private String role;
    @JsonProperty("sede_id")
    private Long sedeId;
    private String sede;
    private String picture; // ✅ AGREGAR CAMPO PICTURE
    private CoordinadorLoginDTO coordinador;
}
