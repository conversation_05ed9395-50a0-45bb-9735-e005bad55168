package com.midas.crm.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@Configuration
public class CorsConfig {


	@Bean
	public CorsFilter corsFilter() {
	    final UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
	    final CorsConfiguration config = new CorsConfiguration();
	    config.setAllowCredentials(true);
	    // Agregar orígenes permitidos
	    config.addAllowedOrigin("http://localhost:5173");
		config.addAllowedOrigin("http://localhost:4200");
		config.addAllowedOrigin("http://localhost:4321");
		config.addAllowedOrigin("http://localhost:9039");
	    config.addAllowedOrigin("https://www.crm.midassolutiongroup.com");
	    config.addAllowedOrigin("https://crm.midassolutiongroup.com");
		config.addAllowedOrigin("https://apisozarusac.com/BackendJava");
		config.addAllowedOrigin("https://apisozarusac.com");
		config.addAllowedOrigin("https://www.api.midassolutiongroup.com");
		config.addAllowedOrigin("https://consultanumero.midassolutiongroup.com");

	    // Permitir todos los encabezados
	    config.addAllowedHeader("*");
	    // Permitir todos los métodos HTTP necesarios
	    config.addAllowedMethod("GET");
	    config.addAllowedMethod("PUT");
	    config.addAllowedMethod("POST");
	    config.addAllowedMethod("DELETE");
		config.addAllowedMethod("PATCH");
	    // Aplicar la configuración a todas las rutas
	    source.registerCorsConfiguration("/**", config);
	    return new CorsFilter(source);
	}

}