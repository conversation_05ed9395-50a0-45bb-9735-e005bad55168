package com.midas.crm.mapper;

import com.midas.crm.entity.Cuestionario;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioDTO;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioUpdateDTO;
import com.midas.crm.entity.DTO.cuestionario.PreguntaDTO;
import com.midas.crm.entity.Leccion;

import java.util.List;
import java.util.stream.Collectors;

public final class CuestionarioMapper {

    private CuestionarioMapper() {}

    public static Cuestionario toEntity(CuestionarioCreateDTO dto, Leccion leccion) {
        Cuestionario cuestionario = new Cuestionario();
        cuestionario.setTitulo(dto.getTitulo());
        cuestionario.setDescripcion(dto.getDescripcion());
        cuestionario.setTiempoLimite(dto.getTiempoLimite());
        cuestionario.setPuntajeAprobacion(dto.getPuntajeAprobacion());
        cuestionario.setIntentosMaximos(dto.getIntentosMaximos());
        cuestionario.setMostrarRespuestas(dto.getMostrarRespuestas());
        cuestionario.setAleatorizarPreguntas(dto.getAleatorizarPreguntas());
        cuestionario.setLeccion(leccion);
        cuestionario.setEstado("A");
        return cuestionario;
    }

    public static CuestionarioDTO toDTO(Cuestionario cuestionario) {
        if (cuestionario == null) return null;

        List<PreguntaDTO> preguntasDTO = null;
        if (cuestionario.getPreguntas() != null && !cuestionario.getPreguntas().isEmpty()) {
            preguntasDTO = cuestionario.getPreguntas().stream()
                    .map(PreguntaMapper::toDTO)
                    .collect(Collectors.toList());
        }

        return new CuestionarioDTO(
                cuestionario.getId(),
                cuestionario.getTitulo(),
                cuestionario.getDescripcion(),
                cuestionario.getTiempoLimite(),
                cuestionario.getPuntajeAprobacion(),
                cuestionario.getIntentosMaximos(),
                cuestionario.getMostrarRespuestas(),
                cuestionario.getAleatorizarPreguntas(),
                cuestionario.getLeccion() != null ? cuestionario.getLeccion().getId() : null,
                cuestionario.getLeccion() != null ? cuestionario.getLeccion().getTitulo() : null,
                cuestionario.getEstado(),
                cuestionario.getFechaCreacion(),
                cuestionario.getFechaActualizacion(),
                preguntasDTO
        );
    }

    public static void updateEntity(Cuestionario cuestionario, CuestionarioUpdateDTO dto) {
        if (dto.getTitulo() != null) cuestionario.setTitulo(dto.getTitulo());
        if (dto.getDescripcion() != null) cuestionario.setDescripcion(dto.getDescripcion());
        if (dto.getTiempoLimite() != null) cuestionario.setTiempoLimite(dto.getTiempoLimite());
        if (dto.getPuntajeAprobacion() != null) cuestionario.setPuntajeAprobacion(dto.getPuntajeAprobacion());
        if (dto.getIntentosMaximos() != null) cuestionario.setIntentosMaximos(dto.getIntentosMaximos());
        if (dto.getMostrarRespuestas() != null) cuestionario.setMostrarRespuestas(dto.getMostrarRespuestas());
        if (dto.getAleatorizarPreguntas() != null) cuestionario.setAleatorizarPreguntas(dto.getAleatorizarPreguntas());
        if (dto.getEstado() != null) cuestionario.setEstado(dto.getEstado());
    }
}
