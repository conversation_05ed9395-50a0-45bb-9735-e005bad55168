package com.midas.crm.entity;


import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

@Entity
@Data
@Table(name = "calendarios")
public class Calendar {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String titulo;

    @Column(columnDefinition = "TEXT")
    private String descripcion;

    private String color;

    @Column(name = "fecha_inicio")
    private LocalDate fechaInicio;

    @Column(name = "hora_inicio")
    private LocalTime horaInicio;

    @Column(name = "fecha_final")
    private LocalDate fechaFinal;

    @Column(name = "hora_final")
    private LocalTime horaFinal;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "is_seen")
    private Boolean isSeen = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_create_id")
    private User userCreate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_update_id")
    private User userUpdate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_delete_id")
    private User userDelete;

    @Column(name = "deleted")
    private Boolean deleted = false;
}

