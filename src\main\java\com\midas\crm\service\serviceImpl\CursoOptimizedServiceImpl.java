package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Curso;
import com.midas.crm.entity.Modulo;
import com.midas.crm.entity.DTO.curso.CursoListDTO;
import com.midas.crm.entity.DTO.curso.ModuloMinimalDTO;
import com.midas.crm.repository.CursoRepository;
import com.midas.crm.repository.ModuloRepository;
import com.midas.crm.service.CursoOptimizedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementación del servicio optimizado para cursos
 */
@Service
public class CursoOptimizedServiceImpl implements CursoOptimizedService {

    @Autowired
    private CursoRepository cursoRepository;

    @Autowired
    private ModuloRepository moduloRepository;

    @Override
    public List<CursoListDTO> getCursosOptimizedByIds(List<Long> ids) {
        // Usar la consulta optimizada que incluye conteos de módulos, secciones y lecciones
        return cursoRepository.findOptimizedByIds(ids);
    }

    @Override
    public List<ModuloMinimalDTO> getModulosOptimizedByCursoId(Long cursoId) {
        List<Modulo> modulos = moduloRepository.findByCursoIdOrderByOrdenAsc(cursoId);
        return modulos.stream()
                .map(this::toModuloMinimalDTO)
                .collect(Collectors.toList());
    }

    @Override
    public CursoListDTO getCursoOptimizedById(Long cursoId) {
        Curso curso = cursoRepository.findById(cursoId)
                .orElseThrow(() -> new RuntimeException("Curso no encontrado"));
        return toCursoListDTO(curso);
    }

    /**
     * Convierte un Curso a CursoListDTO con solo los campos necesarios
     */
    private CursoListDTO toCursoListDTO(Curso curso) {
        CursoListDTO dto = new CursoListDTO();
        dto.setId(curso.getId());
        dto.setNombre(curso.getNombre());
        dto.setDescripcion(curso.getDescripcion());
        dto.setFechaInicio(curso.getFechaInicio());
        dto.setFechaFin(curso.getFechaFin());
        dto.setEstado(curso.getEstado());
        dto.setVideoUrl(curso.getVideoUrl());

        // Solo incluir información básica del usuario
        if (curso.getUsuario() != null) {
            dto.setUsuarioNombre(curso.getUsuario().getNombre());
            dto.setUsuarioApellido(curso.getUsuario().getApellido());
        }

        return dto;
    }

    /**
     * Convierte un Modulo a ModuloMinimalDTO con solo los campos necesarios
     */
    private ModuloMinimalDTO toModuloMinimalDTO(Modulo modulo) {
        ModuloMinimalDTO dto = new ModuloMinimalDTO();
        dto.setId(modulo.getId());
        dto.setNombre(modulo.getTitulo()); // El frontend espera 'nombre' pero el backend tiene 'titulo'
        dto.setDescripcion(modulo.getDescripcion());
        dto.setOrden(modulo.getOrden());
        dto.setEstado(modulo.getEstado());

        // Campos de progreso - por ahora en false/0, se pueden calcular después si es necesario
        dto.setCompletado(false);
        dto.setPorcentajeCompletado(0);
        dto.setTotalLecciones(0);
        dto.setLeccionesCompletadas(0);

        return dto;
    }
}
