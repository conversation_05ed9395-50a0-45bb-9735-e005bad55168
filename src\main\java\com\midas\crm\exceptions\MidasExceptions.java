package com.midas.crm.exceptions;

import com.midas.crm.utils.MidasErrorMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MidasExceptions extends RuntimeException {
    private final MidasErrorMessage errorMessage;
    private final Integer errorCode;
    private final String customMessage;

    public MidasExceptions(final MidasErrorMessage errorMessage) {
        super(errorMessage.getErrorMessage());
        this.errorMessage = errorMessage;
        this.errorCode = errorMessage.getErrorCode();
        this.customMessage = null;
    }

    public MidasExceptions(final MidasErrorMessage errorMessage, String customMessage) {
        super(customMessage != null ? customMessage : errorMessage.getErrorMessage());
        this.errorMessage = errorMessage;
        this.errorCode = errorMessage.getErrorCode();
        this.customMessage = customMessage;
    }

    public String getDisplayMessage() {
        return customMessage != null ? customMessage : errorMessage.getErrorMessage();
    }
}
