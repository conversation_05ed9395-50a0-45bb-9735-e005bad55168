package com.midas.crm.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Configuración de Spring MVC para interceptors y otras configuraciones web
 */
@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

    private final RequestTracingInterceptor requestTracingInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // Agregar interceptor de tracing para todas las rutas excepto recursos estáticos
        registry.addInterceptor(requestTracingInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/static/**",
                    "/css/**",
                    "/js/**",
                    "/images/**",
                    "/favicon.ico",
                    "/actuator/health",
                    "/actuator/prometheus"
                );
    }
}
