package com.midas.crm.repository;

import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.RespuestaEncuestaUsuario;
import com.midas.crm.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RespuestaEncuestaUsuarioRepository extends JpaRepository<RespuestaEncuestaUsuario, Long> {

    // Buscar respuestas por usuario y encuesta
    List<RespuestaEncuestaUsuario> findByUsuarioAndEncuesta(User usuario, Encuesta encuesta);

    List<RespuestaEncuestaUsuario> findByUsuarioIdAndEncuestaId(Long usuarioId, Long encuestaId);

    // Buscar respuestas completadas por usuario y encuesta
    List<RespuestaEncuestaUsuario> findByUsuarioAndEncuestaAndCompletadaTrue(User usuario, Encuesta encuesta);

    List<RespuestaEncuestaUsuario> findByUsuarioIdAndEncuestaIdAndCompletadaTrue(Long usuarioId, Long encuestaId);

    // Buscar respuestas por encuesta
    List<RespuestaEncuestaUsuario> findByEncuesta(Encuesta encuesta);

    List<RespuestaEncuestaUsuario> findByEncuestaId(Long encuestaId);

    // Buscar respuestas completadas por encuesta
    List<RespuestaEncuestaUsuario> findByEncuestaAndCompletadaTrue(Encuesta encuesta);

    List<RespuestaEncuestaUsuario> findByEncuestaIdAndCompletadaTrue(Long encuestaId);

    // Verificar si un usuario ha completado una encuesta
    boolean existsByUsuarioIdAndEncuestaIdAndCompletadaTrue(Long usuarioId, Long encuestaId);

    // Contar todas las respuestas por encuesta
    @Query("SELECT COUNT(r) FROM RespuestaEncuestaUsuario r WHERE r.encuesta.id = :encuestaId")
    Long countByEncuestaId(@Param("encuestaId") Long encuestaId);

    // Contar respuestas completadas por encuesta
    @Query("SELECT COUNT(r) FROM RespuestaEncuestaUsuario r WHERE r.encuesta.id = :encuestaId AND r.completada = true")
    Long countCompletadasByEncuestaId(@Param("encuestaId") Long encuestaId);

    // Buscar la última respuesta no completada de un usuario para una encuesta
    Optional<RespuestaEncuestaUsuario> findFirstByUsuarioIdAndEncuestaIdAndCompletadaFalseOrderByFechaCreacionDesc(
            Long usuarioId, Long encuestaId);
}
