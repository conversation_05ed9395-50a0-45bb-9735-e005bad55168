package com.midas.crm.entity.DTO.encuesta;

import com.midas.crm.entity.DTO.user.UserDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO para transferir información de respuestas de usuario a encuestas
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RespuestaEncuestaUsuarioDTO {
    private Long id;
    private UserDTO usuario; // Puede ser null si la encuesta es anónima
    private Long usuarioId;
    private String usuarioNombre;
    private Long encuestaId;
    private String encuestaTitulo;
    private LocalDateTime fechaInicio;
    private LocalDateTime fechaFin;
    private Boolean completada;
    private List<DetalleRespuestaEncuestaUsuarioDTO> detallesRespuestas;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
}
