package com.midas.crm.entity.DTO.asistencia;

import com.midas.crm.entity.Asistencia;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AsistenciaCreateDTO {
    
    @NotNull(message = "El ID del usuario es obligatorio")
    private Long usuarioId;
    
    private LocalDateTime fechaHoraEntrada;
    private LocalDateTime fechaHoraSalida;
    private String ipEntrada;
    private String ipSalida;
    private String dispositivoEntrada;
    private String dispositivoSalida;
    private String ubicacionEntrada;
    private String ubicacionSalida;
    private Asistencia.TipoActividad tipoActividad = Asistencia.TipoActividad.ENTRADA;
    private Asistencia.SubtipoActividad subtipoActividad = Asistencia.SubtipoActividad.AUTOMATICA;
    private Integer duracionMinutos;
    private Integer tiempoSesionMinutos;
    private String observaciones;
}
