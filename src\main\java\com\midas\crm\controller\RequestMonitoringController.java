package com.midas.crm.controller;

import com.midas.crm.entity.DTO.monitoring.MonitoringDTOs.*;
import com.midas.crm.service.RequestMonitoringService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Controlador para monitorear peticiones HTTP.
 * Lógica de negocio delegada a RequestMonitoringService.
 * Solo accesible para roles de ADMIN o PROGRAMADOR.
 */
@RestController
@RequestMapping("/api/monitoring/requests")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN') or hasRole('PROGRAMADOR')")
public class RequestMonitoringController {

    private final RequestMonitoringService monitoringService;

    @GetMapping("/active")
    public ResponseEntity<GenericResponse<ActiveRequestsDTO>> getActiveRequests() {
        ActiveRequestsDTO response = monitoringService.getActiveRequests();
        return ResponseEntity.ok(new GenericResponse<>(
                GenericResponseConstants.SUCCESS,
                "Peticiones activas obtenidas correctamente.",
                response
        ));
    }

    @GetMapping("/long-running")
    public ResponseEntity<GenericResponse<LongRunningRequestsDTO>> getLongRunningRequests() {
        LongRunningRequestsDTO response = monitoringService.getLongRunningRequests();
        return ResponseEntity.ok(new GenericResponse<>(
                GenericResponseConstants.SUCCESS,
                "Peticiones de larga duración obtenidas correctamente.",
                response
        ));
    }

    @GetMapping("/stats")
    public ResponseEntity<GenericResponse<RequestStatsDTO>> getRequestStats() {
        RequestStatsDTO response = monitoringService.getRequestStats();
        return ResponseEntity.ok(new GenericResponse<>(
                GenericResponseConstants.SUCCESS,
                "Estadísticas de peticiones obtenidas correctamente.",
                response
        ));
    }

    @PostMapping("/log-long-requests")
    public ResponseEntity<GenericResponse<String>> forceLogLongRequests() {
        String responseMessage = monitoringService.logLongRequestsAndGetResponse();
        return ResponseEntity.ok(new GenericResponse<>(
                GenericResponseConstants.SUCCESS,
                "Operación de registro completada.",
                responseMessage
        ));
    }
}