package com.midas.crm.service;

import com.midas.crm.entity.DTO.transcription.TAListadoDTO;
import com.midas.crm.entity.TranscriptionAnalysis;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Servicio para manejar análisis de transcripción
 */
public interface TranscriptionAnalysisService {

    /**
     * Guarda un nuevo análisis de transcripción
     */
    TranscriptionAnalysis saveAnalysis(Long clienteResidencialId, Map<String, Object> comparisonResult);

    /**
     * Busca análisis por ID del cliente residencial
     */
    Optional<TranscriptionAnalysis> findByClienteResidencialId(Long clienteResidencialId);

    /**
     * Busca todos los análisis de un cliente residencial
     */
    List<TranscriptionAnalysis> findAllByClienteResidencialId(Long clienteResidencialId);

    /**
     * Busca análisis por ID
     */
    Optional<TranscriptionAnalysis> findById(Long id);

    /**
     * Busca análisis por estado
     */
    List<TranscriptionAnalysis> findByEstado(String estado);

    /**
     * Busca análisis por rango de fechas
     */

    /* MÉTODOS EXISTENTES — NO toques sus firmas */
    Page<TranscriptionAnalysis> findByFechaCreacionBetween(
            LocalDateTime ini, LocalDateTime fin, Pageable pageable);

    Page<TAListadoDTO> findListadoDTO(LocalDateTime ini, LocalDateTime fin, Pageable pageable);

    /* NUEVO — sólo para el listado principal */
    Page<TAListadoDTO> listado(Pageable pageable, LocalDateTime ini, LocalDateTime fin);
    /**
     * Busca análisis por nivel de confianza
     */
    List<TranscriptionAnalysis> findByNivelConfianza(String nivelConfianza);

    /**
     * Busca análisis con porcentaje promedio mayor o igual al especificado
     */
    List<TranscriptionAnalysis> findByPorcentajePromedioGreaterThanEqual(Double porcentajeMinimo);

    /**
     * Busca análisis con porcentaje promedio menor al especificado
     */
    List<TranscriptionAnalysis> findByPorcentajePromedioLessThan(Double porcentajeMaximo);

    /**
     * Busca análisis por número de agente
     */
    List<TranscriptionAnalysis> findByNumeroAgente(String numeroAgente);

    /**
     * Busca análisis por móvil de contacto
     */
    List<TranscriptionAnalysis> findByMovilContacto(String movilContacto);

    /**
     * Obtiene estadísticas de análisis por rango de fechas
     */
    Map<String, Object> getEstadisticasByFechaCreacionBetween(LocalDateTime fechaInicio, LocalDateTime fechaFin);

    /**
     * Verifica si existe un análisis para un cliente residencial específico
     */
    boolean existsByClienteResidencialId(Long clienteResidencialId);

    /**
     * Actualiza un análisis existente
     */
    TranscriptionAnalysis updateAnalysis(Long id, Map<String, Object> comparisonResult);

    /**
     * Elimina un análisis por ID
     */
    void deleteById(Long id);

    /**
     * Elimina análisis antiguos (más de X días)
     */
    void deleteOldAnalysis(int diasAntiguedad);

    /**
     * Busca los últimos análisis
     */
    List<TranscriptionAnalysis> findLatestAnalysis();

    /**
     * Busca análisis con problemas
     */
    List<TranscriptionAnalysis> findProblematicAnalysis();

    /**
     * Cuenta análisis por estado
     */
    long countByEstado(String estado);

    /**
     * Cuenta análisis por nivel de confianza
     */
    long countByNivelConfianza(String nivelConfianza);

    /**
     * Extrae datos específicos del response del comparador
     */
    Map<String, Object> extractAnalysisData(Map<String, Object> comparisonResult);

    /**
     * Convierte el response completo a JSON string
     */
    String convertToJsonString(Map<String, Object> data);

    /**
     * Convierte JSON string a Map
     */
    Map<String, Object> convertFromJsonString(String jsonString);

    /**
     * Busca análisis con filtros y paginación
     */
    Page<TranscriptionAnalysis> findAllWithFilters(Pageable pageable, String estado, String nivelConfianza,
                                                   String numeroAgente, String movilContacto,
                                                   Double porcentajeMinimo, Double porcentajeMaximo,
                                                   LocalDate fechaInicio, LocalDate fechaFin);

    /**
     * Obtiene estadísticas generales
     */
    Map<String, Object> getEstadisticasGenerales();

    /**
     * Obtiene resumen general
     */
    Map<String, Object> getResumenGeneral();
}
