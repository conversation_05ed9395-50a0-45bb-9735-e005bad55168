package com.midas.crm.service;

import com.midas.crm.entity.Sede;
import com.midas.crm.repository.SedeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;

/**
 * Servicio para gestionar tópicos WebSocket específicos por sede
 * Centraliza la lógica de envío de mensajes a tópicos específicos
 */
@Service
@Slf4j
public class WebSocketTopicService {

    private final SimpMessagingTemplate messagingTemplate;
    private final SedeRepository sedeRepository;

    @Autowired
    public WebSocketTopicService(SimpMessagingTemplate messagingTemplate, SedeRepository sedeRepository) {
        this.messagingTemplate = messagingTemplate;
        this.sedeRepository = sedeRepository;
    }

    /**
     * Envía un mensaje al tópico general (sin filtrar por sede)
     * @param topic Nombre del tópico (sin prefijos)
     * @param payload Contenido del mensaje
     */
    public void sendToGeneralTopic(String topic, Object payload) {
        String destination = "/topic/" + topic;
        log.info("Enviando mensaje al tópico general: {}", destination);
        messagingTemplate.convertAndSend(destination, payload);
    }

    /**
     * Envía un mensaje al tópico específico de una sede
     * @param sedeId ID de la sede
     * @param topic Nombre del tópico (sin prefijos)
     * @param payload Contenido del mensaje
     */
    public void sendToSedeTopic(Long sedeId, String topic, Object payload) {
        if (sedeId == null) {
            // Si no hay sede, enviar al tópico general
            sendToGeneralTopic(topic, payload);
            return;
        }

        String destination = "/topic/" + topic + "/sede/" + sedeId;
        log.info("Enviando mensaje al tópico de sede {}: {}", sedeId, destination);
        messagingTemplate.convertAndSend(destination, payload);
    }

    /**
     * Envía mensajes a todas las sedes y al tópico general
     * @param topic Nombre del tópico (sin prefijos)
     * @param payloadProvider Función que genera el payload para cada sede
     */
    public void sendToAllSedes(String topic, Function<Long, Object> payloadProvider) {
        try {
            // Obtener todas las sedes activas
            List<Sede> sedes = sedeRepository.findAll();
            log.info("Enviando mensajes a {} sedes para el tópico: {}", sedes.size(), topic);

            // Enviar al tópico general (sin filtrar por sede)
            Object generalPayload = payloadProvider.apply(null);
            sendToGeneralTopic(topic, generalPayload);

            // Enviar a cada sede su propio mensaje
            for (Sede sede : sedes) {
                Long sedeId = sede.getId();
                if (sedeId != null) {
                    Object sedePayload = payloadProvider.apply(sedeId);
                    sendToSedeTopic(sedeId, topic, sedePayload);
                }
            }
        } catch (Exception e) {
            log.error("Error al enviar mensajes a todas las sedes para el tópico: " + topic, e);
        }
    }

    /**
     * Envía el mismo mensaje a todas las sedes y al tópico general
     * @param topic Nombre del tópico (sin prefijos)
     * @param payload Contenido del mensaje
     */
    public void broadcastToAllSedes(String topic, Object payload) {
        try {
            // Obtener todas las sedes activas
            List<Sede> sedes = sedeRepository.findAll();
            log.info("Enviando broadcast a {} sedes para el tópico: {}", sedes.size(), topic);

            // Enviar al tópico general (sin filtrar por sede)
            sendToGeneralTopic(topic, payload);

            // Enviar a cada sede el mismo mensaje
            for (Sede sede : sedes) {
                Long sedeId = sede.getId();
                if (sedeId != null) {
                    sendToSedeTopic(sedeId, topic, payload);
                }
            }
        } catch (Exception e) {
            log.error("Error al enviar broadcast a todas las sedes para el tópico: " + topic, e);
        }
    }
}
