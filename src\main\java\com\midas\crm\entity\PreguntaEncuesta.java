package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entidad que representa una pregunta de una encuesta
 */
@Entity
@Table(name = "preguntas_encuesta")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreguntaEncuesta {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String enunciado;

    @Column(length = 500)
    private String descripcion;

    @Column(nullable = false)
    private Integer orden; // Orden de la pregunta en la encuesta

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TipoPregunta tipo = TipoPregunta.OPCION_MULTIPLE; // Tipo de pregunta

    @Column(name = "es_obligatoria")
    private Boolean esObligatoria = true; // Indica si la pregunta es obligatoria

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encuesta_id", nullable = false)
    private Encuesta encuesta;

    @OneToMany(mappedBy = "pregunta", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<OpcionRespuestaEncuesta> opciones = new ArrayList<>();

    @Column(nullable = false, length = 1)
    private String estado = "A"; // A: Activo, I: Inactivo

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;

    /**
     * Enum que define los tipos de preguntas disponibles
     */
    public enum TipoPregunta {
        OPCION_MULTIPLE,    // Una sola opción seleccionable
        SELECCION_MULTIPLE, // Múltiples opciones seleccionables
        ESCALA_LIKERT,      // Escala de valoración (1-5, 1-7, etc.)
        TEXTO_LIBRE,        // Respuesta de texto libre
        FECHA,              // Respuesta de tipo fecha
        NUMERO              // Respuesta numérica
    }
}
