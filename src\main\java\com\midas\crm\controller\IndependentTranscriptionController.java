package com.midas.crm.controller;

import com.midas.crm.entity.DTO.IndependentTranscriptionDTO;
import com.midas.crm.entity.IndependentTranscription;
import com.midas.crm.entity.IndependentTranscription.TranscriptionStatus;
import com.midas.crm.service.IndependentTranscriptionService;

import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Controlador REST para transcripciones independientes
 */
@RestController
@RequestMapping("${api.route.independent-transcriptions}")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class IndependentTranscriptionController {

    private final IndependentTranscriptionService transcriptionService;

    /**
     * Crea una nueva transcripción independiente con datos ya procesados del frontend
     */
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<GenericResponse<IndependentTranscriptionDTO>> createTranscription(
            @RequestParam("audioFile") MultipartFile audioFile,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "whisperModel", defaultValue = "base") String whisperModel,
            @RequestParam(value = "targetLanguage", required = false) String targetLanguage,
            @RequestParam(value = "tags", required = false) String tags,
            @RequestParam(value = "notes", required = false) String notes,
            @RequestParam(value = "transcriptionText", required = false) String transcriptionText,
            @RequestParam(value = "confidence", required = false) String confidence,
            @RequestParam(value = "language", required = false) String language,
            @RequestParam(value = "processingTime", required = false) String processingTime,
            @RequestParam(value = "duration", required = false) String duration,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "driveUrl", required = false) String driveUrl,
            @RequestParam(value = "callId", required = false) String callId,
            HttpServletRequest request) {

        try {
            // Obtener información del usuario (si está disponible)
            String createdBy = getCurrentUser(request);

            // Convertir etiquetas
            List<String> tagsList = null;
            if (tags != null && !tags.trim().isEmpty()) {
                tagsList = Arrays.asList(tags.split(","));
                tagsList = tagsList.stream().map(String::trim).toList();
            }

            // Si hay datos de transcripción, crear con datos completos
            if (transcriptionText != null && !transcriptionText.trim().isEmpty()) {
                // Crear transcripción con datos ya procesados
                IndependentTranscription transcription = transcriptionService.createTranscriptionWithData(
                        audioFile, fileName, whisperModel, targetLanguage, tagsList, notes,
                        transcriptionText,
                        confidence != null ? Double.parseDouble(confidence) : null,
                        language,
                        processingTime != null ? Long.parseLong(processingTime) : null,
                        duration != null ? Integer.parseInt(duration) : null,
                        status,
                        driveUrl,
                        callId,
                        createdBy);

                IndependentTranscriptionDTO dto = transcriptionService.convertToDTO(transcription);
                return ResponseEntity.status(HttpStatus.CREATED)
                        .body(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                                "Transcripción creada exitosamente", dto));
            } else {
                // Crear transcripción sin datos (modo tradicional)
                IndependentTranscription transcription = transcriptionService.createTranscription(
                        audioFile, fileName, whisperModel, targetLanguage, tagsList, notes, createdBy);

                // Iniciar procesamiento
                transcriptionService.processTranscription(transcription.getId());

                IndependentTranscriptionDTO dto = transcriptionService.convertToDTO(transcription);
                return ResponseEntity.status(HttpStatus.CREATED)
                        .body(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                                "Transcripción creada exitosamente", dto));
            }

        } catch (IllegalArgumentException e) {
            log.warn("Error de validación al crear transcripción: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null));
        } catch (Exception e) {
            log.error("Error inesperado al crear transcripción: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Obtiene todas las transcripciones con paginación y filtros
     */
    @GetMapping
    public ResponseEntity<GenericResponse<Page<IndependentTranscriptionDTO>>> getTranscriptions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) List<String> status,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String language,
            @RequestParam(required = false) String createdBy,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateFrom,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateTo,
            @RequestParam(required = false) Integer minDuration,
            @RequestParam(required = false) Integer maxDuration,
            @RequestParam(required = false) Double minConfidence,
            @RequestParam(required = false) Double maxConfidence) {

        try {
            // Crear paginación
            Sort sort = Sort.by(sortDir.equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC, sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);

            // Convertir estados
            List<TranscriptionStatus> statusList = null;
            if (status != null && !status.isEmpty()) {
                statusList = status.stream()
                        .map(TranscriptionStatus::valueOf)
                        .toList();
            }

            // Buscar transcripciones
            Page<IndependentTranscriptionDTO> transcriptions = transcriptionService.searchTranscriptions(
                    statusList, fileName, language, createdBy, dateFrom, dateTo,
                    minDuration, maxDuration, minConfidence, maxConfidence, pageable);

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Transcripciones obtenidas exitosamente", transcriptions));

        } catch (Exception e) {
            log.error("Error obteniendo transcripciones: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Obtiene una transcripción por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<IndependentTranscriptionDTO>> getTranscriptionById(@PathVariable Long id) {
        try {
            Optional<IndependentTranscription> transcription = transcriptionService.getTranscriptionById(id);

            if (transcription.isPresent()) {
                IndependentTranscriptionDTO dto = transcriptionService.convertToDTO(transcription.get());
                return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                        "Transcripción encontrada", dto));
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("Error obteniendo transcripción {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Actualiza una transcripción
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<IndependentTranscriptionDTO>> updateTranscription(
            @PathVariable Long id,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String tags,
            @RequestParam(required = false) String notes) {

        try {
            // Convertir etiquetas
            List<String> tagsList = null;
            if (tags != null && !tags.trim().isEmpty()) {
                tagsList = Arrays.asList(tags.split(","));
                tagsList = tagsList.stream().map(String::trim).toList();
            }

            IndependentTranscription transcription = transcriptionService.updateTranscription(
                    id, fileName, tagsList, notes);

            IndependentTranscriptionDTO dto = transcriptionService.convertToDTO(transcription);

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Transcripción actualizada exitosamente", dto));

        } catch (RuntimeException e) {
            log.warn("Error actualizando transcripción {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null));
        } catch (Exception e) {
            log.error("Error inesperado actualizando transcripción {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Elimina una transcripción
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<String>> deleteTranscription(@PathVariable Long id) {
        try {
            transcriptionService.deleteTranscription(id);
            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Transcripción eliminada exitosamente", "ID: " + id));

        } catch (RuntimeException e) {
            log.warn("Error eliminando transcripción {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null));
        } catch (Exception e) {
            log.error("Error inesperado eliminando transcripción {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Obtiene el estado de una transcripción
     */
    @GetMapping("/{id}/status")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getTranscriptionStatus(@PathVariable Long id) {
        try {
            TranscriptionStatus status = transcriptionService.getTranscriptionStatus(id);
            Map<String, Object> response = Map.of("status", status);

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Estado obtenido exitosamente", response));

        } catch (RuntimeException e) {
            log.warn("Error obteniendo estado de transcripción {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null));
        } catch (Exception e) {
            log.error("Error inesperado obteniendo estado de transcripción {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Reintenta una transcripción fallida
     */
    @PostMapping("/{id}/retry")
    public ResponseEntity<GenericResponse<IndependentTranscriptionDTO>> retryTranscription(@PathVariable Long id) {
        try {
            IndependentTranscription transcription = transcriptionService.retryTranscription(id);
            IndependentTranscriptionDTO dto = transcriptionService.convertToDTO(transcription);

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Transcripción reintentada exitosamente", dto));

        } catch (RuntimeException e) {
            log.warn("Error reintentando transcripción {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null));
        } catch (Exception e) {
            log.error("Error inesperado reintentando transcripción {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Cancela una transcripción en proceso
     */
    @PostMapping("/{id}/cancel")
    public ResponseEntity<GenericResponse<IndependentTranscriptionDTO>> cancelTranscription(@PathVariable Long id) {
        try {
            IndependentTranscription transcription = transcriptionService.cancelTranscription(id);
            IndependentTranscriptionDTO dto = transcriptionService.convertToDTO(transcription);

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Transcripción cancelada exitosamente", dto));

        } catch (RuntimeException e) {
            log.warn("Error cancelando transcripción {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null));
        } catch (Exception e) {
            log.error("Error inesperado cancelando transcripción {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Obtiene estadísticas generales
     */
    @GetMapping("/statistics")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getStatistics() {
        try {
            Map<String, Object> statistics = transcriptionService.getStatistics();
            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Estadísticas obtenidas exitosamente", statistics));

        } catch (Exception e) {
            // En caso de error, devolver estadísticas por defecto
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("total", 0L);
            defaultStats.put("totalDuration", 0L);
            defaultStats.put("averageConfidence", 0.0);
            defaultStats.put("totalFileSize", 0L);
            defaultStats.put("byStatus", new HashMap<String, Long>());
            defaultStats.put("byLanguage", new HashMap<String, Long>());

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Estadísticas por defecto (servicio en desarrollo)", defaultStats));
        }
    }

    /**
     * Busca transcripciones por contenido
     */
    @GetMapping("/search")
    public ResponseEntity<GenericResponse<Page<IndependentTranscriptionDTO>>> searchTranscriptions(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            Page<IndependentTranscriptionDTO> transcriptions = transcriptionService.searchByContent(query, pageable);

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Búsqueda completada exitosamente", transcriptions));

        } catch (Exception e) {
            log.error("Error en búsqueda de transcripciones: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Obtiene etiquetas populares
     */
    @GetMapping("/tags/popular")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> getPopularTags(
            @RequestParam(defaultValue = "20") int limit) {

        try {
            List<Map<String, Object>> tags = transcriptionService.getPopularTags(limit);
            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Etiquetas populares obtenidas exitosamente", tags));

        } catch (Exception e) {
            log.error("Error obteniendo etiquetas populares: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Actualiza el resultado de una transcripción
     */
    @PutMapping("/{id}/result")
    public ResponseEntity<GenericResponse<String>> updateTranscriptionResult(
            @PathVariable Long id,
            @RequestBody Map<String, Object> resultData) {
        try {
            String transcriptionText = (String) resultData.get("transcriptionText");
            Double confidence = resultData.get("confidence") != null ?
                    Double.valueOf(resultData.get("confidence").toString()) : null;
            String language = (String) resultData.get("language");
            Long processingTime = resultData.get("processingTime") != null ?
                    Long.valueOf(resultData.get("processingTime").toString()) : null;
            Integer duration = resultData.get("duration") != null ?
                    Integer.valueOf(resultData.get("duration").toString()) : null;

            transcriptionService.saveTranscriptionResult(id, transcriptionText, confidence,
                    language, processingTime);

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Resultado de transcripción actualizado exitosamente", "OK"));

        } catch (Exception e) {
            log.error("Error actualizando resultado de transcripción: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Actualiza el estado de una transcripción
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<GenericResponse<String>> updateTranscriptionStatus(
            @PathVariable Long id,
            @RequestBody Map<String, Object> statusData) {
        try {
            String status = (String) statusData.get("status");
            String notes = (String) statusData.get("notes");

            TranscriptionStatus transcriptionStatus = TranscriptionStatus.valueOf(status);
            transcriptionService.updateTranscriptionStatus(id, transcriptionStatus);

            // Si hay notas adicionales, actualizar también
            if (notes != null) {
                // TODO: Implementar actualización de notas si es necesario
            }

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Estado de transcripción actualizado exitosamente", "OK"));

        } catch (Exception e) {
            log.error("Error actualizando estado de transcripción: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error interno del servidor", null));
        }
    }

    /**
     * Obtiene el usuario actual desde el request
     */
    private String getCurrentUser(HttpServletRequest request) {
        // TODO: Implementar obtención del usuario desde el token JWT o sesión
        return "sistema"; // Placeholder
    }
}
