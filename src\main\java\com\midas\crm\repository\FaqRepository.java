package com.midas.crm.repository;

import com.midas.crm.entity.EstadoFaq;
import com.midas.crm.entity.Faq;
import com.midas.crm.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FaqRepository extends JpaRepository<Faq, Long> {
    // Métodos existentes
    List<Faq> findByCategoria(String categoria);

    // Nuevos métodos para preguntas y respuestas de usuarios
    List<Faq> findByUsuarioPregunta(User usuarioPregunta);
    List<Faq> findByUsuarioPregunta_Id(Long usuarioId);
    List<Faq> findByRespondidaFalse();
    List<Faq> findByEsPublicaTrue();
    List<Faq> findByEsPublicaFalseAndUsuarioPregunta_Id(Long usuarioId);

    // Métodos para el estado de las preguntas
    List<Faq> findByEstado(EstadoFaq estado);
    List<Faq> findByEstadoAndUsuarioPregunta_Id(EstadoFaq estado, Long usuarioId);
    List<Faq> findByEstadoAndEsPublicaTrue(EstadoFaq estado);

    // Consulta personalizada para obtener preguntas sin responder
    @Query("SELECT f FROM Faq f WHERE f.respondida = false ORDER BY f.createdAt DESC")
    List<Faq> findPreguntasSinResponder();

    // Consulta personalizada para obtener preguntas públicas
    @Query("SELECT f FROM Faq f WHERE f.esPublica = true ORDER BY f.createdAt DESC")
    List<Faq> findPreguntasPublicas();

    // Consulta personalizada para obtener preguntas por estado
    @Query("SELECT f FROM Faq f WHERE f.estado = :estado ORDER BY f.createdAt DESC")
    List<Faq> findPreguntasPorEstado(EstadoFaq estado);
}