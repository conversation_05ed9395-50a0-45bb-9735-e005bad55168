package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Curso;
import com.midas.crm.entity.User;
import com.midas.crm.entity.DTO.curso.CursoCreateDTO;
import com.midas.crm.entity.DTO.curso.CursoDTO;
import com.midas.crm.entity.DTO.curso.CursoListDTO;
import com.midas.crm.entity.DTO.curso.CursoUpdateDTO;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.CursoMapper;
import com.midas.crm.repository.CursoRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.CursoService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CursoServiceImpl implements CursoService {

    /* ---------- L1 cache ---------- */
    private final Map<Long, Curso> cursoL1 = new ConcurrentHashMap<>();
    private final Map<String, List<CursoDTO>> listL1 = new ConcurrentHashMap<>();
    private final Map<String, List<CursoListDTO>> listOptimizedL1 = new ConcurrentHashMap<>();

    /* ---------- dependencias ---------- */
    private final CursoRepository cursoRepository;
    private final UserRepository userRepository;

    @Override
    @Transactional
    @CacheEvict(cacheNames = { "cursoById", "cursoList" }, allEntries = true)
    public CursoDTO createCurso(CursoCreateDTO dto) {
        if (cursoRepository.existsByNombre(dto.getNombre())) {
            throw new MidasExceptions(MidasErrorMessage.CURSO_ALREADY_EXISTS);
        }

        Curso curso = new Curso();
        curso.setNombre(dto.getNombre());
        curso.setDescripcion(dto.getDescripcion());
        curso.setFechaInicio(dto.getFechaInicio());
        curso.setFechaFin(dto.getFechaFin());
        curso.setEstado("A");
        curso.setVideoUrl(dto.getVideoUrl());

        // 🔥 Si envía usuarioId, lo asignamos
        if (dto.getUsuarioId() != null) {
            User usuario = userRepository.findById(dto.getUsuarioId())
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_USUARIO_NOT_FOUND));
            curso.setUsuario(usuario);
        }

        Curso saved = cursoRepository.save(curso);
        clearL1();
        return CursoMapper.toDTO(saved);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "cursoList", key = "'all'")
    public List<CursoDTO> listCursos() {
        return listL1.computeIfAbsent("all", k ->
                cursoRepository.findAll()
                        .stream()
                        .map(CursoMapper::toDTO)
                        .collect(Collectors.toList()));
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "cursoById", key = "#id")
    public CursoDTO getCursoById(Long id) {
        Curso curso = cursoL1.computeIfAbsent(id,
                k -> cursoRepository.findById(k)
                        .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND)));
        return CursoMapper.toDTO(curso);
    }

    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="cursoById", key="#id"),
            @CacheEvict(cacheNames="cursoList", allEntries=true)
    })
    public CursoDTO updateCurso(Long id, CursoUpdateDTO dto) {
        Curso curso = cursoRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND));

        CursoMapper.updateEntity(curso, dto);

        Curso saved = cursoRepository.save(curso);
        invalidate(id);
        return CursoMapper.toDTO(saved);
    }

    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="cursoById", key="#id"),
            @CacheEvict(cacheNames="cursoList", allEntries=true)
    })
    public void deleteCurso(Long id) {
        Curso curso = cursoRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND));

        cursoRepository.delete(curso);
        invalidate(id);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "cursoList", key = "#ids")
    public List<CursoDTO> getCursosByIds(List<Long> ids) {
        String key = "ids:" + ids.toString();
        return listL1.computeIfAbsent(key, k -> {
            List<Curso> cursos = cursoRepository.findAllById(ids);
            return cursos.stream()
                    .map(CursoMapper::toDTO)
                    .collect(Collectors.toList());
        });
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "cursoList", key = "{'optimized',#ids}")
    public List<CursoListDTO> getCursosOptimizedByIds(List<Long> ids) {
        String key = "optimized:" + ids.toString();
        return listOptimizedL1.computeIfAbsent(key, k -> {
            List<Curso> cursos = cursoRepository.findAllById(ids);
            return cursos.stream()
                    .map(this::toCursoListDTO)
                    .collect(Collectors.toList());
        });
    }

    @Override
    public List<CursoListDTO> getCursosOptimizedByIdsWithUserAssignments(List<Long> ids, Long usuarioId) {
        List<Curso> cursos = cursoRepository.findAllById(ids);
        return cursos.stream()
                .map(curso -> toCursoListDTOWithAssignment(curso, usuarioId))
                .collect(Collectors.toList());
    }

    private CursoListDTO toCursoListDTO(Curso curso) {
        CursoListDTO dto = new CursoListDTO();
        dto.setId(curso.getId());
        dto.setNombre(curso.getNombre());
        dto.setDescripcion(curso.getDescripcion());
        dto.setFechaInicio(curso.getFechaInicio());
        dto.setFechaFin(curso.getFechaFin());
        dto.setEstado(curso.getEstado());
        dto.setVideoUrl(curso.getVideoUrl());

        if (curso.getUsuario() != null) {
            dto.setUsuarioNombre(curso.getUsuario().getNombre());
            dto.setUsuarioApellido(curso.getUsuario().getApellido());
        }

        return dto;
    }

    private CursoListDTO toCursoListDTOWithAssignment(Curso curso, Long usuarioId) {
        CursoListDTO dto = toCursoListDTO(curso);

        // Aquí podrías agregar lógica para obtener información de asignación
        // si es necesario, pero por ahora mantenemos simple

        return dto;
    }

    /* =============================================================== */
    /*                      CACHE MANAGEMENT                           */
    /* =============================================================== */

    /**
     * Limpia todo el cache L1
     */
    private void clearL1() {
        cursoL1.clear();
        listL1.clear();
        listOptimizedL1.clear();
    }

    /**
     * Invalida cache específico para un curso
     */
    private void invalidate(Long id) {
        cursoL1.remove(id);
        // Limpiar cache de listas que podrían contener este curso
        listL1.clear();
        listOptimizedL1.clear();
    }

    /**
     * Método async para publicar eventos
     */
    @Async
    void publishAsync(Object event) {
        log.debug("Evento publicado: {}", event.getClass().getSimpleName());
    }
}
