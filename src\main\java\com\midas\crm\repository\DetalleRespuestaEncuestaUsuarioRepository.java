package com.midas.crm.repository;

import com.midas.crm.entity.DetalleRespuestaEncuestaUsuario;
import com.midas.crm.entity.OpcionRespuestaEncuesta;
import com.midas.crm.entity.PreguntaEncuesta;
import com.midas.crm.entity.RespuestaEncuestaUsuario;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface DetalleRespuestaEncuestaUsuarioRepository extends JpaRepository<DetalleRespuestaEncuestaUsuario, Long> {

    // Buscar detalles por respuesta de usuario
    List<DetalleRespuestaEncuestaUsuario> findByRespuestaEncuestaUsuario(RespuestaEncuestaUsuario respuestaEncuestaUsuario);
    List<DetalleRespuestaEncuestaUsuario> findByRespuestaEncuestaUsuarioId(Long respuestaEncuestaUsuarioId);

    // Buscar detalles por respuesta de usuario y pregunta
    List<DetalleRespuestaEncuestaUsuario> findByRespuestaEncuestaUsuarioAndPregunta(RespuestaEncuestaUsuario respuestaEncuestaUsuario, PreguntaEncuesta pregunta);
    List<DetalleRespuestaEncuestaUsuario> findByRespuestaEncuestaUsuarioIdAndPreguntaId(Long respuestaEncuestaUsuarioId, Long preguntaId);

    // Contar respuestas por opción para una pregunta específica
    @Query("SELECT COUNT(d) FROM DetalleRespuestaEncuestaUsuario d WHERE d.pregunta.id = :preguntaId AND d.opcion.id = :opcionId")
    Long countByPreguntaIdAndOpcionId(@Param("preguntaId") Long preguntaId, @Param("opcionId") Long opcionId);

    // Obtener estadísticas de respuestas para una pregunta específica
    @Query("SELECT d.opcion.id as opcionId, COUNT(d) as cantidad FROM DetalleRespuestaEncuestaUsuario d " +
            "WHERE d.pregunta.id = :preguntaId AND d.opcion IS NOT NULL " +
            "GROUP BY d.opcion.id")
    List<Map<String, Object>> getEstadisticasPorPregunta(@Param("preguntaId") Long preguntaId);

    // Obtener promedio de respuestas numéricas para una pregunta específica
    @Query("SELECT AVG(d.respuestaNumero) FROM DetalleRespuestaEncuestaUsuario d " +
            "WHERE d.pregunta.id = :preguntaId AND d.respuestaNumero IS NOT NULL")
    Double getPromedioRespuestasNumericasPorPregunta(@Param("preguntaId") Long preguntaId);
}
