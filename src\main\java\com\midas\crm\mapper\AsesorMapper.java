package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.asesor.AsesorDisponibleDTO;
import com.midas.crm.entity.User;

public class AsesorMapper {

    public static AsesorDTO toDTO(User user) {
        // Obtener el nombre de la sede, primero desde sedeNombre y si es null, desde la relación sede
        String nombreSede = user.getSedeNombre();
        if (nombreSede == null && user.getSede() != null) {
            nombreSede = user.getSede().getNombre();
        }

        return new AsesorDTO(
                user.getId(),
                user.getNombre(),
                user.getApellido(),
                user.getUsername(),
                user.getDni(),
                user.getEmail(),
                user.getTelefono(),
                nombreSede
        );
    }

    public static AsesorDisponibleDTO toDisponibleDTO(User user) {
        // Determinar la sede respetando sedeNombre o la relación sede
        String sedeNombre = user.getSedeNombre();
        if (sedeNombre == null && user.getSede() != null) {
            sedeNombre = user.getSede().getNombre();
        }

        return new AsesorDisponibleDTO(
                user.getId(),
                user.getNombre(),
                user.getApellido(),
                user.getUsername(),
                sedeNombre
        );
    }
}
