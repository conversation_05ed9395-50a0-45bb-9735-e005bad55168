package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.cuestionario.RespuestaCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUpdateDTO;
import com.midas.crm.entity.Pregunta;
import com.midas.crm.entity.Respuesta;

public final class RespuestaMapper {

    private RespuestaMapper() {}

    public static Respuesta toEntity(RespuestaCreateDTO dto, Pregunta pregunta) {
        Respuesta respuesta = new Respuesta();
        respuesta.setTexto(dto.getTexto());
        respuesta.setEsCorrecta(dto.getEsCorrecta());
        respuesta.setOrden(dto.getOrden());
        respuesta.setPregunta(pregunta);
        respuesta.setEstado("A");
        return respuesta;
    }

    public static RespuestaDTO toDTO(Respuesta respuesta) {
        if (respuesta == null) return null;

        return new RespuestaDTO(
                respuesta.getId(),
                respuesta.getTexto(),
                respuesta.getEsCorrecta(),
                respuesta.getOrden(),
                respuesta.getPregunta() != null ? respuesta.getPregunta().getId() : null,
                respuesta.getEstado(),
                respuesta.getFechaCreacion(),
                respuesta.getFechaActualizacion()
        );
    }

    public static void updateEntity(Respuesta respuesta, RespuestaUpdateDTO dto) {
        if (dto.getTexto() != null) respuesta.setTexto(dto.getTexto());
        if (dto.getEsCorrecta() != null) respuesta.setEsCorrecta(dto.getEsCorrecta());
        if (dto.getOrden() != null) respuesta.setOrden(dto.getOrden());
        if (dto.getEstado() != null) respuesta.setEstado(dto.getEstado());
    }
}
