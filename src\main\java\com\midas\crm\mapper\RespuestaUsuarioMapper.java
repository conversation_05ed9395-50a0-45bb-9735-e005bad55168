package com.midas.crm.mapper;

import com.midas.crm.entity.Cuestionario;
import com.midas.crm.entity.DTO.cuestionario.DetalleRespuestaUsuarioDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUsuarioDTO;
import com.midas.crm.entity.RespuestaUsuario;
import com.midas.crm.entity.User;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

public final class RespuestaUsuarioMapper {

    private RespuestaUsuarioMapper() {}

    public static RespuestaUsuario toEntity(RespuestaUsuarioCreateDTO dto, User usuario, Cuestionario cuestionario, int numeroIntento) {
        RespuestaUsuario respuestaUsuario = new RespuestaUsuario();
        respuestaUsuario.setUsuario(usuario);
        respuestaUsuario.setCuestionario(cuestionario);
        respuestaUsuario.setFechaInicio(LocalDateTime.now());
        respuestaUsuario.setCompletado(false);
        respuestaUsuario.setAprobado(false);
        respuestaUsuario.setNumeroIntento(numeroIntento);
        return respuestaUsuario;
    }

    public static RespuestaUsuarioDTO toDTO(RespuestaUsuario respuestaUsuario) {
        if (respuestaUsuario == null) return null;

        List<DetalleRespuestaUsuarioDTO> detallesDTO = null;
        if (respuestaUsuario.getDetallesRespuestas() != null && !respuestaUsuario.getDetallesRespuestas().isEmpty()) {
            detallesDTO = respuestaUsuario.getDetallesRespuestas().stream()
                    .map(DetalleRespuestaUsuarioMapper::toDTO)
                    .collect(Collectors.toList());
        }

        return new RespuestaUsuarioDTO(
                respuestaUsuario.getId(),
                respuestaUsuario.getUsuario() != null ? respuestaUsuario.getUsuario().getId() : null,
                respuestaUsuario.getUsuario() != null ? respuestaUsuario.getUsuario().getNombre() + " " + respuestaUsuario.getUsuario().getApellido() : null,
                respuestaUsuario.getCuestionario() != null ? respuestaUsuario.getCuestionario().getId() : null,
                respuestaUsuario.getCuestionario() != null ? respuestaUsuario.getCuestionario().getTitulo() : null,
                respuestaUsuario.getFechaInicio(),
                respuestaUsuario.getFechaFin(),
                respuestaUsuario.getPuntajeObtenido(),
                respuestaUsuario.getPorcentajeAprobacion(),
                respuestaUsuario.getCompletado(),
                respuestaUsuario.getAprobado(),
                respuestaUsuario.getNumeroIntento(),
                respuestaUsuario.getFechaCreacion(),
                respuestaUsuario.getFechaActualizacion(),
                detallesDTO
        );
    }
}
