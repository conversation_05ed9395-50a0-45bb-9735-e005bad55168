package com.midas.crm.controller;

import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.asesor.AsesorDisponibleDTO;
import com.midas.crm.entity.DTO.asesor.AsignacionAsesorDTO;
import com.midas.crm.entity.DTO.asesor.AsignacionMasivaDTO;
import com.midas.crm.entity.DTO.coordinador.CoordinadorDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.CoordinadorService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;

@RestController
@RequiredArgsConstructor
@RequestMapping("${api.route.coordinadores}")
@Slf4j
public class CoordinadorController {

    private final CoordinadorService coordinadorService;
    private final UserRepository userRepository;

    /**
     * Asigna asesores a un coordinador
     * Implementado con programación funcional para mejor manejo de errores
     */
    @PostMapping("/asignar-asesores")
    public ResponseEntity<?> asignarAsesoresACoordinador(@RequestBody AsignacionAsesorDTO asignacionDTO) {
        return Optional.ofNullable(asignacionDTO)
                .map(dto -> {
                    try {
                        CoordinadorDTO coordinador = coordinadorService.asignarAsesoresACoordinador(dto);
                        return ResponseEntity.ok(coordinador);
                    } catch (Exception e) {
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                .body(new GenericResponse<>(
                                        GenericResponseConstants.ERROR,
                                        "Error al asignar asesores: " + e.getMessage(),
                                        null));
                    }
                })
                .orElseGet(() -> {
                    return ResponseEntity.badRequest()
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "Datos de asignación inválidos",
                                    null));
                });
    }

    /**
     * Obtiene todos los coordinadores de forma paginada (versión reducida)
     * Implementado con programación funcional para mejor manejo de datos y errores
     */
    @GetMapping
    public GenericResponse<Map<String,Object>> listarCoordinadores(
            @RequestParam int page,
            @RequestParam int size,
            @RequestParam(required = false) String term
    ) {
        return coordinadorService.listarCoordinadoresReducido(page, size, term);
    }

    /**
     * Obtiene un coordinador por su ID con detalle completo (incluyendo lista de asesores)
     * Implementado con programación funcional para mejor manejo de errores
     */
    @GetMapping("/{coordinadorId}")
    public ResponseEntity<?> obtenerCoordinadorPorId(@PathVariable Long coordinadorId) {
        return Optional.ofNullable(coordinadorId)
                .map(id -> {
                    try {
                        CoordinadorDTO coordinador = coordinadorService.obtenerCoordinadorPorId(id);
                        return ResponseEntity.ok(coordinador);
                    } catch (Exception e) {
                        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                .body(new GenericResponse<>(
                                        GenericResponseConstants.ERROR,
                                        "Error al obtener coordinador: " + e.getMessage(),
                                        null));
                    }
                })
                .orElseGet(() -> {
                    return ResponseEntity.badRequest()
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "ID de coordinador inválido",
                                    null));
                });
    }

    /**
     * Obtiene todos los coordinadores con detalle completo (incluyendo lista de asesores)
     * Útil para casos donde se necesita la información completa de los asesores
     */
    @GetMapping("/detalle")
    public GenericResponse<Map<String,Object>> listarCoordinadoresDetalle(
            @RequestParam int page,
            @RequestParam int size,
            @RequestParam(required = false) String term
    ) {
        Page<CoordinadorDTO> p = coordinadorService.obtenerTodosLosCoordinadoresPaginado(page, size, term);
        Map<String,Object> data = Map.of(
                "coordinadores", p.getContent(),
                "totalItems",   p.getTotalElements(),
                "totalPages",   p.getTotalPages(),
                "currentPage",  p.getNumber(),
                "size",         p.getSize(),
                "hasNext",      p.hasNext(),
                "hasPrevious",  p.hasPrevious()
        );
        return new GenericResponse<>(1, "Coordinadores obtenidos correctamente", data);
    }

    /**
     * Obtiene la lista de asesores sin coordinador asignado
     * Implementado con programación funcional para mejor manejo de datos y errores
     */
    @GetMapping("/asesores-disponibles")
    public ResponseEntity<?> obtenerAsesoresSinCoordinador(
            @RequestParam(defaultValue="0")  int page,
            @RequestParam(defaultValue="10") int size,
            @RequestParam(required = false)  String term) {

        var resp = coordinadorService.obtenerAsesoresSinCoordinador(page, size, term);
        return ResponseEntity.ok(resp);
    }

    /**
     * ✅ MEJORADO: Obtiene asesores de un coordinador - AHORA CON PAGINACIÓN OPCIONAL
     * Si no se envían parámetros de paginación, devuelve todos los asesores (comportamiento actual)
     * Si se envían parámetros de paginación, devuelve resultado paginado
     */
    @GetMapping("/{coordinadorId}/asesores")
    public GenericResponse<Map<String,Object>> detalleAsesores(
            @PathVariable Long coordinadorId,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer size,
            @RequestParam(required = false) String term) {

        // Si no se proporcionan parámetros de paginación, usar el método original
        if (page == null || size == null) {
            List<AsesorDTO> asesores = coordinadorService.obtenerAsesoresPorCoordinador(coordinadorId);

            // Aplicar filtro si se proporciona término de búsqueda
            if (term != null && !term.trim().isEmpty()) {
                String termLower = term.trim().toLowerCase();
                asesores = asesores.stream()
                        .filter(asesor ->
                                (asesor.getNombre() + " " + asesor.getApellido()).toLowerCase().contains(termLower) ||
                                        (asesor.getDni() != null && asesor.getDni().toLowerCase().contains(termLower)) ||
                                        (asesor.getUsername() != null && asesor.getUsername().toLowerCase().contains(termLower)) ||
                                        (asesor.getEmail() != null && asesor.getEmail().toLowerCase().contains(termLower))
                        )
                        .toList();
            }

            return new GenericResponse<>(1, "Asesores del coordinador", Map.of(
                    "asesores", asesores,
                    "totalItems", asesores.size(),
                    "totalPages", 1,
                    "currentPage", 0,
                    "size", asesores.size(),
                    "hasNext", false,
                    "hasPrevious", false
            ));
        }

        // Si se proporcionan parámetros de paginación, usar el método paginado
        return coordinadorService.obtenerAsesoresPorCoordinadorPaginado(coordinadorId, page, size, term);
    }

    /**
     * Elimina un asesor de un coordinador
     * Implementado con programación funcional para mejor manejo de resultados y errores
     */
    @DeleteMapping("/{coordinadorId}/asesores/{asesorId}")
    public ResponseEntity<?> eliminarAsesorDeCoordinador(
            @PathVariable Long coordinadorId,
            @PathVariable Long asesorId) {

        // Validar que los IDs no sean nulos
        if (coordinadorId == null || asesorId == null) {
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "IDs de coordinador o asesor inválidos",
                            null));
        }

        try {

            // Usar programación funcional para manejar el resultado
            return Optional.of(coordinadorService.eliminarAsesorDeCoordinador(coordinadorId, asesorId))
                    .filter(Boolean::booleanValue)
                    .map(eliminado -> {
                        return ResponseEntity.ok(
                                new GenericResponse<>(
                                        GenericResponseConstants.SUCCESS,
                                        "Asesor removido del coordinador exitosamente",
                                        true));
                    })
                    .orElseGet(() -> {
                        return ResponseEntity.badRequest()
                                .body(new GenericResponse<>(
                                        GenericResponseConstants.ERROR,
                                        "El asesor no pertenece a este coordinador",
                                        false));
                    });
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al eliminar asesor: " + e.getMessage(),
                            null));
        }
    }

    /**
     * Asigna múltiples asesores a un coordinador de forma masiva
     * Implementado con programación funcional para mejor manejo de datos y errores
     */
    @PostMapping("/asignar-masivo")
    public ResponseEntity<?> asignarAsesoresMasivo(@RequestBody AsignacionMasivaDTO asignacionDTO) {
        return Optional.ofNullable(asignacionDTO)
                .filter(dto -> dto.getCoordinadorId() != null && dto.getAsesorIds() != null && !dto.getAsesorIds().isEmpty())
                .map(dto -> {
                    try {
                        // Verificar si el coordinador existe y es válido
                        return userRepository.findById(dto.getCoordinadorId())
                                .filter(coordinador -> coordinador.getRole() == Role.COORDINADOR)
                                .map(coordinador -> {
                                    // Procesar los asesores usando programación funcional
                                    int asignadosExitosamente = dto.getAsesorIds().stream()
                                            .map(asesorId -> userRepository.findById(asesorId).orElse(null))
                                            .filter(asesor -> asesor != null && asesor.getRole() == Role.ASESOR)
                                            .map(asesor -> {
                                                // Crear un objeto User con solo el ID para el coordinador
                                                User coordinadorRef = new User(dto.getCoordinadorId());
                                                asesor.setCoordinador(coordinadorRef);
                                                return userRepository.save(asesor);
                                            })
                                            .mapToInt(asesor -> 1)
                                            .sum();

                                    return ResponseEntity.ok(
                                            new GenericResponse<>(
                                                    GenericResponseConstants.SUCCESS,
                                                    "Se asignaron " + asignadosExitosamente + " asesores al coordinador",
                                                    asignadosExitosamente));
                                })
                                .orElseGet(() -> {
                                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                            .body(new GenericResponse<>(
                                                    GenericResponseConstants.ERROR,
                                                    "Coordinador no encontrado o no es un coordinador válido",
                                                    null));
                                });
                    } catch (Exception e) {
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(new GenericResponse<>(
                                        GenericResponseConstants.ERROR,
                                        "Error al asignar asesores: " + e.getMessage(),
                                        null));
                    }
                })
                .orElseGet(() -> {
                    return ResponseEntity.badRequest()
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "Datos de asignación masiva inválidos",
                                    null));
                });
    }
}