package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.faq.FaqRespuestaDTO;
import com.midas.crm.entity.DTO.faq.FileFaqRespuestaDTO;
import com.midas.crm.entity.DTO.user.UserDTO;
import com.midas.crm.entity.Faq;
import com.midas.crm.entity.FaqRespuesta;
import com.midas.crm.entity.FileFaqRespuesta;
import com.midas.crm.entity.User;
import com.midas.crm.repository.FaqRepository;
import com.midas.crm.repository.FaqRespuestaRepository;
import com.midas.crm.repository.UserRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class FaqRespuestaServiceImpl {

    @Autowired
    private FaqRespuestaRepository faqRespuestaRepository;

    @Autowired
    private FaqRepository faqRepository;

    @Autowired
    private UserRepository userRepository;

    @Transactional
    public FaqRespuestaDTO agregarRespuesta(Long faqId, FaqRespuestaDTO respuestaDTO, Long usuarioId) {
        // Buscar la pregunta
        Faq faq = faqRepository.findById(faqId)
                .orElseThrow(() -> new EntityNotFoundException("FAQ no encontrado con id: " + faqId));

        // Buscar el usuario
        User usuario = userRepository.findById(usuarioId)
                .orElseThrow(() -> new EntityNotFoundException("Usuario no encontrado con id: " + usuarioId));

        // Crear la respuesta
        FaqRespuesta respuesta = new FaqRespuesta();
        respuesta.setContenido(respuestaDTO.getContenido());
        respuesta.setUsuario(usuario);
        respuesta.setFaq(faq);

        // Agregar archivos si existen
        if (respuestaDTO.getArchivos() != null && !respuestaDTO.getArchivos().isEmpty()) {
            for (FileFaqRespuestaDTO fileDTO : respuestaDTO.getArchivos()) {
                FileFaqRespuesta file = new FileFaqRespuesta();
                file.setName(fileDTO.getName());
                file.setType(fileDTO.getType());
                file.setSize(fileDTO.getSize());
                file.setUrl(fileDTO.getUrl());
                respuesta.getArchivos().add(file);
            }
        }

        // Guardar la respuesta
        FaqRespuesta savedRespuesta = faqRespuestaRepository.save(respuesta);

        // Actualizar el estado de la pregunta
        faq.agregarRespuesta(savedRespuesta);
        faqRepository.save(faq);

        return convertToDTO(savedRespuesta);
    }

    @Transactional
    public FaqRespuestaDTO actualizarRespuesta(Long respuestaId, FaqRespuestaDTO respuestaDTO) {
        // Buscar la respuesta
        FaqRespuesta respuesta = faqRespuestaRepository.findById(respuestaId)
                .orElseThrow(() -> new EntityNotFoundException("Respuesta no encontrada con id: " + respuestaId));

        // Actualizar el contenido
        respuesta.setContenido(respuestaDTO.getContenido());

        // Actualizar archivos
        respuesta.getArchivos().clear();
        if (respuestaDTO.getArchivos() != null && !respuestaDTO.getArchivos().isEmpty()) {
            for (FileFaqRespuestaDTO fileDTO : respuestaDTO.getArchivos()) {
                FileFaqRespuesta file = new FileFaqRespuesta();
                file.setName(fileDTO.getName());
                file.setType(fileDTO.getType());
                file.setSize(fileDTO.getSize());
                file.setUrl(fileDTO.getUrl());
                respuesta.getArchivos().add(file);
            }
        }

        // Guardar los cambios
        FaqRespuesta updatedRespuesta = faqRespuestaRepository.save(respuesta);
        return convertToDTO(updatedRespuesta);
    }

    @Transactional
    public void eliminarRespuesta(Long respuestaId) {
        // Buscar la respuesta
        FaqRespuesta respuesta = faqRespuestaRepository.findById(respuestaId)
                .orElseThrow(() -> new EntityNotFoundException("Respuesta no encontrada con id: " + respuestaId));

        // Obtener la pregunta asociada
        Faq faq = respuesta.getFaq();

        // Eliminar la respuesta de la pregunta
        faq.eliminarRespuesta(respuesta);
        faqRepository.save(faq);

        // Eliminar la respuesta
        faqRespuestaRepository.delete(respuesta);
    }

    @Transactional(readOnly = true)
    public List<FaqRespuestaDTO> getRespuestasByFaqId(Long faqId) {
        return faqRespuestaRepository.findByFaqId(faqId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<FaqRespuestaDTO> getRespuestasByUsuarioId(Long usuarioId) {
        return faqRespuestaRepository.findByUsuarioId(usuarioId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    // Método de conversión de Entity a DTO
    private FaqRespuestaDTO convertToDTO(FaqRespuesta respuesta) {
        FaqRespuestaDTO dto = new FaqRespuestaDTO();
        dto.setId(respuesta.getId());
        dto.setContenido(respuesta.getContenido());
        dto.setCreatedAt(respuesta.getCreatedAt());
        dto.setUpdatedAt(respuesta.getUpdatedAt());

        // Establecer el ID de la FAQ
        if (respuesta.getFaq() != null) {
            dto.setFaqId(respuesta.getFaq().getId());
        }

        // Establecer información del usuario
        if (respuesta.getUsuario() != null) {
            dto.setUsuarioId(respuesta.getUsuario().getId());
            dto.setUsuarioNombre(respuesta.getUsuario().getNombre() + " " + respuesta.getUsuario().getApellido());

            // Crear UserDTO
            UserDTO userDTO = new UserDTO();
            userDTO.setId(respuesta.getUsuario().getId());
            userDTO.setNombre(respuesta.getUsuario().getNombre());
            userDTO.setApellido(respuesta.getUsuario().getApellido());
            userDTO.setUsername(respuesta.getUsuario().getUsername());
            userDTO.setEmail(respuesta.getUsuario().getEmail());
            userDTO.setRole(respuesta.getUsuario().getRole());
            dto.setUsuario(userDTO);
        }

        // Convertir archivos
        if (respuesta.getArchivos() != null && !respuesta.getArchivos().isEmpty()) {
            List<FileFaqRespuestaDTO> archivosDTO = respuesta.getArchivos().stream()
                    .map(file -> {
                        FileFaqRespuestaDTO fileDTO = new FileFaqRespuestaDTO();
                        fileDTO.setId(file.getId());
                        fileDTO.setName(file.getName());
                        fileDTO.setType(file.getType());
                        fileDTO.setSize(file.getSize());
                        fileDTO.setUrl(file.getUrl());
                        return fileDTO;
                    })
                    .collect(Collectors.toList());
            dto.setArchivos(archivosDTO);
        }

        return dto;
    }
}
