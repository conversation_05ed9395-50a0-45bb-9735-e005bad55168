package com.midas.crm.entity.DTO.coordinador;

public class CoordinadorListDTO {
    private Long id;
    private String nombre;
    private String apellido;
    private String dni;
    private String sede;
    private long asesorCount;

    // Constructor requerido por la proyección
    public CoordinadorListDTO(Long id,
                              String nombre,
                              String apellido,
                              String dni,
                              String sede,
                              long asesorCount) {
        this.id = id;
        this.nombre = nombre;
        this.apellido = apellido;
        this.dni = dni;
        this.sede = sede;
        this.asesorCount = asesorCount;
    }

    // Getters (y setters si los necesitas)
    public Long getId() { return id; }
    public String getNombre() { return nombre; }
    public String getApellido() { return apellido; }
    public String getDni() { return dni; }
    public String getSede() { return sede; }
    public long getAsesorCount() { return asesorCount; }
}
