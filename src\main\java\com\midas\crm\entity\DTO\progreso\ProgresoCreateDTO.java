package com.midas.crm.entity.DTO.progreso;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgresoCreateDTO {
    @NotNull(message = "El ID de la lección es obligatorio")
    private Long leccionId;
    
    @NotNull(message = "El ID del usuario es obligatorio")
    private Long usuarioId;
    
    private Boolean completado;
    
    private Integer segundosVistos;
    
    private Integer porcentajeCompletado;
    
    private Integer ultimaPosicion;
}
