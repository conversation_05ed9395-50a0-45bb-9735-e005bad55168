package com.midas.crm.service;

import org.springframework.data.domain.Page;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.queue.TranscriptionQueueMessage;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Servicio para gestionar las colas de transcripción automática.
 * Define sólo la lógica de negocio, sin acoplar detalles de infraestructura.
 */
public interface TranscriptionQueueService {

    /**
     * Procesa leads pendientes de transcripción y los envía a la cola.
     *
     * @param batchSize     Número máximo de leads a procesar.
     * @param numeroAgente  Filtro opcional por número de agente.
     * @param allDates      Si true → incluye todos los meses; false → mes actual.
     * @param selectedDate  Si no es null → filtra sólo ese día.
     * @return Mapa con estadísticas del procesamiento.
     */
    Map<String, Object> processPendingLeads(int batchSize,
                                            String numeroAgente,
                                            boolean allDates,
                                            LocalDate selectedDate);

    /**
     * Procesa leads pendientes SOLO para transcripción (sin enviar a comparación).
     *
     * @param batchSize     Número máximo de leads a procesar.
     * @param numeroAgente  Filtro opcional por número de agente.
     * @param allDates      Si true → incluye todos los meses; false → mes actual.
     * @param selectedDate  Si no es null → filtra sólo ese día.
     * @return Mapa con estadísticas del procesamiento.
     */
    Map<String, Object> processTranscriptionOnly(int batchSize,
                                                 String numeroAgente,
                                                 boolean allDates,
                                                 LocalDate selectedDate);

    /**
     * Procesa leads que YA TIENEN transcripción SOLO para comparación.
     *
     * @param batchSize     Número máximo de leads a procesar.
     * @param numeroAgente  Filtro opcional por número de agente.
     * @param allDates      Si true → incluye todos los meses; false → mes actual.
     * @param selectedDate  Si no es null → filtra sólo ese día.
     * @return Mapa con estadísticas del procesamiento.
     */
    Map<String, Object> processComparisonOnly(int batchSize,
                                              String numeroAgente,
                                              boolean allDates,
                                              LocalDate selectedDate);

    /**
     * Obtiene estadísticas de las colas de transcripción para un día específico o mes/all-time.
     *
     * @param allDates      Si true → todos los meses (o all-time); false → mes actual.
     * @param selectedDate  Si no es null → estadísticas sólo de ese día.
     * @return Mapa con estadísticas.
     */
    Map<String, Object> getQueueStatistics(boolean allDates,
                                           LocalDate selectedDate);

    /**
     * Obtiene estadísticas de las colas de transcripción para el mes actual o all-time.
     *
     * @param currentMonthOnly  true → mes actual; false → all-time.
     * @return Mapa con estadísticas.
     */
    Map<String, Object> getQueueStatistics(boolean currentMonthOnly);

    /**
     * Lista de leads pendientes de transcripción.
     *
     * @param limit           Número máximo de leads a retornar.
     * @param numeroAgente    Filtro opcional por número de agente.
     * @param currentMonthOnly  true → mes actual; false → todos los meses.
     * @return Lista de mensajes de transcripción pendientes.
     */
    List<TranscriptionQueueMessage> getPendingLeads(int limit,
                                                    String numeroAgente,
                                                    boolean currentMonthOnly);

    /**
     * Reintenta el procesamiento de mensajes fallidos.
     *
     * @param maxRetries  Número máximo de mensajes a reintentar.
     * @return Mapa con estadísticas del reintento.
     */
    Map<String, Object> retryFailedMessages(int maxRetries);

    /**
     * Envía un lead específico a la cola de transcripción.
     *
     * @param leadId  ID del lead a enviar.
     * @return true si se envió correctamente; false en caso contrario.
     */
    boolean sendLeadToQueue(Long leadId);

    /**
     * Pausa el procesamiento de las colas.
     */
    void pauseProcessing();

    /**
     * Reanuda el procesamiento de las colas.
     */
    void resumeProcessing();

    /**
     * Normaliza el número de agente para manejar diferentes formatos.
     *
     * @param numeroAgente  Número de agente original.
     * @return Número de agente normalizado.
     */
    String normalizeAgentNumber(String numeroAgente);

    /**
     * Verifica si un lead cumple los criterios para ser procesado.
     *
     * @param leadId  ID del lead a verificar.
     * @return true si cumple los criterios; false en caso contrario.
     */
    boolean isLeadEligibleForProcessing(Long leadId);

    /**
     * Comprueba si el procesamiento está activo o pausado.
     *
     * @return true si activo; false si pausado.
     */
    boolean isProcessingActive();

    /**
     * Obtiene leads pendientes de la base de datos de forma paginada.
     *
     * @param page             El número de página (0-indexed).
     * @param size             El tamaño de la página.
     * @param numeroAgente     Filtro opcional por número de agente.
     * @param currentMonthOnly true → mes actual; false → todos los meses.
     * @return Una página de ClienteResidencial.
     */
    Page<ClienteResidencial> getPendingLeadsFromDatabasePaginated(int page,
                                                                  int size,
                                                                  String numeroAgente,
                                                                  boolean currentMonthOnly);

    /**
     * Versión extendida de paginación con filtro por fecha concreta.
     *
     * @param page          El número de página (0-indexed).
     * @param size          El tamaño de la página.
     * @param numeroAgente  Filtro opcional por número de agente.
     * @param allDates      true → all-time; false → mes actual.
     * @param selectedDate  Si no es null → filtra sólo ese día.
     * @return Una página de ClienteResidencial.
     */
    Page<ClienteResidencial> getPendingLeadsFromDatabasePaginated(int page,
                                                                  int size,
                                                                  String numeroAgente,
                                                                  boolean allDates,
                                                                  LocalDate selectedDate);

    /**
     * Obtiene el conteo total de leads pendientes.
     *
     * @param numeroAgente     Filtro opcional por número de agente.
     * @param currentMonthOnly true → mes actual; false → todos los meses.
     * @return El número total de leads pendientes.
     */
    long getTotalPendingLeadsCount(String numeroAgente,
                                   boolean currentMonthOnly);

    /**
     * 📊 Analiza la correspondencia entre archivos de audio y leads existentes.
     * Útil para debugging y verificación de que los audios se están asociando correctamente con los leads.
     *
     * @param fecha La fecha específica para analizar.
     * @return Mapa con análisis detallado de coincidencias, audios sin lead y leads sin audio.
     */
    Map<String, Object> analizarAudiosVsLeads(LocalDate fecha);
}
