package com.midas.crm.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.*;
import java.security.cert.X509Certificate;

/**
 * Configuración para RestTemplate con manejo de SSL
 */
@Configuration
@Slf4j
public class RestTemplateConfig {

    /**
     * RestTemplate que ignora certificados SSL (solo para desarrollo/testing)
     * Configurado con timeouts apropiados para APIs de transcripción
     */
    @Bean("transcriptionRestTemplate")
    public RestTemplate transcriptionRestTemplate() {
        try {
            // Crear un TrustManager que acepta todos los certificados
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        public X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }
                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        }
                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        }
                    }
            };

            // Instalar el TrustManager que acepta todos
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Crear un HostnameVerifier que acepta todos los hostnames
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Instalar el HostnameVerifier que acepta todos
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // 🔧 CONFIGURAR TIMEOUTS HOLGADOS PARA TRANSCRIPCIÓN (4 HORAS)
            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
            factory.setConnectTimeout(30_000);        // 30 segundos para conectar
            factory.setReadTimeout(14_500_000);       // ~4 horas para archivos grandes
            // Nota: setBufferRequestBody está deprecado, se maneja automáticamente

            RestTemplate restTemplate = new RestTemplate(factory);

            return restTemplate;

        } catch (Exception e) {
            // Fallback con timeouts básicos
            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
            factory.setConnectTimeout(60_000);
            factory.setReadTimeout(7_200_000);
            return new RestTemplate(factory);
        }
    }

    /**
     * 🏃‍♂️ RestTemplate rápido para envíos iniciales (30 segundos timeout)
     * Usado para el POST inicial que puede fallar con timeout esperado
     */
    @Bean("quickRestTemplate")
    public RestTemplate quickRestTemplate() {
        try {
            log.info("🔧 Configurando RestTemplate rápido para envíos iniciales...");

            // Configuración SSL (misma que arriba)
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        public X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }
                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        }
                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        }
                    }
            };

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            HostnameVerifier allHostsValid = (hostname, session) -> true;
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // ⚡ TIMEOUTS CORTOS PARA ENVÍO INICIAL
            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
            factory.setConnectTimeout(10_000);   // 10 segundos para conectar
            factory.setReadTimeout(30_000);      // 30 segundos para leer

            RestTemplate restTemplate = new RestTemplate(factory);

            log.info("✅ RestTemplate rápido configurado - Connect: 10s, Read: 30s");

            return restTemplate;

        } catch (Exception e) {
            log.error("❌ Error configurando RestTemplate rápido: {}", e.getMessage());
            // Fallback básico
            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
            factory.setConnectTimeout(10_000);
            factory.setReadTimeout(30_000);
            return new RestTemplate(factory);
        }
    }

    /**
     * RestTemplate estándar para uso general
     */
    @Bean("standardRestTemplate")
    public RestTemplate standardRestTemplate() {
        return new RestTemplate();
    }
}
