package com.midas.crm.entity.DTO.transcription;


import java.math.BigDecimal;
import java.time.LocalDateTime;

public record TAListadoDTO(
        Long id,
        Long clienteResidencialId,
        String nombreCliente,   // cliente
        String movilContacto,
        String numeroAgente,
        String nombresUsuario,     // asesor
        BigDecimal porcentajePromedio,
        String nivelConfianza,
        String estado,
        LocalDateTime fechaCreacion) {
}