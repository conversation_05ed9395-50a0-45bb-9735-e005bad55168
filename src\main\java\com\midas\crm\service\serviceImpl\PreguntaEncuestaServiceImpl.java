package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.encuesta.OpcionRespuestaEncuestaCreateDTO;
import com.midas.crm.entity.DTO.encuesta.OpcionRespuestaEncuestaDTO;
import com.midas.crm.entity.DTO.encuesta.PreguntaEncuestaCreateDTO;
import com.midas.crm.entity.DTO.encuesta.PreguntaEncuestaDTO;
import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.OpcionRespuestaEncuesta;
import com.midas.crm.entity.PreguntaEncuesta;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.OpcionRespuestaEncuestaMapper;
import com.midas.crm.mapper.PreguntaEncuestaMapper;
import com.midas.crm.repository.DetalleRespuestaEncuestaUsuarioRepository;
import com.midas.crm.repository.EncuestaRepository;
import com.midas.crm.repository.OpcionRespuestaEncuestaRepository;
import com.midas.crm.repository.PreguntaEncuestaRepository;
import com.midas.crm.service.PreguntaEncuestaService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PreguntaEncuestaServiceImpl implements PreguntaEncuestaService {

    private final PreguntaEncuestaRepository preguntaEncuestaRepository;
    private final OpcionRespuestaEncuestaRepository opcionRespuestaEncuestaRepository;
    private final EncuestaRepository encuestaRepository;
    private final DetalleRespuestaEncuestaUsuarioRepository detalleRespuestaEncuestaUsuarioRepository;

    @Override
    @Transactional
    public PreguntaEncuestaDTO createPregunta(PreguntaEncuestaCreateDTO dto) {
        // Obtener la encuesta
        Encuesta encuesta = encuestaRepository.findById(dto.getEncuestaId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        // Asignar orden si no se proporcionó
        if (dto.getOrden() == null) {
            int maxOrden = preguntaEncuestaRepository.findByEncuestaOrderByOrdenAsc(encuesta)
                    .stream()
                    .mapToInt(PreguntaEncuesta::getOrden)
                    .max()
                    .orElse(0);
            dto.setOrden(maxOrden + 1);
        }

        // Crear la pregunta
        PreguntaEncuesta pregunta = PreguntaEncuestaMapper.toEntity(dto, encuesta);
        pregunta = preguntaEncuestaRepository.save(pregunta);

        // Crear opciones si se proporcionaron y el tipo de pregunta lo requiere
        if (dto.getOpciones() != null && !dto.getOpciones().isEmpty() &&
                (pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.OPCION_MULTIPLE ||
                        pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.SELECCION_MULTIPLE ||
                        pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.ESCALA_LIKERT)) {

            for (int i = 0; i < dto.getOpciones().size(); i++) {
                OpcionRespuestaEncuestaCreateDTO opcionDTO = dto.getOpciones().get(i);

                // Asignar orden si no se proporcionó
                if (opcionDTO.getOrden() == null) {
                    opcionDTO.setOrden(i + 1);
                }

                // Asignar ID de pregunta
                opcionDTO.setPreguntaId(pregunta.getId());

                // Crear la opción
                opcionRespuestaEncuestaRepository.save(
                        OpcionRespuestaEncuestaMapper.toEntity(opcionDTO, pregunta));
            }
        }

        // Retornar la pregunta creada con todas sus opciones
        return getPreguntaCompleta(pregunta.getId());
    }

    @Override
    @Transactional(readOnly = true)
    public PreguntaEncuestaDTO getPreguntaById(Long id) {
        PreguntaEncuesta pregunta = preguntaEncuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_ENCUESTA_NOT_FOUND));

        return PreguntaEncuestaMapper.toDTO(pregunta);
    }

    @Override
    @Transactional(readOnly = true)
    public PreguntaEncuestaDTO getPreguntaCompleta(Long id) {
        // Obtener la pregunta
        PreguntaEncuesta pregunta = preguntaEncuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_ENCUESTA_NOT_FOUND));

        // Obtener opciones
        List<OpcionRespuestaEncuesta> opciones = opcionRespuestaEncuestaRepository
                .findByPreguntaOrderByOrdenAsc(pregunta);

        // Convertir opciones a DTOs
        List<OpcionRespuestaEncuestaDTO> opcionesDTO = opciones.stream()
                .map(OpcionRespuestaEncuestaMapper::toDTO)
                .collect(Collectors.toList());

        // Crear DTO completo
        return PreguntaEncuestaMapper.toDTOWithOpciones(pregunta, opcionesDTO);
    }

    @Override
    @Transactional
    public PreguntaEncuestaDTO updatePregunta(Long id, PreguntaEncuestaCreateDTO dto) {
        // Obtener la pregunta
        PreguntaEncuesta pregunta = preguntaEncuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_ENCUESTA_NOT_FOUND));

        // Actualizar campos básicos
        pregunta.setEnunciado(dto.getEnunciado());
        if (dto.getDescripcion() != null) {
            pregunta.setDescripcion(dto.getDescripcion());
        }
        if (dto.getOrden() != null) {
            pregunta.setOrden(dto.getOrden());
        }
        if (dto.getTipo() != null) {
            pregunta.setTipo(dto.getTipo());
        }
        if (dto.getEsObligatoria() != null) {
            pregunta.setEsObligatoria(dto.getEsObligatoria());
        }

        // Guardar cambios
        pregunta = preguntaEncuestaRepository.save(pregunta);

        // Actualizar opciones si se proporcionaron y el tipo de pregunta lo requiere
        if (dto.getOpciones() != null && !dto.getOpciones().isEmpty() &&
                (pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.OPCION_MULTIPLE ||
                        pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.SELECCION_MULTIPLE ||
                        pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.ESCALA_LIKERT)) {

            // Eliminar opciones existentes
            List<OpcionRespuestaEncuesta> opcionesExistentes = opcionRespuestaEncuestaRepository
                    .findByPreguntaOrderByOrdenAsc(pregunta);
            opcionRespuestaEncuestaRepository.deleteAll(opcionesExistentes);

            // Crear nuevas opciones
            for (int i = 0; i < dto.getOpciones().size(); i++) {
                OpcionRespuestaEncuestaCreateDTO opcionDTO = dto.getOpciones().get(i);

                // Asignar orden si no se proporcionó
                if (opcionDTO.getOrden() == null) {
                    opcionDTO.setOrden(i + 1);
                }

                // Asignar ID de pregunta
                opcionDTO.setPreguntaId(pregunta.getId());

                // Crear la opción
                opcionRespuestaEncuestaRepository.save(
                        OpcionRespuestaEncuestaMapper.toEntity(opcionDTO, pregunta));
            }
        }

        // Retornar la pregunta actualizada con todas sus opciones
        return getPreguntaCompleta(pregunta.getId());
    }

    @Override
    @Transactional
    public void deletePregunta(Long id) {
        // Obtener la pregunta
        PreguntaEncuesta pregunta = preguntaEncuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_ENCUESTA_NOT_FOUND));

        // Cambiar estado a inactivo
        pregunta.setEstado("I");
        preguntaEncuestaRepository.save(pregunta);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PreguntaEncuestaDTO> getPreguntasByEncuestaId(Long encuestaId) {
        // Verificar que la encuesta existe
        if (!encuestaRepository.existsById(encuestaId)) {
            throw new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND);
        }

        // Obtener preguntas
        List<PreguntaEncuesta> preguntas = preguntaEncuestaRepository.findByEncuestaIdOrderByOrdenAsc(encuestaId);

        // Convertir a DTOs con sus opciones
        return preguntas.stream()
                .map(pregunta -> {
                    // Obtener opciones para cada pregunta
                    List<OpcionRespuestaEncuesta> opciones = opcionRespuestaEncuestaRepository
                            .findByPreguntaOrderByOrdenAsc(pregunta);
                    List<OpcionRespuestaEncuestaDTO> opcionesDTO = opciones.stream()
                            .map(OpcionRespuestaEncuestaMapper::toDTO)
                            .collect(Collectors.toList());

                    return PreguntaEncuestaMapper.toDTOWithOpciones(pregunta, opcionesDTO);
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getEstadisticasPregunta(Long preguntaId) {
        // Verificar que la pregunta existe
        PreguntaEncuesta pregunta = preguntaEncuestaRepository.findById(preguntaId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_ENCUESTA_NOT_FOUND));

        Map<String, Object> estadisticas = new HashMap<>();

        // Estadísticas según el tipo de pregunta
        switch (pregunta.getTipo()) {
            case OPCION_MULTIPLE:
            case SELECCION_MULTIPLE:
            case ESCALA_LIKERT:
                // Obtener opciones
                List<OpcionRespuestaEncuesta> opciones = opcionRespuestaEncuestaRepository
                        .findByPreguntaOrderByOrdenAsc(pregunta);

                // Obtener conteo de respuestas por opción
                List<Map<String, Object>> estadisticasOpciones = detalleRespuestaEncuestaUsuarioRepository
                        .getEstadisticasPorPregunta(preguntaId);

                // Crear mapa de opciones con sus estadísticas
                List<Map<String, Object>> opcionesConEstadisticas = opciones.stream()
                        .map(opcion -> {
                            Map<String, Object> opcionMap = new HashMap<>();
                            opcionMap.put("id", opcion.getId());
                            opcionMap.put("texto", opcion.getTexto());
                            opcionMap.put("orden", opcion.getOrden());

                            // Buscar estadísticas para esta opción
                            Long cantidad = estadisticasOpciones.stream()
                                    .filter(e -> opcion.getId().equals(e.get("opcionId")))
                                    .map(e -> (Long) e.get("cantidad"))
                                    .findFirst()
                                    .orElse(0L);

                            opcionMap.put("cantidad", cantidad);

                            return opcionMap;
                        })
                        .collect(Collectors.toList());

                estadisticas.put("opciones", opcionesConEstadisticas);
                break;

            case NUMERO:
                // Obtener promedio de respuestas numéricas
                Double promedio = detalleRespuestaEncuestaUsuarioRepository
                        .getPromedioRespuestasNumericasPorPregunta(preguntaId);
                estadisticas.put("promedio", promedio != null ? promedio : 0);
                break;

            case TEXTO_LIBRE:
            case FECHA:
                // Para estos tipos solo se cuenta el número de respuestas
                break;
        }

        return estadisticas;
    }

    @Override
    @Transactional
    public List<PreguntaEncuestaDTO> reordenarPreguntas(Long encuestaId, List<Long> nuevosOrdenes) {
        // Verificar que la encuesta existe
        if (!encuestaRepository.existsById(encuestaId)) {
            throw new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND);
        }

        // Obtener preguntas
        List<PreguntaEncuesta> preguntas = preguntaEncuestaRepository.findByEncuestaIdOrderByOrdenAsc(encuestaId);

        // Verificar que la cantidad de preguntas coincide con la cantidad de órdenes
        if (preguntas.size() != nuevosOrdenes.size()) {
            throw new MidasExceptions(MidasErrorMessage.PREGUNTA_ENCUESTA_INVALID_DATA);
        }

        // Actualizar órdenes
        for (int i = 0; i < nuevosOrdenes.size(); i++) {
            Long preguntaId = nuevosOrdenes.get(i);
            PreguntaEncuesta pregunta = preguntas.stream()
                    .filter(p -> p.getId().equals(preguntaId))
                    .findFirst()
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_ENCUESTA_NOT_FOUND));

            pregunta.setOrden(i + 1);
            preguntaEncuestaRepository.save(pregunta);
        }

        // Retornar preguntas reordenadas
        return getPreguntasByEncuestaId(encuestaId);
    }
}
