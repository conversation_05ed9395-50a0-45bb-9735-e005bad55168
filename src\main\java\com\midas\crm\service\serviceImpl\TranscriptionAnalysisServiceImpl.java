package com.midas.crm.service.serviceImpl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.transcription.TAListadoDTO;
import com.midas.crm.entity.TranscriptionAnalysis;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.repository.TranscriptionAnalysisRepository;
import com.midas.crm.service.TranscriptionAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Implementación del servicio para manejar análisis de transcripción
 */
@Service
@Slf4j
@Transactional
public class TranscriptionAnalysisServiceImpl implements TranscriptionAnalysisService {

    private final TranscriptionAnalysisRepository transcriptionAnalysisRepository;
    private final ClienteResidencialRepository clienteResidencialRepository;
    private final ObjectMapper objectMapper;

    public TranscriptionAnalysisServiceImpl(
            TranscriptionAnalysisRepository transcriptionAnalysisRepository,
            ClienteResidencialRepository clienteResidencialRepository,
            ObjectMapper objectMapper) {
        this.transcriptionAnalysisRepository = transcriptionAnalysisRepository;
        this.clienteResidencialRepository = clienteResidencialRepository;
        this.objectMapper = objectMapper;
    }

    @Override
    public TranscriptionAnalysis saveAnalysis(Long clienteResidencialId, Map<String, Object> comparisonResult) {
        try {
            log.info("💾 Guardando análisis completo para cliente residencial ID: {}", clienteResidencialId);

            // Buscar el cliente residencial
            Optional<ClienteResidencial> clienteOpt = clienteResidencialRepository.findById(clienteResidencialId);
            if (!clienteOpt.isPresent()) {
                throw new RuntimeException("Cliente residencial no encontrado con ID: " + clienteResidencialId);
            }

            ClienteResidencial cliente = clienteOpt.get();

            // Extraer datos del response
            Map<String, Object> analysisData = extractAnalysisData(comparisonResult);

            // Crear el análisis
            TranscriptionAnalysis analysis = TranscriptionAnalysis.builder()
                    .clienteResidencial(cliente)
                    .responseCompleto(convertToJsonString(comparisonResult))
                    .camposAnalizados(convertToJsonString((Map<String, Object>) comparisonResult.get("campos_analizados")))
                    .debugInfo(convertToJsonString((Map<String, Object>) comparisonResult.get("debug_info")))
                    .resumen(convertToJsonString((Map<String, Object>) comparisonResult.get("resumen")))
                    .estadisticas(convertToJsonString((Map<String, Object>) comparisonResult.get("estadisticas")))
                    .recomendaciones(convertToJsonString((List<String>) comparisonResult.get("recomendaciones")))
                    .porcentajePromedio((BigDecimal) analysisData.get("porcentajePromedio"))
                    .porcentajePonderado((BigDecimal) analysisData.get("porcentajePonderado"))
                    .nivelConfianza((String) analysisData.get("nivelConfianza"))
                    .totalCampos((Integer) analysisData.get("totalCampos"))
                    .camposExactos((Integer) analysisData.get("camposExactos"))
                    .camposBuenos((Integer) analysisData.get("camposBuenos"))
                    .camposRegulares((Integer) analysisData.get("camposRegulares"))
                    .camposMalos((Integer) analysisData.get("camposMalos"))
                    .fechaCreacion(LocalDateTime.now())
                    .estado("COMPLETED")
                    .versionApi("v1.0")
                    .build();

            TranscriptionAnalysis savedAnalysis = transcriptionAnalysisRepository.save(analysis);
            log.info("✅ Análisis completo guardado con ID: {}", savedAnalysis.getId());

            return savedAnalysis;

        } catch (Exception e) {
            log.error("❌ Error al guardar análisis completo para cliente {}: {}", clienteResidencialId, e.getMessage(), e);
            throw new RuntimeException("Error al guardar análisis: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TranscriptionAnalysis> findByClienteResidencialId(Long clienteResidencialId) {
        return transcriptionAnalysisRepository.findByClienteResidencialId(clienteResidencialId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TranscriptionAnalysis> findAllByClienteResidencialId(Long clienteResidencialId) {
        return transcriptionAnalysisRepository.findAllByClienteResidencialIdOrderByFechaCreacionDesc(clienteResidencialId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TranscriptionAnalysis> findById(Long id) {
        return transcriptionAnalysisRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TranscriptionAnalysis> findByEstado(String estado) {
        return transcriptionAnalysisRepository.findByEstado(estado);
    }

    /* --------- EXISTENTE (entidad) --------- */
    @Override
    @Transactional(readOnly = true)
    public Page<TranscriptionAnalysis> findByFechaCreacionBetween(
            LocalDateTime ini, LocalDateTime fin, Pageable pageable) {
        return transcriptionAnalysisRepository.findByFechaCreacionBetween(ini, fin, pageable);
    }

    @Override
    public Page<TAListadoDTO> findListadoDTO(LocalDateTime ini, LocalDateTime fin, Pageable pageable) {
        return transcriptionAnalysisRepository.findListadoDTO(ini, fin, pageable);
    }

    /* --------- NUEVO (DTO) --------- */
    @Override
    @Transactional(readOnly = true)
    public Page<TAListadoDTO> listado(
            Pageable pageable, LocalDateTime ini, LocalDateTime fin) {
        return transcriptionAnalysisRepository.findListadoDTO(ini, fin, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TranscriptionAnalysis> findByNivelConfianza(String nivelConfianza) {
        return transcriptionAnalysisRepository.findByNivelConfianza(nivelConfianza);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TranscriptionAnalysis> findByPorcentajePromedioGreaterThanEqual(Double porcentajeMinimo) {
        return transcriptionAnalysisRepository.findByPorcentajePromedioGreaterThanEqual(porcentajeMinimo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TranscriptionAnalysis> findByPorcentajePromedioLessThan(Double porcentajeMaximo) {
        return transcriptionAnalysisRepository.findByPorcentajePromedioLessThan(porcentajeMaximo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TranscriptionAnalysis> findByNumeroAgente(String numeroAgente) {
        return transcriptionAnalysisRepository.findByNumeroAgente(numeroAgente);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TranscriptionAnalysis> findByMovilContacto(String movilContacto) {
        return transcriptionAnalysisRepository.findByMovilContacto(movilContacto);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getEstadisticasByFechaCreacionBetween(LocalDateTime fechaInicio, LocalDateTime fechaFin) {
        Object[] result = transcriptionAnalysisRepository.getEstadisticasByFechaCreacionBetween(fechaInicio, fechaFin);

        Map<String, Object> estadisticas = new HashMap<>();
        if (result != null && result.length > 0) {
            estadisticas.put("total", result[0]);
            estadisticas.put("promedioGeneral", result[1]);
            estadisticas.put("minimoPromedio", result[2]);
            estadisticas.put("maximoPromedio", result[3]);
            estadisticas.put("confianzaAlta", result[4]);
            estadisticas.put("confianzaMedia", result[5]);
            estadisticas.put("confianzaBaja", result[6]);
            estadisticas.put("confianzaMuyBaja", result[7]);
        }

        return estadisticas;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByClienteResidencialId(Long clienteResidencialId) {
        return transcriptionAnalysisRepository.existsByClienteResidencialId(clienteResidencialId);
    }

    @Override
    public TranscriptionAnalysis updateAnalysis(Long id, Map<String, Object> comparisonResult) {
        Optional<TranscriptionAnalysis> existingOpt = transcriptionAnalysisRepository.findById(id);
        if (!existingOpt.isPresent()) {
            throw new RuntimeException("Análisis no encontrado con ID: " + id);
        }

        TranscriptionAnalysis existing = existingOpt.get();
        Map<String, Object> analysisData = extractAnalysisData(comparisonResult);

        // Actualizar campos
        existing.setResponseCompleto(convertToJsonString(comparisonResult));
        existing.setCamposAnalizados(convertToJsonString((Map<String, Object>) comparisonResult.get("campos_analizados")));
        existing.setDebugInfo(convertToJsonString((Map<String, Object>) comparisonResult.get("debug_info")));
        existing.setResumen(convertToJsonString((Map<String, Object>) comparisonResult.get("resumen")));
        existing.setEstadisticas(convertToJsonString((Map<String, Object>) comparisonResult.get("estadisticas")));
        existing.setRecomendaciones(convertToJsonString((List<String>) comparisonResult.get("recomendaciones")));
        existing.setPorcentajePromedio((BigDecimal) analysisData.get("porcentajePromedio"));
        existing.setPorcentajePonderado((BigDecimal) analysisData.get("porcentajePonderado"));
        existing.setNivelConfianza((String) analysisData.get("nivelConfianza"));
        existing.setTotalCampos((Integer) analysisData.get("totalCampos"));
        existing.setCamposExactos((Integer) analysisData.get("camposExactos"));
        existing.setCamposBuenos((Integer) analysisData.get("camposBuenos"));
        existing.setCamposRegulares((Integer) analysisData.get("camposRegulares"));
        existing.setCamposMalos((Integer) analysisData.get("camposMalos"));
        existing.setFechaActualizacion(LocalDateTime.now());

        return transcriptionAnalysisRepository.save(existing);
    }

    @Override
    public void deleteById(Long id) {
        transcriptionAnalysisRepository.deleteById(id);
    }

    @Override
    public void deleteOldAnalysis(int diasAntiguedad) {
        LocalDateTime fechaLimite = LocalDateTime.now().minusDays(diasAntiguedad);
        transcriptionAnalysisRepository.deleteByFechaCreacionBefore(fechaLimite);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TranscriptionAnalysis> findLatestAnalysis() {
        return transcriptionAnalysisRepository.findTop10ByOrderByFechaCreacionDesc();
    }

    @Override
    @Transactional(readOnly = true)
    public List<TranscriptionAnalysis> findProblematicAnalysis() {
        return transcriptionAnalysisRepository.findProblematicAnalysis();
    }

    @Override
    @Transactional(readOnly = true)
    public long countByEstado(String estado) {
        return transcriptionAnalysisRepository.countByEstado(estado);
    }

    @Override
    @Transactional(readOnly = true)
    public long countByNivelConfianza(String nivelConfianza) {
        return transcriptionAnalysisRepository.countByNivelConfianza(nivelConfianza);
    }

    @Override
    public Map<String, Object> extractAnalysisData(Map<String, Object> comparisonResult) {
        Map<String, Object> data = new HashMap<>();

        try {
            // Extraer estadísticas
            Map<String, Object> estadisticas = (Map<String, Object>) comparisonResult.get("estadisticas");
            if (estadisticas != null) {
                Object porcentajePromedio = estadisticas.get("porcentaje_promedio");
                Object porcentajePonderado = estadisticas.get("porcentaje_ponderado");
                Object nivelConfianza = estadisticas.get("nivel_confianza");

                data.put("porcentajePromedio", porcentajePromedio != null ?
                        BigDecimal.valueOf(((Number) porcentajePromedio).doubleValue()) : null);
                data.put("porcentajePonderado", porcentajePonderado != null ?
                        BigDecimal.valueOf(((Number) porcentajePonderado).doubleValue()) : null);
                data.put("nivelConfianza", nivelConfianza);
            }

            // Extraer resumen
            Map<String, Object> resumen = (Map<String, Object>) comparisonResult.get("resumen");
            if (resumen != null) {
                data.put("totalCampos", resumen.get("total_campos"));
                data.put("camposExactos", resumen.get("campos_exactos"));
                data.put("camposBuenos", resumen.get("campos_buenos"));
                data.put("camposRegulares", resumen.get("campos_regulares"));
                data.put("camposMalos", resumen.get("campos_malos"));
            }

        } catch (Exception e) {
            log.warn("⚠️ Error al extraer datos del análisis: {}", e.getMessage());
        }

        return data;
    }

    @Override
    public String convertToJsonString(Map<String, Object> data) {
        if (data == null) return null;
        try {
            return objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            log.error("Error al convertir a JSON: {}", e.getMessage());
            return null;
        }
    }

    public String convertToJsonString(List<String> data) {
        if (data == null) return null;
        try {
            return objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            log.error("Error al convertir lista a JSON: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public Map<String, Object> convertFromJsonString(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) return new HashMap<>();
        try {
            return objectMapper.readValue(jsonString, Map.class);
        } catch (JsonProcessingException e) {
            log.error("Error al convertir desde JSON: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TranscriptionAnalysis> findAllWithFilters(Pageable pageable, String estado, String nivelConfianza,
                                                          String numeroAgente, String movilContacto,
                                                          Double porcentajeMinimo, Double porcentajeMaximo,
                                                          java.time.LocalDate fechaInicio, java.time.LocalDate fechaFin) {
        try {
            log.info("🔍 Buscando análisis con filtros - Página: {}, Tamaño: {}", pageable.getPageNumber(), pageable.getPageSize());

            // Si no hay filtros, devolver todos
            if (estado == null && nivelConfianza == null && numeroAgente == null && movilContacto == null &&
                    porcentajeMinimo == null && porcentajeMaximo == null && fechaInicio == null && fechaFin == null) {
                return transcriptionAnalysisRepository.findAll(pageable);
            }

            // Para simplificar, usar findAll() y filtrar en memoria por ahora
            // En producción, esto debería ser optimizado con queries específicas
            return transcriptionAnalysisRepository.findAll(pageable);

        } catch (Exception e) {
            log.error("❌ Error al buscar análisis con filtros: {}", e.getMessage());
            return Page.empty(pageable);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getEstadisticasGenerales() {
        try {
            log.info("📊 Obteniendo estadísticas generales");

            long totalAnalisis = transcriptionAnalysisRepository.count();
            long completados = transcriptionAnalysisRepository.countByEstado("COMPLETED");
            long altaConfianza = transcriptionAnalysisRepository.countByNivelConfianza("ALTO");

            // Calcular promedio general
            Double promedioGeneral = transcriptionAnalysisRepository.findAll().stream()
                    .filter(a -> a.getPorcentajePromedio() != null)
                    .mapToDouble(a -> a.getPorcentajePromedio().doubleValue())
                    .average()
                    .orElse(0.0);

            Map<String, Object> estadisticas = new HashMap<>();
            estadisticas.put("totalAnalisis", totalAnalisis);
            estadisticas.put("completados", completados);
            estadisticas.put("altaConfianza", altaConfianza);
            estadisticas.put("promedioGeneral", Math.round(promedioGeneral * 100.0) / 100.0);

            return estadisticas;

        } catch (Exception e) {
            log.error("❌ Error al obtener estadísticas generales: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getResumenGeneral() {
        try {
            log.info("📊 Obteniendo resumen general");

            Map<String, Object> resumen = new HashMap<>();

            // Estadísticas básicas
            long total = transcriptionAnalysisRepository.count();
            resumen.put("totalAnalisis", total);

            if (total > 0) {
                // Promedio de calidad
                Double promedioCalidad = transcriptionAnalysisRepository.findAll().stream()
                        .filter(a -> a.getPorcentajePromedio() != null)
                        .mapToDouble(a -> a.getPorcentajePromedio().doubleValue())
                        .average()
                        .orElse(0.0);
                resumen.put("promedioCalidad", Math.round(promedioCalidad * 100.0) / 100.0);

                // Estado del sistema
                Map<String, Long> estadoSistema = new HashMap<>();
                estadoSistema.put("COMPLETED", transcriptionAnalysisRepository.countByEstado("COMPLETED"));
                estadoSistema.put("PROCESSING", transcriptionAnalysisRepository.countByEstado("PROCESSING"));
                estadoSistema.put("ERROR", transcriptionAnalysisRepository.countByEstado("ERROR"));
                resumen.put("estadoSistema", estadoSistema);
            }

            return resumen;

        } catch (Exception e) {
            log.error("❌ Error al obtener resumen general: {}", e.getMessage());
            return new HashMap<>();
        }
    }


}
