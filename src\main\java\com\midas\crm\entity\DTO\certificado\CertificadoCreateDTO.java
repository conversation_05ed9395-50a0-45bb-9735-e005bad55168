package com.midas.crm.entity.DTO.certificado;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CertificadoCreateDTO {
    private Long usuarioId;
    private Long cursoId;
    private LocalDateTime fechaEmision;
    private Integer horasCurso;
    private String observaciones;
    private String certificadoUrl;
}
