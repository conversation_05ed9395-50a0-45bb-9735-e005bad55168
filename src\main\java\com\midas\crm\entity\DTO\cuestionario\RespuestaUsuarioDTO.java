package com.midas.crm.entity.DTO.cuestionario;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RespuestaUsuarioDTO {
    private Long id;
    private Long usuarioId;
    private String usuarioNombre;
    private Long cuestionarioId;
    private String cuestionarioTitulo;
    private LocalDateTime fechaInicio;
    private LocalDateTime fechaFin;
    private Integer puntajeObtenido;
    private Integer porcentajeAprobacion;
    private Boolean completado;
    private Boolean aprobado;
    private Integer numeroIntento;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
    private List<DetalleRespuestaUsuarioDTO> detallesRespuestas;
}
