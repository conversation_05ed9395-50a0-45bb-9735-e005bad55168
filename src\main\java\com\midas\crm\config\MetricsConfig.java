package com.midas.crm.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.config.MeterFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuración personalizada de métricas para excluir WebSocket endpoints
 * y optimizar el rendimiento de las métricas
 */
@Configuration
@Slf4j
public class MetricsConfig {

    /**
     * Personaliza el registro de métricas para filtrar endpoints problemáticos
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> {
            // Agregar tags comunes
            registry.config().commonTags("application", "crm-leads");
            
            // Filtrar métricas de WebSocket que causan problemas
            registry.config().meterFilter(
                MeterFilter.deny(id -> {
                    String name = id.getName();
                    // Excluir métricas de WebSocket que se cuentan incorrectamente como HTTP
                    if ("http.server.requests".equals(name) || "http_server_requests".equals(name)) {
                        String uri = id.getTag("uri");
                        if (uri != null) {
                            // Excluir endpoints WebSocket
                            if (uri.startsWith("/ws") || 
                                uri.contains("websocket") || 
                                uri.contains("sockjs") ||
                                uri.equals("UNKNOWN")) {
                                return true;
                            }
                        }
                    }
                    return false;
                })
            );
            
            // Filtrar métricas de actuator innecesarias
            registry.config().meterFilter(
                MeterFilter.deny(id -> {
                    String name = id.getName();
                    if ("http.server.requests".equals(name) || "http_server_requests".equals(name)) {
                        String uri = id.getTag("uri");
                        if (uri != null && uri.startsWith("/actuator/")) {
                            // Solo mantener métricas de health y prometheus
                            return !uri.equals("/actuator/health") && !uri.equals("/actuator/prometheus");
                        }
                    }
                    return false;
                })
            );
            
            // Limitar el número de tags únicos para evitar explosión de métricas
            registry.config().meterFilter(
                MeterFilter.maximumAllowableTags("http.server.requests", "uri", 100, 
                    MeterFilter.deny())
            );
            
            registry.config().meterFilter(
                MeterFilter.maximumAllowableTags("http_server_requests", "uri", 100, 
                    MeterFilter.deny())
            );
            
        };
    }

    /**
     * Configuración adicional para métricas de JVM y sistema
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> jvmMetricsCustomizer() {
        return registry -> {
            // Configurar métricas de JVM con tags adicionales
            registry.config().commonTags(
                "service", "crm-backend",
                "environment", "production"
            );
            
        };
    }
}
