package com.midas.crm.entity.DTO.cuestionario;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RespuestaDTO {
    private Long id;
    private String texto;
    private Boolean esCorrecta;
    private Integer orden;
    private Long preguntaId;
    private String estado;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
}
