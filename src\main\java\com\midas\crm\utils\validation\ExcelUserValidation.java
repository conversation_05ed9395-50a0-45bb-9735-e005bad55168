package com.midas.crm.utils.validation;

import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.utils.MidasErrorMessage;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class ExcelUserValidation {

    public static List<String> validateExcelUser(User user, int rowNumber) {
        List<String> errors = new ArrayList<>();

        // Validar DNI
        if (!StringUtils.hasText(user.getDni()) || user.getDni().length() != 8) {
            errors.add("Fila " + rowNumber + ": El DNI debe tener exactamente 8 caracteres");
        }

        // Validar username
        if (!StringUtils.hasText(user.getUsername()) || user.getUsername().length() < 3) {
            errors.add("Fila " + rowNumber + ": El nombre de usuario debe tener al menos 3 caracteres");
        }

        // Validar nombre
        if (!StringUtils.hasText(user.getNombre()) || user.getNombre().length() < 2) {
            errors.add("Fila " + rowNumber + ": El nombre debe tener al menos 2 caracteres");
        }

        // Validar apellido
        if (!StringUtils.hasText(user.getApellido()) || user.getApellido().length() < 2) {
            errors.add("Fila " + rowNumber + ": El apellido debe tener al menos 2 caracteres");
        }

        // Validar contraseña
        if (!StringUtils.hasText(user.getPassword()) || user.getPassword().length() < 6) {
            errors.add("Fila " + rowNumber + ": La contraseña debe tener al menos 6 caracteres");
        }

        // Validar email si está presente
        if (StringUtils.hasText(user.getEmail()) && !user.getEmail().matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
            errors.add("Fila " + rowNumber + ": El formato del email no es válido");
        }

        // Validar teléfono si está presente
        if (StringUtils.hasText(user.getTelefono()) && !user.getTelefono().matches("^[0-9]{9,15}$")) {
            errors.add("Fila " + rowNumber + ": El teléfono debe contener solo números y tener entre 9 y 15 dígitos");
        }

        // Validar sede (ID)
        if (user.getSede() == null || user.getSede().getId() == null) {
            errors.add("Fila " + rowNumber + ": El ID de la sede es obligatorio");
        }

        return errors;
    }
}