package com.midas.crm.service;

import com.midas.crm.entity.DTO.cuestionario.RespuestaCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUpdateDTO;

import java.util.List;

public interface RespuestaService {
    RespuestaDTO createRespuesta(RespuestaCreateDTO dto);
    List<RespuestaDTO> listRespuestas();
    List<RespuestaDTO> listRespuestasByPreguntaId(Long preguntaId);
    RespuestaDTO getRespuestaById(Long id);
    RespuestaDTO updateRespuesta(Long id, RespuestaUpdateDTO dto);
    void deleteRespuesta(Long id);
}
