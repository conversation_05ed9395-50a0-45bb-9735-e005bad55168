package com.midas.crm.repository;

import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.DTO.EstadisticaSedeDTO;
import com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionCoordinadorDTO;
import com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ClienteResidencialRepository extends JpaRepository<ClienteResidencial, Long> {

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) "
                +
                "FROM ClienteResidencial cr JOIN cr.usuario u LEFT JOIN u.coordinador c")
        Page<ClienteConUsuarioDTO> obtenerClientesConUsuario(Pageable pageable);

        // Consulta para cargar el cliente con usuario y coordinador de forma ansiosa
        // (EAGER)
        @Query("SELECT c FROM ClienteResidencial c JOIN FETCH c.usuario u LEFT JOIN FETCH u.coordinador WHERE c.movilContacto = :movilContacto")
        List<ClienteResidencial> findByMovilContacto(@Param("movilContacto") String movilContacto);

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) "
                +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "WHERE (:dniAsesor IS NULL OR :dniAsesor = '' OR u.dni = :dniAsesor) " +
                "AND (COALESCE(:nombreAsesor, '') = '' OR CONCAT(u.nombre, ' ', u.apellido) LIKE CONCAT('%', :nombreAsesor, '%')) "
                +
                "AND (:numeroMovil IS NULL OR :numeroMovil = '' OR cr.movilContacto = :numeroMovil) " +
                "AND (:fecha IS NULL OR DATE(cr.fechaCreacion) = :fecha)")
        Page<ClienteConUsuarioDTO> obtenerClientesConUsuarioFiltrados(
                @Param("dniAsesor") String dniAsesor,
                @Param("nombreAsesor") String nombreAsesor,
                @Param("numeroMovil") String numeroMovil,
                @Param("fecha") LocalDate fecha,
                Pageable pageable);

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) "
                +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "WHERE (:dniAsesor IS NULL OR :dniAsesor = '' OR u.dni = :dniAsesor) " +
                "AND (COALESCE(:nombreAsesor, '') = '' OR CONCAT(u.nombre, ' ', u.apellido) LIKE CONCAT('%', :nombreAsesor, '%')) "
                +
                "AND (:numeroMovil IS NULL OR :numeroMovil = '' OR cr.movilContacto = :numeroMovil) " +
                "AND DATE(cr.fechaCreacion) = CURRENT_DATE")
        Page<ClienteConUsuarioDTO> obtenerClientesConUsuarioFiltradosPorFechaActual(
                @Param("dniAsesor") String dniAsesor,
                @Param("nombreAsesor") String nombreAsesor,
                @Param("numeroMovil") String numeroMovil,
                Pageable pageable);

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) "
                +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "WHERE (:dniAsesor IS NULL OR :dniAsesor = '' OR u.dni = :dniAsesor) " +
                "AND (COALESCE(:nombreAsesor, '') = '' OR " +
                "REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ') LIKE CONCAT('%', REPLACE(TRIM(:nombreAsesor), '  ', ' '), '%')) "
                +
                "AND (:numeroMovil IS NULL OR :numeroMovil = '' OR cr.movilContacto = :numeroMovil) " +
                "AND DATE(cr.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin")
        Page<ClienteConUsuarioDTO> obtenerClientesConUsuarioFiltradosPorRango(
                @Param("dniAsesor") String dniAsesor,
                @Param("nombreAsesor") String nombreAsesor,
                @Param("numeroMovil") String numeroMovil,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable);

        List<ClienteResidencial> findByFechaCreacionBetween(LocalDateTime start, LocalDateTime end);

        /**
         * Encuentra clientes por fecha de creación entre dos fechas con soporte para
         * paginación
         *
         * @param start    Fecha de inicio
         * @param end      Fecha de fin
         * @param pageable Objeto de paginación
         * @return Página de clientes residenciales
         */
        Page<ClienteResidencial> findByFechaCreacionBetween(LocalDateTime start, LocalDateTime end, Pageable pageable);

        /**
         * Encuentra todos los clientes residenciales asociados a un usuario (asesor)
         * específico
         *
         * @param usuarioId ID del usuario (asesor)
         * @return Lista de clientes residenciales
         */
        List<ClienteResidencial> findByUsuarioId(Long usuarioId);

        /**
         * Cuenta el número de clientes residenciales asociados a un usuario (asesor)
         * específico
         *
         * @param usuarioId ID del usuario (asesor)
         * @return Número de clientes
         */
        @Query("SELECT COUNT(c) FROM ClienteResidencial c WHERE c.usuario.id = :usuarioId")
        Long countClientesByUsuarioId(@Param("usuarioId") Long usuarioId);

        /**
         * Encuentra todos los clientes residenciales con venta realizada asociados a un
         * usuario (asesor)
         *
         * @param usuarioId ID del usuario (asesor)
         * @return Lista de clientes con venta realizada
         */
        @Query("SELECT c FROM ClienteResidencial c WHERE c.usuario.id = :usuarioId AND c.ventaRealizada = true")
        List<ClienteResidencial> findVentasRealizadasByUsuarioId(@Param("usuarioId") Long usuarioId);

        /**
         * Reemplaza el método buscarPorDniMovilYFechaEntre en tu
         * ClienteResidencialRepository
         * Ahora también permite buscar por nombre completo del usuario
         * CORREGIDO: Agregado LEFT JOIN FETCH u.coordinador para evitar LazyInitializationException
         */
        @Query("SELECT DISTINCT c FROM ClienteResidencial c " +
                "JOIN FETCH c.usuario u " +
                "LEFT JOIN FETCH u.coordinador " +
                "LEFT JOIN FETCH c.movilesAPortar " +
                "WHERE (u.dni = :dni OR (:nombreCompleto IS NOT NULL AND :nombreCompleto != '' AND CONCAT(u.nombre, ' ', u.apellido) LIKE CONCAT('%', :nombreCompleto, '%'))) "
                +
                "AND c.movilContacto = :movil " +
                "AND c.fechaCreacion BETWEEN :inicio AND :fin")
        List<ClienteResidencial> buscarPorDniMovilYFechaEntre(
                @Param("dni") String dni,
                @Param("nombreCompleto") String nombreCompleto,
                @Param("movil") String movil,
                @Param("inicio") LocalDateTime inicio,
                @Param("fin") LocalDateTime fin);

        @Query("""
                            SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(
                                c.usuario.dni,
                                CONCAT(c.usuario.nombre, ' ', c.usuario.apellido),
                                c.fechaCreacion,
                                c.movilContacto,
                                CASE WHEN c.usuario.coordinador IS NOT NULL THEN CONCAT(c.usuario.coordinador.nombre, ' ', c.usuario.coordinador.apellido) ELSE '' END
                            )
                            FROM ClienteResidencial c
                            LEFT JOIN c.usuario.coordinador coord
                            WHERE DATE(c.fechaCreacion) = :fecha
                            AND c.usuario.id IN :idsAsesores
                        """)
        List<ClienteConUsuarioDTO> findClientesConUsuarioPorFechaYAsesores(
                @Param("fecha") LocalDate fecha,
                @Param("idsAsesores") List<Long> idsAsesores);

        List<ClienteResidencial> findByMovilContactoIn(List<String> moviles);

        // ===== MÉTODOS PARA COLAS DE TRANSCRIPCIÓN =====

        /**
         * Cuenta clientes que tienen transcripción
         */
        @Query("SELECT COUNT(c) FROM ClienteResidencial c WHERE c.textoTranscription IS NOT NULL AND c.textoTranscription != ''")
        long countByTextoTranscriptionIsNotNull();

        /**
         * Cuenta clientes que tienen observaciones/notas
         */
        @Query("SELECT COUNT(c) FROM ClienteResidencial c WHERE c.observacion IS NOT NULL AND c.observacion != ''")
        long countByObservacionIsNotNull();

        /**
         * Cuenta clientes que tienen notas de IA del comparador
         */
        @Query("SELECT COUNT(c) FROM ClienteResidencial c WHERE c.notaAgenteComparadorIA IS NOT NULL")
        long countByNotaAgenteComparadorIAIsNotNull();

        /**
         * Obtiene estadísticas de transcripción por agente
         */
        @Query("SELECT c.numeroAgente, COUNT(c), " +
                "SUM(CASE WHEN c.textoTranscription IS NOT NULL AND c.textoTranscription != '' THEN 1 ELSE 0 END) " +
                "FROM ClienteResidencial c " +
                "WHERE c.numeroAgente IS NOT NULL " +
                "GROUP BY c.numeroAgente")
        List<Object[]> getTranscriptionStatsByAgent();

        /**
         * Busca leads sin transcripción con límite usando consulta nativa
         */
        @Query(value = "SELECT * FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND (c.texto_transcription IS NULL OR c.texto_transcription = '') " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?1", nativeQuery = true)
        List<ClienteResidencial> findLeadsWithoutTranscription(int limit);

        /**
         * Busca leads sin transcripción filtrados por agente normalizado usando consulta nativa
         */
        @Query(value = "SELECT * FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND (c.texto_transcription IS NULL OR c.texto_transcription = '') " +
                "AND (CASE " +
                "    WHEN LOWER(c.numero_agente) LIKE 'agen%' THEN TRIM(LEADING '0' FROM SUBSTRING(LOWER(c.numero_agente), 5)) " +
                "    ELSE TRIM(LEADING '0' FROM c.numero_agente) " +
                "END) = ?1 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?2", nativeQuery = true)
        List<ClienteResidencial> findLeadsWithoutTranscriptionByAgent(String normalizedAgent, int limit);

        /**
         * Busca leads sin transcripción del mes actual usando consulta nativa
         */
        @Query(value = "SELECT * FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND (c.texto_transcription IS NULL OR c.texto_transcription = '') " +
                "AND YEAR(c.fecha_creacion) = YEAR(CURDATE()) " +
                "AND MONTH(c.fecha_creacion) = MONTH(CURDATE()) " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?1", nativeQuery = true)
        List<ClienteResidencial> findLeadsWithoutTranscriptionCurrentMonth(int limit);

        /**
         * Busca leads sin transcripción del mes actual filtrados por agente normalizado
         */
        @Query(value = "SELECT * FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND (c.texto_transcription IS NULL OR c.texto_transcription = '') " +
                "AND YEAR(c.fecha_creacion) = YEAR(CURDATE()) " +
                "AND MONTH(c.fecha_creacion) = MONTH(CURDATE()) " +
                "AND (CASE " +
                "    WHEN LOWER(c.numero_agente) LIKE 'agen%' THEN TRIM(LEADING '0' FROM SUBSTRING(LOWER(c.numero_agente), 5)) " +
                "    ELSE TRIM(LEADING '0' FROM c.numero_agente) " +
                "END) = ?1 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?2", nativeQuery = true)
        List<ClienteResidencial> findLeadsWithoutTranscriptionCurrentMonthByAgent(String normalizedAgent, int limit);

        // ========== CONSULTAS PAGINADAS ==========

        /**
         * Busca leads sin transcripción con paginación
         */
        @Query(value = "SELECT * FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND (c.texto_transcription IS NULL OR c.texto_transcription = '') " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?1 OFFSET ?2", nativeQuery = true)
        List<ClienteResidencial> findLeadsWithoutTranscriptionPaginated(int limit, int offset);

        /**
         * Busca leads sin transcripción del mes actual con paginación
         */
        @Query(value = "SELECT * FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND (c.texto_transcription IS NULL OR c.texto_transcription = '') " +
                "AND YEAR(c.fecha_creacion) = YEAR(CURDATE()) " +
                "AND MONTH(c.fecha_creacion) = MONTH(CURDATE()) " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?1 OFFSET ?2", nativeQuery = true)
        List<ClienteResidencial> findLeadsWithoutTranscriptionCurrentMonthPaginated(int limit, int offset);

        /**
         * Busca leads sin transcripción por agente con paginación
         */
        @Query(value = "SELECT * FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND (c.texto_transcription IS NULL OR c.texto_transcription = '') " +
                "AND (CASE " +
                "    WHEN LOWER(c.numero_agente) LIKE 'agen%' THEN TRIM(LEADING '0' FROM SUBSTRING(LOWER(c.numero_agente), 5)) " +
                "    ELSE TRIM(LEADING '0' FROM c.numero_agente) " +
                "END) = ?1 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?2 OFFSET ?3", nativeQuery = true)
        List<ClienteResidencial> findLeadsWithoutTranscriptionByAgentPaginated(String normalizedAgent, int limit, int offset);

        /**
         * Busca leads sin transcripción del mes actual por agente con paginación
         */
        @Query(value = "SELECT * FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND (c.texto_transcription IS NULL OR c.texto_transcription = '') " +
                "AND YEAR(c.fecha_creacion) = YEAR(CURDATE()) " +
                "AND MONTH(c.fecha_creacion) = MONTH(CURDATE()) " +
                "AND (CASE " +
                "    WHEN LOWER(c.numero_agente) LIKE 'agen%' THEN TRIM(LEADING '0' FROM SUBSTRING(LOWER(c.numero_agente), 5)) " +
                "    ELSE TRIM(LEADING '0' FROM c.numero_agente) " +
                "END) = ?1 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?2 OFFSET ?3", nativeQuery = true)
        List<ClienteResidencial> findLeadsWithoutTranscriptionCurrentMonthByAgentPaginated(String normalizedAgent, int limit, int offset);

        // ========== CONSULTAS DE CONTEO PARA ESTADÍSTICAS ==========

        /**
         * Cuenta leads sin transcripción del mes actual
         */
        @Query(value = "SELECT COUNT(*) FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND (c.texto_transcription IS NULL OR c.texto_transcription = '') " +
                "AND YEAR(c.fecha_creacion) = YEAR(CURDATE()) " +
                "AND MONTH(c.fecha_creacion) = MONTH(CURDATE())", nativeQuery = true)
        Long countLeadsWithoutTranscriptionCurrentMonth();

        /**
         * Cuenta leads con transcripción del mes actual
         */
        @Query(value = "SELECT COUNT(*) FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND c.texto_transcription IS NOT NULL AND c.texto_transcription != '' " +
                "AND YEAR(c.fecha_creacion) = YEAR(CURDATE()) " +
                "AND MONTH(c.fecha_creacion) = MONTH(CURDATE())", nativeQuery = true)
        Long countLeadsWithTranscriptionCurrentMonth();

        /**
         * Cuenta leads con notas del comparador del mes actual
         */
        @Query(value = "SELECT COUNT(*) FROM cliente_residencial c " +
                "WHERE c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "AND c.nota_agente_comparador_ia IS NOT NULL " +
                "AND YEAR(c.fecha_creacion) = YEAR(CURDATE()) " +
                "AND MONTH(c.fecha_creacion) = MONTH(CURDATE())", nativeQuery = true)
        Long countLeadsWithComparatorNotesCurrentMonth();

        /**
         * Obtiene solo los IDs de todos los clientes residenciales
         *
         * @return Lista de IDs de clientes
         */
        @Query("SELECT c.id FROM ClienteResidencial c")
        List<Long> findAllIds();

        /**
         * Encuentra clientes por sus IDs
         *
         * @param ids Lista de IDs de clientes
         * @return Lista de clientes residenciales
         */
        List<ClienteResidencial> findAllByIdIn(List<Long> ids);

        /**
         * Consulta optimizada para obtener solo los datos básicos de los clientes por
         * fecha de creación
         * con un límite máximo de registros para mejorar el rendimiento
         *
         * @param inicio Fecha de inicio
         * @param fin    Fecha de fin
         * @param limite Límite máximo de registros a retornar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?1 AND ?2 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3", nativeQuery = true)
        List<Object[]> findClientesBasicosByFechaCreacion(
                LocalDateTime inicio,
                LocalDateTime fin,
                int limite);

        /**
         * Consulta optimizada para obtener solo los datos básicos de los clientes por
         * rango de fechas de creación para exportación masiva
         * con un límite máximo de registros para mejorar el rendimiento
         *
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @param limite      Límite máximo de registros a retornar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?1 AND ?2 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3", nativeQuery = true)
        List<Object[]> findClientesBasicosByRangoFechas(
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite);

        /**
         * Consulta optimizada para obtener solo los datos básicos de los clientes por
         * rango de fechas de creación con paginación para exportación masiva
         *
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @param limite      Límite máximo de registros a retornar
         * @param offset      Número de registros a saltar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?1 AND ?2 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3 OFFSET ?4", nativeQuery = true)
        List<Object[]> findClientesBasicosByRangoFechasWithOffset(
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite,
                int offset);

        /**
         * Consulta optimizada para obtener solo los datos básicos de todos los clientes
         * con un límite máximo de registros para mejorar el rendimiento
         *
         * @param limite Límite máximo de registros a retornar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "ORDER BY c.id DESC " +
                "LIMIT ?1", nativeQuery = true)
        List<Object[]> findClientesBasicos(int limite);

        /**
         * Consulta optimizada para obtener datos básicos de clientes usando un cursor
         * basado en ID
         * para paginación eficiente con grandes volúmenes de datos
         *
         * @param lastId     ID del último cliente procesado (para continuar desde ahí)
         * @param tamanoLote Tamaño del lote a recuperar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "WHERE c.id > ?1 " +
                "ORDER BY c.id ASC " +
                "LIMIT ?2", nativeQuery = true)
        List<Object[]> findClientesBasicosByCursor(long lastId, int tamanoLote);

        /**
         * Consulta optimizada para obtener datos básicos de clientes usando offset para
         * paginación
         *
         * @param offset     Número de registros a saltar
         * @param tamanoLote Tamaño del lote a recuperar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "ORDER BY c.id ASC " +
                "LIMIT ?2 OFFSET ?1", nativeQuery = true)
        List<Object[]> findClientesBasicosPaginados(int offset, int tamanoLote);

        // ===== MÉTODOS PARA ESTADÍSTICAS POR SEDE =====

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
         * específica con paginación
         *
         * @param fecha    Fecha para filtrar
         * @param pageable Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            COALESCE(s.nombre, 'Sin Sede'),
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        LEFT JOIN u.sede s
                        WHERE DATE(c.fechaCreacion) = :fecha
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorFecha(@Param("fecha") LocalDate fecha, Pageable pageable);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
         * específica con paginación y filtro de búsqueda por nombre de vendedor
         *
         * @param fecha            Fecha para filtrar
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @param pageable         Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            COALESCE(s.nombre, 'Sin Sede'),
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        LEFT JOIN u.sede s
                        WHERE DATE(c.fechaCreacion) = :fecha
                        AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                             LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                             LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                             LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorFechaConBusqueda(
                @Param("fecha") LocalDate fecha,
                @Param("busquedaVendedor") String busquedaVendedor,
                Pageable pageable);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una sede
         * y fecha específica con paginación
         *
         * @param sedeId   ID de la sede
         * @param fecha    Fecha para filtrar
         * @param pageable Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            s.nombre,
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        JOIN u.sede s
                        WHERE s.id = :sedeId AND DATE(c.fechaCreacion) = :fecha
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYFechaPaginado(@Param("sedeId") Long sedeId,
                                                                          @Param("fecha") LocalDate fecha, Pageable pageable);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una sede
         * y fecha específica con paginación y filtro de búsqueda por nombre de vendedor
         *
         * @param sedeId           ID de la sede
         * @param fecha            Fecha para filtrar
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @param pageable         Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            s.nombre,
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        JOIN u.sede s
                        WHERE s.id = :sedeId AND DATE(c.fechaCreacion) = :fecha
                        AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                             LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                             LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                             LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYFechaConBusquedaPaginado(
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("busquedaVendedor") String busquedaVendedor,
                Pageable pageable);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
         * específica (sin paginación para compatibilidad)
         *
         * @param fecha Fecha para filtrar
         * @return Lista de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            COALESCE(s.nombre, 'Sin Sede'),
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        LEFT JOIN u.sede s
                        WHERE DATE(c.fechaCreacion) = :fecha
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY s.nombre, coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorFechaSinPaginacion(@Param("fecha") LocalDate fecha);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una sede
         * y fecha específica
         *
         * @param sedeId ID de la sede
         * @param fecha  Fecha para filtrar
         * @return Lista de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            s.nombre,
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        JOIN u.sede s
                        WHERE s.id = :sedeId AND DATE(c.fechaCreacion) = :fecha
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYFecha(@Param("sedeId") Long sedeId,
                                                                  @Param("fecha") LocalDate fecha);

        /**
         * Obtiene estadísticas resumidas por sede para una fecha específica
         *
         * @param fecha Fecha para filtrar
         * @return Lista de estadísticas resumidas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            COALESCE(s.nombre, 'Sin Sede'),
                            'RESUMEN',
                            'TOTAL',
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.sede s
                        WHERE DATE(c.fechaCreacion) = :fecha
                        GROUP BY s.nombre
                        ORDER BY s.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerResumenPorSedeYFecha(@Param("fecha") LocalDate fecha);

        /**
         * Obtiene estadísticas por rango de fechas para todas las sedes
         *
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @return Lista de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            COALESCE(s.nombre, 'Sin Sede'),
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        LEFT JOIN u.sede s
                        WHERE DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY s.nombre, coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorRango(@Param("fechaInicio") LocalDate fechaInicio,
                                                             @Param("fechaFin") LocalDate fechaFin);

        /**
         * Obtiene estadísticas por rango de fechas para una sede específica
         *
         * @param sedeId      ID de la sede
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @return Lista de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            s.nombre,
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        JOIN u.sede s
                        WHERE s.id = :sedeId AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYRango(@Param("sedeId") Long sedeId,
                                                                  @Param("fechaInicio") LocalDate fechaInicio, @Param("fechaFin") LocalDate fechaFin);

        /**
         * Obtiene estadísticas por rango de fechas para todas las sedes con búsqueda
         * por vendedor
         *
         * @param fechaInicio      Fecha de inicio
         * @param fechaFin         Fecha de fin
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @return Lista de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            COALESCE(s.nombre, 'Sin Sede'),
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        LEFT JOIN u.sede s
                        WHERE DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                        AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                             LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                             LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                             LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY s.nombre, coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorRangoConBusqueda(
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                @Param("busquedaVendedor") String busquedaVendedor);

        /**
         * Obtiene estadísticas por rango de fechas para una sede específica con
         * búsqueda por vendedor
         *
         * @param sedeId           ID de la sede
         * @param fechaInicio      Fecha de inicio
         * @param fechaFin         Fecha de fin
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @return Lista de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            s.nombre,
                            COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        LEFT JOIN u.coordinador coord
                        JOIN u.sede s
                        WHERE s.id = :sedeId AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                        AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                             LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                             LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                             LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYRangoConBusqueda(
                @Param("sedeId") Long sedeId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                @Param("busquedaVendedor") String busquedaVendedor);

        // ===== CONSULTAS PARA SEDE Y SUPERVISOR CON RANGO DE FECHAS =====

        /**
         * Obtiene estadísticas por sede, supervisor y rango de fechas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                        s.nombre,
                        CONCAT(coord.nombre, ' ', coord.apellido),
                        CONCAT(u.nombre, ' ', u.apellido),
                        COUNT(c),
                        SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                        SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                        SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        JOIN u.coordinador coord
                        JOIN u.sede s
                        WHERE s.id = :sedeId AND coord.id = :supervisorId
                        AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYSupervisorYRango(
                @Param("sedeId") Long sedeId,
                @Param("supervisorId") Long supervisorId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin);

        /**
         * Obtiene estadísticas por sede, supervisor y rango de fechas con búsqueda por
         * vendedor
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                        s.nombre,
                        CONCAT(coord.nombre, ' ', coord.apellido),
                        CONCAT(u.nombre, ' ', u.apellido),
                        COUNT(c),
                        SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                        SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                        SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        JOIN u.coordinador coord
                        JOIN u.sede s
                        WHERE s.id = :sedeId AND coord.id = :supervisorId
                        AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                        AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                             LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                             LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                             LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYSupervisorYRangoConBusqueda(
                @Param("sedeId") Long sedeId,
                @Param("supervisorId") Long supervisorId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                @Param("busquedaVendedor") String busquedaVendedor);

        /**
         * Obtiene estadísticas por supervisor y rango de fechas (sin filtro de sede)
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                        s.nombre,
                        CONCAT(coord.nombre, ' ', coord.apellido),
                        CONCAT(u.nombre, ' ', u.apellido),
                        COUNT(c),
                        SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                        SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                        SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        JOIN u.coordinador coord
                        LEFT JOIN u.sede s
                        WHERE coord.id = :supervisorId
                        AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSupervisorYRango(
                @Param("supervisorId") Long supervisorId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin);

        /**
         * Obtiene estadísticas por supervisor y rango de fechas con búsqueda por
         * vendedor
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                        s.nombre,
                        CONCAT(coord.nombre, ' ', coord.apellido),
                        CONCAT(u.nombre, ' ', u.apellido),
                        COUNT(c),
                        SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                        SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                        SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        JOIN u.coordinador coord
                        LEFT JOIN u.sede s
                        WHERE coord.id = :supervisorId
                        AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                        AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                             LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                             LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                             LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        ORDER BY coord.nombre, u.nombre
                        """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSupervisorYRangoConBusqueda(
                @Param("supervisorId") Long supervisorId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                @Param("busquedaVendedor") String busquedaVendedor);

        // ===== MÉTODOS PAGINADOS =====

        /**
         * Obtiene estadísticas filtradas por sede y supervisor para una fecha
         * específica con paginación
         *
         * @param sedeId       ID de la sede
         * @param supervisorId ID del supervisor
         * @param fecha        Fecha para filtrar
         * @param pageable     Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            s.nombre,
                            CONCAT(coord.nombre, ' ', coord.apellido),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        JOIN u.coordinador coord
                        JOIN u.sede s
                        WHERE s.id = :sedeId AND coord.id = :supervisorId AND DATE(c.fechaCreacion) = :fecha
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYSupervisorPaginado(@Param("sedeId") Long sedeId,
                                                                               @Param("supervisorId") Long supervisorId, @Param("fecha") LocalDate fecha, Pageable pageable);

        /**
         * Obtiene estadísticas filtradas por sede y supervisor para una fecha
         * específica con paginación y filtro de búsqueda por nombre de vendedor
         *
         * @param sedeId           ID de la sede
         * @param supervisorId     ID del supervisor
         * @param fecha            Fecha para filtrar
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @param pageable         Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            s.nombre,
                            CONCAT(coord.nombre, ' ', coord.apellido),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        JOIN u.coordinador coord
                        JOIN u.sede s
                        WHERE s.id = :sedeId AND coord.id = :supervisorId AND DATE(c.fechaCreacion) = :fecha
                        AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                             LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                             LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                             LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYSupervisorConBusquedaPaginado(
                @Param("sedeId") Long sedeId,
                @Param("supervisorId") Long supervisorId,
                @Param("fecha") LocalDate fecha,
                @Param("busquedaVendedor") String busquedaVendedor,
                Pageable pageable);

        /**
         * Obtiene estadísticas filtradas solo por supervisor para una fecha específica
         * con paginación
         *
         * @param supervisorId ID del supervisor
         * @param fecha        Fecha para filtrar
         * @param pageable     Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            COALESCE(s.nombre, 'Sin Sede'),
                            CONCAT(coord.nombre, ' ', coord.apellido),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        JOIN u.coordinador coord
                        LEFT JOIN u.sede s
                        WHERE coord.id = :supervisorId AND DATE(c.fechaCreacion) = :fecha
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSupervisorPaginado(@Param("supervisorId") Long supervisorId,
                                                                          @Param("fecha") LocalDate fecha, Pageable pageable);

        /**
         * Obtiene estadísticas filtradas solo por supervisor para una fecha específica
         * con paginación y filtro de búsqueda por nombre de vendedor
         *
         * @param supervisorId     ID del supervisor
         * @param fecha            Fecha para filtrar
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @param pageable         Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
                        SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                            COALESCE(s.nombre, 'Sin Sede'),
                            CONCAT(coord.nombre, ' ', coord.apellido),
                            CONCAT(u.nombre, ' ', u.apellido),
                            COUNT(c.id),
                            SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                            SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
                        )
                        FROM ClienteResidencial c
                        JOIN c.usuario u
                        JOIN u.coordinador coord
                        LEFT JOIN u.sede s
                        WHERE coord.id = :supervisorId AND DATE(c.fechaCreacion) = :fecha
                        AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                             LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                             LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                             LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
                        GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
                        """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSupervisorConBusquedaPaginado(
                @Param("supervisorId") Long supervisorId,
                @Param("fecha") LocalDate fecha,
                @Param("busquedaVendedor") String busquedaVendedor,
                Pageable pageable);

        // ===== CONSULTAS OPTIMIZADAS PARA EXPORTACIÓN RÁPIDA =====

        /**
         * Consulta optimizada para exportar clientes por sede y fecha - UNA SOLA
         * CONSULTA
         * Similar al método rápido de rango de fechas
         *
         * @param sedeId ID de la sede
         * @param fecha  Fecha específica
         * @param limite Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.numero_agente, CONCAT(u.nombre, ' ', u.apellido) as asesor, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, GROUP_CONCAT(DISTINCT map.movil SEPARATOR ', ') as moviles_a_portar, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "LEFT JOIN cliente_residencial_moviles_aportar map ON c.id = map.cliente_residencial_id " +
                "WHERE DATE(c.fecha_creacion) = ?2 " +
                "AND u.sede_id = ?1 " +
                "GROUP BY c.id, c.numero_agente, u.nombre, u.apellido, coord.nombre, coord.apellido, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSedeYFechaOptimizado(
                Long sedeId,
                LocalDate fecha,
                int limite);

        /**
         * Consulta optimizada para exportar clientes por sede, supervisor y fecha - UNA
         * SOLA CONSULTA
         *
         * @param sedeId       ID de la sede
         * @param supervisorId ID del supervisor/coordinador
         * @param fecha        Fecha específica
         * @param limite       Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.numero_agente, CONCAT(u.nombre, ' ', u.apellido) as asesor, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, GROUP_CONCAT(DISTINCT map.movil SEPARATOR ', ') as moviles_a_portar, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "LEFT JOIN cliente_residencial_moviles_aportar map ON c.id = map.cliente_residencial_id " +
                "WHERE DATE(c.fecha_creacion) = ?3 " +
                "AND u.sede_id = ?1 " +
                "AND u.coordinador_id = ?2 " +
                "GROUP BY c.id, c.numero_agente, u.nombre, u.apellido, coord.nombre, coord.apellido, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?4", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSedeYSupervisorYFechaOptimizado(
                Long sedeId,
                Long supervisorId,
                LocalDate fecha,
                int limite);

        // ===== CONSULTAS OPTIMIZADAS PARA RANGO DE FECHAS =====

        /**
         * Consulta optimizada para exportar clientes por sede y rango de fechas
         * INCLUYE MÓVILES A PORTAR con JOIN
         *
         * @param sedeId      ID de la sede
         * @param fechaInicio Fecha de inicio del rango
         * @param fechaFin    Fecha de fin del rango
         * @param limite      Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.numero_agente, CONCAT(u.nombre, ' ', u.apellido) as asesor, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, GROUP_CONCAT(DISTINCT map.movil SEPARATOR ', ') as moviles_a_portar, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "LEFT JOIN cliente_residencial_moviles_aportar map ON c.id = map.cliente_residencial_id " +
                "WHERE c.fecha_creacion BETWEEN ?2 AND ?3 " +
                "AND u.sede_id = ?1 " +
                "GROUP BY c.id, c.numero_agente, u.nombre, u.apellido, coord.nombre, coord.apellido, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?4", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSedeYRangoFechas(
                Long sedeId,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite);

        /**
         * Consulta optimizada para exportar clientes por sede, supervisor y rango de
         * fechas
         * INCLUYE MÓVILES A PORTAR con JOIN
         *
         * @param sedeId       ID de la sede
         * @param supervisorId ID del supervisor/coordinador
         * @param fechaInicio  Fecha de inicio del rango
         * @param fechaFin     Fecha de fin del rango
         * @param limite       Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.numero_agente, CONCAT(u.nombre, ' ', u.apellido) as asesor, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, GROUP_CONCAT(DISTINCT map.movil SEPARATOR ', ') as moviles_a_portar, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "LEFT JOIN cliente_residencial_moviles_aportar map ON c.id = map.cliente_residencial_id " +
                "WHERE c.fecha_creacion BETWEEN ?3 AND ?4 " +
                "AND u.sede_id = ?1 " +
                "AND u.coordinador_id = ?2 " +
                "GROUP BY c.id, c.numero_agente, u.nombre, u.apellido, coord.nombre, coord.apellido, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?5", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSedeYSupervisorYRangoFechas(
                Long sedeId,
                Long supervisorId,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite);

        /**
         * Consulta mejorada para obtener datos básicos de clientes por rango de fechas
         * con paginación - INCLUYE TODOS LOS CAMPOS NECESARIOS PARA CONSISTENCIA
         * INCLUYE MÓVILES A PORTAR con JOIN
         *
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @param limite      Límite máximo de registros a retornar
         * @param offset      Número de registros a saltar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT " +
                "c.id, " +
                "c.numero_agente, " +
                "CONCAT(u.nombre, ' ', u.apellido) AS asesor, " +
                "CONCAT(coord.nombre, ' ', coord.apellido) AS coordinador, " +
                "c.campania, " +
                "c.tipo_plan, " +
                "c.titular_del_servicio, " +
                "c.nif_nie, " +
                "c.nombres_apellidos, " +
                "c.fecha_nacimiento, " +
                "c.tipo_tecnologia, " +
                "c.velocidad, " +
                "c.futbol, " +
                "c.permanencia, " +
                "c.numero_moviles, " +
                "c.movil_contacto, " +
                "c.plan_actual, " +
                "c.direccion, " +
                "c.fijo_compania, " +
                "GROUP_CONCAT(DISTINCT map.movil SEPARATOR ', ') AS moviles_a_portar, " +
                "c.cuenta_bancaria, " +
                "c.correo_electronico, " +
                "c.observacion, " +
                "c.autoriza_seguros, " +
                "c.autoriza_energias, " +
                "c.venta_realizada, " +
                "c.desea_promociones_lowi, " +
                "c.nota_agente_comparador_ia " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "LEFT JOIN cliente_residencial_moviles_aportar map ON c.id = map.cliente_residencial_id " +
                "WHERE c.fecha_creacion BETWEEN ?1 AND ?2 " +
                "GROUP BY " +
                "c.id, " +
                "c.numero_agente, " +
                "u.nombre, " +
                "u.apellido, " +
                "coord.nombre, " +
                "coord.apellido, " +
                "c.campania, " +
                "c.tipo_plan, " +
                "c.titular_del_servicio, " +
                "c.nif_nie, " +
                "c.nombres_apellidos, " +
                "c.fecha_nacimiento, " +
                "c.tipo_tecnologia, " +
                "c.velocidad, " +
                "c.futbol, " +
                "c.permanencia, " +
                "c.numero_moviles, " +
                "c.movil_contacto, " +
                "c.plan_actual, " +
                "c.direccion, " +
                "c.fijo_compania, " +
                "c.cuenta_bancaria, " +
                "c.correo_electronico, " +
                "c.observacion, " +
                "c.autoriza_seguros, " +
                "c.autoriza_energias, " +
                "c.venta_realizada, " +
                "c.desea_promociones_lowi, " +
                "c.nota_agente_comparador_ia " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3 OFFSET ?4",
                nativeQuery = true)
        List<Object[]> findClientesBasicosByRangoFechasWithOffsetMejorado(
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite,
                int offset
        );


        /**
         * Consulta optimizada para exportar clientes por supervisor y rango de fechas
         * INCLUYE MÓVILES A PORTAR con JOIN
         *
         * @param supervisorId ID del supervisor/coordinador
         * @param fechaInicio  Fecha de inicio del rango
         * @param fechaFin     Fecha de fin del rango
         * @param limite       Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.numero_agente, CONCAT(u.nombre, ' ', u.apellido) as asesor, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, GROUP_CONCAT(DISTINCT map.movil SEPARATOR ', ') as moviles_a_portar, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "LEFT JOIN cliente_residencial_moviles_aportar map ON c.id = map.cliente_residencial_id " +
                "WHERE c.fecha_creacion BETWEEN ?2 AND ?3 " +
                "AND u.coordinador_id = ?1 " +
                "GROUP BY c.id, c.numero_agente, u.nombre, u.apellido, coord.nombre, coord.apellido, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?4", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSupervisorYRangoFechas(
                Long supervisorId,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite);

        // ===== CONSULTA OPTIMIZADA CON FILTRO DE BÚSQUEDA POR VENDEDOR =====

        /**
         * Consulta optimizada para obtener clientes con filtros completos incluyendo
         * búsqueda por vendedor
         * Soporta tanto fecha específica como rango de fechas
         */
        @Query(value = "SELECT c.id, c.numero_agente, CONCAT(u.nombre, ' ', u.apellido) as asesor, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, GROUP_CONCAT(DISTINCT map.movil SEPARATOR ', ') as moviles_a_portar, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "LEFT JOIN cliente_residencial_moviles_aportar map ON c.id = map.cliente_residencial_id " +
                "WHERE (" +
                "  (?3 IS NOT NULL AND DATE(c.fecha_creacion) = ?3) OR " +
                "  (?4 IS NOT NULL AND ?5 IS NOT NULL AND c.fecha_creacion BETWEEN ?4 AND ?5)" +
                ") " +
                "AND (?1 IS NULL OR u.sede_id = ?1) " +
                "AND (?2 IS NULL OR u.coordinador_id = ?2) " +
                "AND (?6 IS NULL OR ?6 = '' OR " +
                "     LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(?6, '  ', ' '), '%')) OR "
                +
                "     LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', ?6, '%')) OR " +
                "     LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', ?6, '%'))" +
                ") " +
                "GROUP BY c.id, c.numero_agente, u.nombre, u.apellido, coord.nombre, coord.apellido, c.campania, c.tipo_plan, c.titular_del_servicio, " +
                "c.nif_nie, c.nombres_apellidos, c.fecha_nacimiento, c.tipo_tecnologia, c.velocidad, " +
                "c.futbol, c.permanencia, c.numero_moviles, c.plan_actual, c.direccion, " +
                "c.fijo_compania, c.cuenta_bancaria, c.correo_electronico, c.observacion, " +
                "c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.desea_promociones_lowi, c.nota_agente_comparador_ia " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?7", nativeQuery = true)
        List<Object[]> findClientesBasicosPorFiltrosConBusquedaVendedor(
                Long sedeId,
                Long supervisorId,
                LocalDate fecha,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                String busquedaVendedor,
                int limite);


        // --- Métodos para paginación de Leads Pendientes (sin transcripción) ---

        /** 1) Todos los leads pendientes (sin filtro de fecha) */
        @Query("SELECT c FROM ClienteResidencial c " +
                "WHERE (c.textoTranscription IS NULL OR c.textoTranscription = '') " +
                "ORDER BY c.fechaCreacion ASC")
        Page<ClienteResidencial> findPendingLeads(Pageable pageable);

        /** 2) Todos los leads pendientes de un agente (sin filtro de fecha) */
        @Query("SELECT c FROM ClienteResidencial c " +
                "WHERE c.numeroAgente = :numeroAgente " +
                "  AND (c.textoTranscription IS NULL OR c.textoTranscription = '') " +
                "ORDER BY c.fechaCreacion ASC")
        Page<ClienteResidencial> findPendingLeadsByNumeroAgente(
                @Param("numeroAgente") String numeroAgente,
                Pageable pageable
        );

        // Leads pendientes del mes actual, paginados
        @Query("SELECT cr FROM ClienteResidencial cr WHERE (cr.textoTranscription IS NULL OR cr.textoTranscription = '') AND MONTH(cr.fechaCreacion) = MONTH(CURRENT_DATE) AND YEAR(cr.fechaCreacion) = YEAR(CURRENT_DATE) ORDER BY cr.fechaCreacion ASC")
        Page<ClienteResidencial> findPendingLeadsCurrentMonth(Pageable pageable);

        // Leads pendientes del mes actual por agente, paginados
        @Query("SELECT cr FROM ClienteResidencial cr WHERE cr.numeroAgente = :numeroAgente AND (cr.textoTranscription IS NULL OR cr.textoTranscription = '') AND MONTH(cr.fechaCreacion) = MONTH(CURRENT_DATE) AND YEAR(cr.fechaCreacion) = YEAR(CURRENT_DATE) ORDER BY cr.fechaCreacion ASC")
        Page<ClienteResidencial> findPendingLeadsCurrentMonthByNumeroAgente(@Param("numeroAgente") String numeroAgente, Pageable pageable);

        // --- Métodos para conteo de Leads Pendientes (sin transcripción) ---

        // Conteo de todos los leads pendientes
        @Query("SELECT COUNT(cr) FROM ClienteResidencial cr WHERE (cr.textoTranscription IS NULL OR cr.textoTranscription = '')")
        long countPendingLeads();

        // Conteo de leads pendientes por agente
        @Query("SELECT COUNT(cr) FROM ClienteResidencial cr WHERE cr.numeroAgente = :numeroAgente AND (cr.textoTranscription IS NULL OR cr.textoTranscription = '')")
        long countPendingLeadsByNumeroAgente(@Param("numeroAgente") String numeroAgente);

        // Conteo de leads pendientes del mes actual
        @Query("SELECT COUNT(cr) FROM ClienteResidencial cr WHERE (cr.textoTranscription IS NULL OR cr.textoTranscription = '') AND MONTH(cr.fechaCreacion) = MONTH(CURRENT_DATE) AND YEAR(cr.fechaCreacion) = YEAR(CURRENT_DATE)")
        long countPendingLeadsCurrentMonth();

        // Conteo de leads pendientes del mes actual por agente
        @Query("SELECT COUNT(cr) FROM ClienteResidencial cr WHERE cr.numeroAgente = :numeroAgente AND (cr.textoTranscription IS NULL OR cr.textoTranscription = '') AND MONTH(cr.fechaCreacion) = MONTH(CURRENT_DATE) AND YEAR(cr.fechaCreacion) = YEAR(CURRENT_DATE)")
        long countPendingLeadsCurrentMonthByNumeroAgente(@Param("numeroAgente") String numeroAgente);


        // Métodos para estadísticas generales
        @Query(value = "SELECT COUNT(*) FROM cliente_residencial WHERE texto_transcription IS NOT NULL AND texto_transcription != ''", nativeQuery = true)
        long countWithTranscription();

        // ✅ CORREGIDO: Ahora usa el campo correcto para notas
        @Query(value = "SELECT COUNT(*) FROM cliente_residencial WHERE nota_agente_comparador_ia IS NOT NULL", nativeQuery = true)
        long countWithNotes();

        // Para estadísticas generales también
        @Query(value = "SELECT numero_agente, COUNT(*), " +
                "SUM(CASE WHEN texto_transcription IS NOT NULL AND texto_transcription != '' THEN 1 ELSE 0 END), " +
                "SUM(CASE WHEN nota_agente_comparador_ia IS NOT NULL THEN 1 ELSE 0 END) " +
                "FROM cliente_residencial " +
                "GROUP BY numero_agente", nativeQuery = true)
        List<Object[]> getStatsByAgent();

        // Métodos para estadísticas del mes actual
        @Query(value = "SELECT COUNT(*) FROM cliente_residencial WHERE fecha_creacion BETWEEN :startDate AND :endDate", nativeQuery = true)
        long countByFechaCreacionBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

        // ESTAS CONSULTAS SÍ FILTRAN POR FECHA - CORREGIDAS
        @Query(value = "SELECT COUNT(*) FROM cliente_residencial WHERE fecha_creacion BETWEEN ? AND ? AND texto_transcription IS NOT NULL AND texto_transcription != ''", nativeQuery = true)
        long countByFechaCreacionBetweenAndTranscripcionIsNotNull(LocalDateTime startDate, LocalDateTime endDate);

        @Query(value = "SELECT COUNT(*) FROM cliente_residencial WHERE fecha_creacion BETWEEN ? AND ? AND (texto_transcription IS NULL OR texto_transcription = '')", nativeQuery = true)
        long countByFechaCreacionBetweenAndTranscripcionIsNull(LocalDateTime startDate, LocalDateTime endDate);

        @Query(value = "SELECT COUNT(*) FROM cliente_residencial WHERE fecha_creacion BETWEEN ? AND ? AND observacion IS NOT NULL AND observacion != ''", nativeQuery = true)
        long countByFechaCreacionBetweenAndObservacionesIsNotNull(LocalDateTime startDate, LocalDateTime endDate);

        // ✅ CORREGIDO: Estadísticas por agente incluyendo notas
        @Query(value = "SELECT numero_agente, COUNT(*), " +
                "SUM(CASE WHEN texto_transcription IS NOT NULL AND texto_transcription != '' THEN 1 ELSE 0 END), " +
                "SUM(CASE WHEN nota_agente_comparador_ia IS NOT NULL THEN 1 ELSE 0 END) " +
                "FROM cliente_residencial " +
                "WHERE fecha_creacion BETWEEN ? AND ? " +
                "GROUP BY numero_agente", nativeQuery = true)
        List<Object[]> getStatsByAgentForCurrentMonth(LocalDateTime startDate, LocalDateTime endDate);

        // ✅ CORREGIDO: Notas filtradas por mes actual
        @Query(value = "SELECT COUNT(*) FROM cliente_residencial WHERE fecha_creacion BETWEEN ? AND ? AND nota_agente_comparador_ia IS NOT NULL", nativeQuery = true)
        long countByFechaCreacionBetweenAndNotasIsNotNull(LocalDateTime startDate, LocalDateTime endDate);

        /** 4) Leads pendientes en un rango de fechas **y** por agente */
        @Query("SELECT c FROM ClienteResidencial c " +
                "WHERE (c.textoTranscription IS NULL OR c.textoTranscription = '') " +
                "  AND c.fechaCreacion BETWEEN :start AND :end " +
                "  AND c.numeroAgente = :numeroAgente " +
                "ORDER BY c.fechaCreacion ASC")
        Page<ClienteResidencial> findPendingLeadsByDateRangeAndAgent(
                @Param("start")        LocalDateTime start,
                @Param("end")          LocalDateTime end,
                @Param("numeroAgente") String numeroAgente,
                Pageable pageable
        );

        /** 3) Leads pendientes en un rango de fechas */
        @Query("SELECT c FROM ClienteResidencial c " +
                "WHERE (c.textoTranscription IS NULL OR c.textoTranscription = '') " +
                "  AND c.fechaCreacion BETWEEN :start AND :end " +
                "ORDER BY c.fechaCreacion ASC")
        Page<ClienteResidencial> findPendingLeadsByDateRange(
                @Param("start") LocalDateTime start,
                @Param("end")   LocalDateTime end,
                Pageable pageable
        );

        // Para filtrar por agente sin restricción de fecha
        Page<ClienteResidencial> findByTextoTranscriptionIsNullAndNumeroAgenteOrderByFechaCreacionDesc(
                String numeroAgente,
                Pageable pageable
        );

        // ========== CONSULTAS PARA LEADS CON TRANSCRIPCIÓN (PARA COMPARACIÓN) ==========

        /**
         * Busca leads que YA TIENEN transcripción por fecha específica
         */
        @Query("SELECT c FROM ClienteResidencial c " +
                "WHERE c.textoTranscription IS NOT NULL AND c.textoTranscription != '' " +
                "AND DATE(c.fechaCreacion) = :fecha " +
                "AND (:numeroAgente IS NULL OR c.numeroAgente = :numeroAgente) " +
                "ORDER BY c.fechaCreacion ASC")
        List<ClienteResidencial> findLeadsWithTranscriptionByDate(
                @Param("fecha") LocalDate fecha,
                @Param("numeroAgente") String numeroAgente,
                Pageable pageable
        );

        /**
         * Busca leads que YA TIENEN transcripción - todos los meses
         */
        @Query("SELECT c FROM ClienteResidencial c " +
                "WHERE c.textoTranscription IS NOT NULL AND c.textoTranscription != '' " +
                "AND (:numeroAgente IS NULL OR c.numeroAgente = :numeroAgente) " +
                "ORDER BY c.fechaCreacion ASC")
        List<ClienteResidencial> findLeadsWithTranscriptionAllDates(
                @Param("numeroAgente") String numeroAgente,
                Pageable pageable
        );

        /**
         * Busca leads que YA TIENEN transcripción del mes actual
         */
        @Query("SELECT c FROM ClienteResidencial c " +
                "WHERE c.textoTranscription IS NOT NULL AND c.textoTranscription != '' " +
                "AND MONTH(c.fechaCreacion) = MONTH(CURRENT_DATE) " +
                "AND YEAR(c.fechaCreacion) = YEAR(CURRENT_DATE) " +
                "AND (:numeroAgente IS NULL OR c.numeroAgente = :numeroAgente) " +
                "ORDER BY c.fechaCreacion ASC")
        List<ClienteResidencial> findLeadsWithTranscriptionCurrentMonth(
                @Param("numeroAgente") String numeroAgente,
                Pageable pageable
        );

        // Para obtener todos sin filtro de agente ni fecha
        Page<ClienteResidencial> findByTextoTranscriptionIsNullOrderByFechaCreacionDesc(Pageable pageable);

// Archivo: ClienteResidencialRepository.java
// REEMPLAZA los 6 métodos de estadísticas por esta versión final y corregida.
// Se mantienen los nombres originales de los métodos.

        @Query(value = """
        WITH leads_registrados_por_coord AS (
            SELECT u.coordinador_id, COUNT(c.id) as total_leads, SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien, SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal
            FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
            GROUP BY u.coordinador_id
        ), audios_huerfanos_por_coord AS (
            SELECT Agentes.coordinador_id, COUNT(DISTINCT asl.id) as total_huerfanos
            FROM (SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario) AS Agentes
            JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = Agentes.numero_agente COLLATE utf8mb4_unicode_ci
            GROUP BY Agentes.coordinador_id
        )
        SELECT s.nombre, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre, coord.codi_usuario,
               (COALESCE(lr.total_leads, 0) + COALESCE(ah.total_huerfanos, 0)), COALESCE(lr.total_leads, 0),
               COALESCE(lr.bien, 0), COALESCE(lr.mal, 0), COALESCE(ah.total_huerfanos, 0)
        FROM usuarios coord
        LEFT JOIN sedes s ON coord.sede_id = s.id
        LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
        LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
        WHERE coord.role = 'COORDINADOR' AND (lr.total_leads IS NOT NULL OR ah.total_huerfanos IS NOT NULL)
        ORDER BY s.nombre, coordinador_nombre
        """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionPorCoordinador();

        @Query(value = """
        WITH leads_registrados_por_coord AS (
            SELECT u.coordinador_id, COUNT(c.id) as total_leads, SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien, SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal
            FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE u.sede_id = :sedeId AND c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
            GROUP BY u.coordinador_id
        ), audios_huerfanos_por_coord AS (
            SELECT Agentes.coordinador_id, COUNT(DISTINCT asl.id) as total_huerfanos
            FROM (SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario WHERE u.sede_id = :sedeId) AS Agentes
            JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = Agentes.numero_agente COLLATE utf8mb4_unicode_ci
            GROUP BY Agentes.coordinador_id
        )
        SELECT s.nombre, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre, coord.codi_usuario,
               (COALESCE(lr.total_leads, 0) + COALESCE(ah.total_huerfanos, 0)), COALESCE(lr.total_leads, 0),
               COALESCE(lr.bien, 0), COALESCE(lr.mal, 0), COALESCE(ah.total_huerfanos, 0)
        FROM usuarios coord
        LEFT JOIN sedes s ON coord.sede_id = s.id
        LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
        LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
        WHERE coord.role = 'COORDINADOR' AND coord.sede_id = :sedeId AND (lr.total_leads IS NOT NULL OR ah.total_huerfanos IS NOT NULL)
        ORDER BY s.nombre, coordinador_nombre
        """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionPorCoordinadorYSede(@Param("sedeId") Long sedeId);

        @Query(value = """
        WITH leads_registrados_por_coord AS (
            SELECT u.coordinador_id, COUNT(c.id) as total_leads, SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien, SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal
            FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE DATE(c.fecha_creacion) = :fecha AND c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
            GROUP BY u.coordinador_id
        ), audios_huerfanos_por_coord AS (
            SELECT Agentes.coordinador_id, COUNT(DISTINCT asl.id) as total_huerfanos
            FROM (SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario WHERE DATE(c.fecha_creacion) = :fecha) AS Agentes
            JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = Agentes.numero_agente COLLATE utf8mb4_unicode_ci
            WHERE DATE(asl.fecha_extraida) = :fecha GROUP BY Agentes.coordinador_id
        )
        SELECT s.nombre, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre, coord.codi_usuario,
               (COALESCE(lr.total_leads, 0) + COALESCE(ah.total_huerfanos, 0)), COALESCE(lr.total_leads, 0),
               COALESCE(lr.bien, 0), COALESCE(lr.mal, 0), COALESCE(ah.total_huerfanos, 0)
        FROM usuarios coord
        LEFT JOIN sedes s ON coord.sede_id = s.id
        LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
        LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
        WHERE coord.role = 'COORDINADOR' AND (lr.total_leads IS NOT NULL OR ah.total_huerfanos IS NOT NULL)
        ORDER BY s.nombre, coordinador_nombre
        """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionPorCoordinadorYFecha(@Param("fecha") LocalDate fecha);

        @Query(value = """
        WITH leads_registrados_por_coord AS (
            SELECT u.coordinador_id, COUNT(c.id) as total_leads, SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien, SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal
            FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE u.sede_id = :sedeId AND DATE(c.fecha_creacion) = :fecha AND c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
            GROUP BY u.coordinador_id
        ), audios_huerfanos_por_coord AS (
            SELECT Agentes.coordinador_id, COUNT(DISTINCT asl.id) as total_huerfanos
            FROM (SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario WHERE u.sede_id = :sedeId AND DATE(c.fecha_creacion) = :fecha) AS Agentes
            JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = Agentes.numero_agente COLLATE utf8mb4_unicode_ci
            WHERE DATE(asl.fecha_extraida) = :fecha GROUP BY Agentes.coordinador_id
        )
        SELECT s.nombre, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre, coord.codi_usuario,
               (COALESCE(lr.total_leads, 0) + COALESCE(ah.total_huerfanos, 0)), COALESCE(lr.total_leads, 0),
               COALESCE(lr.bien, 0), COALESCE(lr.mal, 0), COALESCE(ah.total_huerfanos, 0)
        FROM usuarios coord
        LEFT JOIN sedes s ON coord.sede_id = s.id
        LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
        LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
        WHERE coord.role = 'COORDINADOR' AND coord.sede_id = :sedeId AND (lr.total_leads IS NOT NULL OR ah.total_huerfanos IS NOT NULL)
        ORDER BY s.nombre, coordinador_nombre
        """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionPorCoordinadorSedeYFecha(@Param("sedeId") Long sedeId, @Param("fecha") LocalDate fecha);

        @Query(value = """
        WITH leads_registrados_por_coord AS (
            SELECT u.coordinador_id, COUNT(c.id) as total_leads, SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien, SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal
            FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE DATE(c.fecha_creacion) BETWEEN :fechaInicio AND :fechaFin AND c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
            GROUP BY u.coordinador_id
        ), audios_huerfanos_por_coord AS (
            SELECT Agentes.coordinador_id, COUNT(DISTINCT asl.id) as total_huerfanos
            FROM (SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario WHERE DATE(c.fecha_creacion) BETWEEN :fechaInicio AND :fechaFin) AS Agentes
            JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = Agentes.numero_agente COLLATE utf8mb4_unicode_ci
            WHERE DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin GROUP BY Agentes.coordinador_id
        )
        SELECT s.nombre, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre, coord.codi_usuario,
               (COALESCE(lr.total_leads, 0) + COALESCE(ah.total_huerfanos, 0)), COALESCE(lr.total_leads, 0),
               COALESCE(lr.bien, 0), COALESCE(lr.mal, 0), COALESCE(ah.total_huerfanos, 0)
        FROM usuarios coord
        LEFT JOIN sedes s ON coord.sede_id = s.id
        LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
        LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
        WHERE coord.role = 'COORDINADOR' AND (lr.total_leads IS NOT NULL OR ah.total_huerfanos IS NOT NULL)
        ORDER BY s.nombre, coordinador_nombre
        """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionPorCoordinadorYRangoFechas(@Param("fechaInicio") LocalDate fechaInicio, @Param("fechaFin") LocalDate fechaFin);

        @Query(value = """
        WITH leads_registrados_por_coord AS (
            SELECT u.coordinador_id, COUNT(c.id) as total_leads, SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien, SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal
            FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE u.sede_id = :sedeId AND DATE(c.fecha_creacion) BETWEEN :fechaInicio AND :fechaFin AND c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
            GROUP BY u.coordinador_id
        ), audios_huerfanos_por_coord AS (
            SELECT Agentes.coordinador_id, COUNT(DISTINCT asl.id) as total_huerfanos
            FROM (SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario WHERE u.sede_id = :sedeId AND DATE(c.fecha_creacion) BETWEEN :fechaInicio AND :fechaFin) AS Agentes
            JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = Agentes.numero_agente COLLATE utf8mb4_unicode_ci
            WHERE DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin GROUP BY Agentes.coordinador_id
        )
        SELECT s.nombre, CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre, coord.codi_usuario,
               (COALESCE(lr.total_leads, 0) + COALESCE(ah.total_huerfanos, 0)), COALESCE(lr.total_leads, 0),
               COALESCE(lr.bien, 0), COALESCE(lr.mal, 0), COALESCE(ah.total_huerfanos, 0)
        FROM usuarios coord
        LEFT JOIN sedes s ON coord.sede_id = s.id
        LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
        LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
        WHERE coord.role = 'COORDINADOR' AND coord.sede_id = :sedeId AND (lr.total_leads IS NOT NULL OR ah.total_huerfanos IS NOT NULL)
        ORDER BY s.nombre, coordinador_nombre
        """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionPorCoordinadorSedeYRangoFechas(@Param("sedeId") Long sedeId, @Param("fechaInicio") LocalDate fechaInicio, @Param("fechaFin") LocalDate fechaFin);

        /**
         * Busca leads por fecha de creación, móvil y agente para verificar si existe un lead para un audio
         */
        List<ClienteResidencial> findByFechaCreacionAndMovilContactoContainingAndNumeroAgenteContaining(
                LocalDate fechaCreacion,
                String movilContacto,
                String numeroAgente
        );

        /**
         * Busca leads por móvil, agente y rango de fechas
         */
        List<ClienteResidencial> findByMovilContactoAndNumeroAgenteAndFechaCreacionBetween(
                String movilContacto,
                String numeroAgente,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin
        );

        /**
         * 🔍 BÚSQUEDA FLEXIBLE DE LEADS POR AUDIO
         * Busca leads que coincidan con un archivo de audio usando múltiples estrategias:
         * 1. Coincidencia exacta de móvil
         * 2. Coincidencia flexible de agente (normalizado)
         * 3. Rango de fechas del día completo
         */
        @Query(value = "SELECT * FROM cliente_residencial c " +
                "WHERE c.movil_contacto = ?1 " +
                "AND c.fecha_creacion BETWEEN ?3 AND ?4 " +
                "AND (" +
                "  c.numero_agente = ?2 " +
                "  OR (CASE " +
                "    WHEN LOWER(c.numero_agente) LIKE 'agen%' THEN TRIM(LEADING '0' FROM SUBSTRING(LOWER(c.numero_agente), 5)) " +
                "    ELSE TRIM(LEADING '0' FROM c.numero_agente) " +
                "  END) = ?2 " +
                "  OR c.numero_agente = CONCAT('agen', LPAD(?2, 3, '0')) " +
                "  OR c.numero_agente = CONCAT('agent', LPAD(?2, 3, '0')) " +
                ") " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT 5", nativeQuery = true)
        List<ClienteResidencial> findLeadsByAudioPattern(
                String movilContacto,
                String agenteNormalizado,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin
        );

        /**
         * 📊 VERIFICACIÓN MASIVA DE AUDIOS VS LEADS
         * Obtiene todos los leads de una fecha específica con información de móvil y agente
         * para verificación masiva contra archivos de audio
         */
        @Query(value = "SELECT c.id, c.movil_contacto, c.numero_agente, c.fecha_creacion, " +
                "c.texto_transcription, c.url_drive_transcripcion, c.nota_agente_comparador_ia, " +
                "(CASE " +
                "  WHEN LOWER(c.numero_agente) LIKE 'agen%' THEN TRIM(LEADING '0' FROM SUBSTRING(LOWER(c.numero_agente), 5)) " +
                "  ELSE TRIM(LEADING '0' FROM c.numero_agente) " +
                "END) as agente_normalizado " +
                "FROM cliente_residencial c " +
                "WHERE c.fecha_creacion BETWEEN ?1 AND ?2 " +
                "AND c.movil_contacto IS NOT NULL AND c.movil_contacto != '' " +
                "AND c.numero_agente IS NOT NULL AND c.numero_agente != '' " +
                "ORDER BY c.fecha_creacion DESC", nativeQuery = true)
        List<Object[]> findAllLeadsForAudioVerification(
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin
        );

        /**
         * Busca leads por número de agente para determinar coordinador
         */
        List<ClienteResidencial> findByNumeroAgenteContaining(String numeroAgente);

        // ===== CONSULTAS PARA ESTADÍSTICAS DE TRANSCRIPCIONES POR ASESOR =====

        /**
         * Obtiene estadísticas de transcripciones por asesor de un coordinador específico
         */
        @Query("""
                SELECT new com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO(
                    COALESCE(CONCAT(u.nombre, ' ', u.apellido), 'Sin Asesor'),
                    COUNT(c.id),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA >= 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA < 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NULL OR TRIM(c.textoTranscription) = '' THEN 1 ELSE 0 END)
                )
                FROM ClienteResidencial c
                JOIN c.usuario u
                JOIN u.coordinador coord
                WHERE coord.id = :coordinadorId
                GROUP BY u.nombre, u.apellido
                ORDER BY u.nombre, u.apellido
                """)
        List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionPorAsesorYCoordinador(
                @Param("coordinadorId") Long coordinadorId
        );

        /**
         * Obtiene estadísticas de transcripciones por asesor filtrado por coordinador y sede
         */
        @Query("""
                SELECT new com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO(
                    COALESCE(CONCAT(u.nombre, ' ', u.apellido), 'Sin Asesor'),
                    COUNT(c.id),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA >= 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA < 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NULL OR TRIM(c.textoTranscription) = '' THEN 1 ELSE 0 END)
                )
                FROM ClienteResidencial c
                JOIN c.usuario u
                JOIN u.coordinador coord
                LEFT JOIN u.sede s
                WHERE coord.id = :coordinadorId AND s.id = :sedeId
                GROUP BY u.nombre, u.apellido
                ORDER BY u.nombre, u.apellido
                """)
        List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionPorAsesorCoordinadorYSede(
                @Param("coordinadorId") Long coordinadorId,
                @Param("sedeId") Long sedeId
        );

        /**
         * Obtiene estadísticas de transcripciones por asesor filtrado por coordinador y fecha
         */
        @Query("""
                SELECT new com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO(
                    COALESCE(CONCAT(u.nombre, ' ', u.apellido), 'Sin Asesor'),
                    COUNT(c.id),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA >= 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA < 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NULL OR TRIM(c.textoTranscription) = '' THEN 1 ELSE 0 END)
                )
                FROM ClienteResidencial c
                JOIN c.usuario u
                JOIN u.coordinador coord
                WHERE coord.id = :coordinadorId AND DATE(c.fechaCreacion) = :fecha
                GROUP BY u.nombre, u.apellido
                ORDER BY u.nombre, u.apellido
                """)
        List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionPorAsesorCoordinadorYFecha(
                @Param("coordinadorId") Long coordinadorId,
                @Param("fecha") LocalDate fecha
        );

        /**
         * Obtiene estadísticas de transcripciones por asesor filtrado por coordinador, sede y fecha
         */
        @Query("""
                SELECT new com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO(
                    COALESCE(CONCAT(u.nombre, ' ', u.apellido), 'Sin Asesor'),
                    COUNT(c.id),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA >= 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA < 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NULL OR TRIM(c.textoTranscription) = '' THEN 1 ELSE 0 END)
                )
                FROM ClienteResidencial c
                JOIN c.usuario u
                JOIN u.coordinador coord
                LEFT JOIN u.sede s
                WHERE coord.id = :coordinadorId AND s.id = :sedeId AND DATE(c.fechaCreacion) = :fecha
                GROUP BY u.nombre, u.apellido
                ORDER BY u.nombre, u.apellido
                """)
        List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionPorAsesorCoordinadorSedeYFecha(
                @Param("coordinadorId") Long coordinadorId,
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha
        );

        /**
         * Obtiene estadísticas de transcripciones por asesor filtrado por coordinador y rango de fechas
         */
        @Query("""
                SELECT new com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO(
                    COALESCE(CONCAT(u.nombre, ' ', u.apellido), 'Sin Asesor'),
                    COUNT(c.id),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA >= 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA < 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NULL OR TRIM(c.textoTranscription) = '' THEN 1 ELSE 0 END)
                )
                FROM ClienteResidencial c
                JOIN c.usuario u
                JOIN u.coordinador coord
                WHERE coord.id = :coordinadorId AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                GROUP BY u.nombre, u.apellido
                ORDER BY u.nombre, u.apellido
                """)
        List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionPorAsesorCoordinadorYRangoFechas(
                @Param("coordinadorId") Long coordinadorId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin
        );

        /**
         * Obtiene estadísticas de transcripciones por asesor filtrado por coordinador, sede y rango de fechas
         */
        @Query("""
                SELECT new com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO(
                    COALESCE(CONCAT(u.nombre, ' ', u.apellido), 'Sin Asesor'),
                    COUNT(c.id),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA >= 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NOT NULL AND TRIM(c.textoTranscription) != '' AND c.notaAgenteComparadorIA < 50.0 THEN 1 ELSE 0 END),
                    SUM(CASE WHEN c.textoTranscription IS NULL OR TRIM(c.textoTranscription) = '' THEN 1 ELSE 0 END)
                )
                FROM ClienteResidencial c
                JOIN c.usuario u
                JOIN u.coordinador coord
                LEFT JOIN u.sede s
                WHERE coord.id = :coordinadorId AND s.id = :sedeId AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
                GROUP BY u.nombre, u.apellido
                ORDER BY u.nombre, u.apellido
                """)
        List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionPorAsesorCoordinadorSedeYRangoFechas(
                @Param("coordinadorId") Long coordinadorId,
                @Param("sedeId") Long sedeId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin
        );

        // ===== MÉTODOS PARA LEADS FILTRADOS POR COORDINADOR Y TIPO DE INTERÉS =====

        /**
         * Obtiene leads específicos de un coordinador filtrados por tipo de interés para una fecha determinada
         * Maneja coordinadores específicos y "TODOS LOS COORDINADORES"
         */
        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) " +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "LEFT JOIN u.sede s " +
                "WHERE (:nombreCoordinador = 'TODOS LOS COORDINADORES' OR " +
                "       :nombreCoordinador LIKE 'TODOS LOS COORDINADORES DE %' OR " +
                "       CONCAT(c.nombre, ' ', c.apellido) = :nombreCoordinador) " +
                "AND (:sedeId IS NULL OR s.id = :sedeId) " +
                "AND DATE(cr.fechaCreacion) = :fecha " +
                "AND (:numeroMovil IS NULL OR :numeroMovil = '' OR cr.movilContacto = :numeroMovil) " +
                "AND ((:campoInteres = 'interesadosSeguro' AND cr.autorizaSeguros = true) OR " +
                "     (:campoInteres = 'interesadosEnergia' AND cr.autorizaEnergias = true) OR " +
                "     (:campoInteres = 'interesadosLowi' AND cr.deseaPromocionesLowi = true)) " +
                "ORDER BY cr.fechaCreacion DESC")
        Page<ClienteConUsuarioDTO> obtenerLeadsPorCoordinadorYTipoInteres(
                @Param("nombreCoordinador") String nombreCoordinador,
                @Param("campoInteres") String campoInteres,
                @Param("fecha") LocalDate fecha,
                @Param("sedeId") Long sedeId,
                @Param("numeroMovil") String numeroMovil,
                Pageable pageable
        );

        /**
         * Obtiene leads específicos de un coordinador filtrados por tipo de interés para un rango de fechas
         * Maneja coordinadores específicos y "TODOS LOS COORDINADORES"
         */
        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) " +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "LEFT JOIN u.sede s " +
                "WHERE (:nombreCoordinador = 'TODOS LOS COORDINADORES' OR " +
                "       :nombreCoordinador LIKE 'TODOS LOS COORDINADORES DE %' OR " +
                "       CONCAT(c.nombre, ' ', c.apellido) = :nombreCoordinador) " +
                "AND (:sedeId IS NULL OR s.id = :sedeId) " +
                "AND DATE(cr.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin " +
                "AND (:numeroMovil IS NULL OR :numeroMovil = '' OR cr.movilContacto = :numeroMovil) " +
                "AND ((:campoInteres = 'interesadosSeguro' AND cr.autorizaSeguros = true) OR " +
                "     (:campoInteres = 'interesadosEnergia' AND cr.autorizaEnergias = true) OR " +
                "     (:campoInteres = 'interesadosLowi' AND cr.deseaPromocionesLowi = true)) " +
                "ORDER BY cr.fechaCreacion DESC")
        Page<ClienteConUsuarioDTO> obtenerLeadsPorCoordinadorYTipoInteresRango(
                @Param("nombreCoordinador") String nombreCoordinador,
                @Param("campoInteres") String campoInteres,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                @Param("sedeId") Long sedeId,
                @Param("numeroMovil") String numeroMovil,
                Pageable pageable
        );


        @Query(value = """
    WITH leads_registrados_por_coord AS (
        SELECT u.coordinador_id, COUNT(c.id) as total_leads_registrados,
               SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
               SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
        FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
        WHERE c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
        GROUP BY u.coordinador_id
    ),
    audios_huerfanos_por_coord AS (
        SELECT AgentesActivos.coordinador_id, COUNT(DISTINCT asl.id) as total_audios_huerfanos
        FROM (
            SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
        ) AS AgentesActivos
        JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = AgentesActivos.numero_agente COLLATE utf8mb4_unicode_ci
        -- ===== NUEVA LÓGICA: SOLO CONTAR AUDIOS CON ASESOR IDENTIFICADO =====
        JOIN (
            SELECT c.numero_agente, DATE(c.fecha_creacion) as fecha_uso,
                   ROW_NUMBER() OVER (PARTITION BY c.numero_agente, DATE(c.fecha_creacion) ORDER BY c.fecha_creacion DESC) as rn
            FROM cliente_residencial c
            JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE c.numero_agente IS NOT NULL AND c.numero_agente != ''
        ) asesor_identificado ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = asesor_identificado.numero_agente COLLATE utf8mb4_unicode_ci
                               AND DATE(asl.fecha_extraida) = asesor_identificado.fecha_uso
                               AND asesor_identificado.rn = 1
        -- ===== FIN DE NUEVA LÓGICA =====
        GROUP BY AgentesActivos.coordinador_id
    )
    SELECT COALESCE(s.nombre, 'Sin Sede') as sede_nombre,
           CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre,
           coord.codi_usuario as coordinador_id,
           (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
           COALESCE(lr.total_leads_registrados, 0) as leads_registrado,
           COALESCE(lr.bien_registrado, 0) as bien_registrado,
           COALESCE(lr.mal_registrado, 0) as mal_registrado,
           COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
    FROM usuarios coord
    LEFT JOIN sedes s ON coord.sede_id = s.id
    LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
    LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
    WHERE coord.role = 'COORDINADOR'
      AND (lr.total_leads_registrados IS NOT NULL OR ah.total_audios_huerfanos IS NOT NULL)
    ORDER BY sede_nombre, coordinador_nombre
    """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionConAudiosHuerfanos();


        @Query(value = """
    WITH leads_registrados_por_coord AS (
        SELECT u.coordinador_id, COUNT(c.id) as total_leads_registrados,
               SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
               SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
        FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
        WHERE u.sede_id = :sedeId AND c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
        GROUP BY u.coordinador_id
    ),
    audios_huerfanos_por_coord AS (
        SELECT AgentesActivos.coordinador_id, COUNT(DISTINCT asl.id) as total_audios_huerfanos
        FROM (
            SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE u.sede_id = :sedeId
        ) AS AgentesActivos
        JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = AgentesActivos.numero_agente COLLATE utf8mb4_unicode_ci
        -- ===== NUEVA LÓGICA: SOLO CONTAR AUDIOS CON ASESOR IDENTIFICADO =====
        JOIN (
            SELECT c.numero_agente, DATE(c.fecha_creacion) as fecha_uso,
                   ROW_NUMBER() OVER (PARTITION BY c.numero_agente, DATE(c.fecha_creacion) ORDER BY c.fecha_creacion DESC) as rn
            FROM cliente_residencial c
            JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE c.numero_agente IS NOT NULL AND c.numero_agente != ''
        ) asesor_identificado ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = asesor_identificado.numero_agente COLLATE utf8mb4_unicode_ci
                               AND DATE(asl.fecha_extraida) = asesor_identificado.fecha_uso
                               AND asesor_identificado.rn = 1
        -- ===== FIN DE NUEVA LÓGICA =====
        GROUP BY AgentesActivos.coordinador_id
    )
    SELECT COALESCE(s.nombre, 'Sin Sede') as sede_nombre,
           CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre,
           coord.codi_usuario as coordinador_id,
           (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
           COALESCE(lr.total_leads_registrados, 0) as leads_registrado,
           COALESCE(lr.bien_registrado, 0) as bien_registrado,
           COALESCE(lr.mal_registrado, 0) as mal_registrado,
           COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
    FROM usuarios coord
    LEFT JOIN sedes s ON coord.sede_id = s.id
    LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
    LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
    WHERE coord.role = 'COORDINADOR' AND coord.sede_id = :sedeId
      AND (lr.total_leads_registrados IS NOT NULL OR ah.total_audios_huerfanos IS NOT NULL)
    ORDER BY sede_nombre, coordinador_nombre
    """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSede(@Param("sedeId") Long sedeId);



        @Query(value = """
    WITH leads_registrados_por_coord AS (
        SELECT u.coordinador_id, COUNT(c.id) as total_leads_registrados,
               SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
               SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
        FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
        WHERE DATE(c.fecha_creacion) = :fecha AND c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
        GROUP BY u.coordinador_id
    ),
    audios_huerfanos_por_coord AS (
        SELECT AgentesActivos.coordinador_id, COUNT(DISTINCT asl.id) as total_audios_huerfanos
        FROM (
            SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE DATE(c.fecha_creacion) = :fecha
        ) AS AgentesActivos
        JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = AgentesActivos.numero_agente COLLATE utf8mb4_unicode_ci
        -- ===== NUEVA LÓGICA: SOLO CONTAR AUDIOS CON ASESOR IDENTIFICADO =====
        JOIN (
            SELECT c.numero_agente, DATE(c.fecha_creacion) as fecha_uso,
                   ROW_NUMBER() OVER (PARTITION BY c.numero_agente, DATE(c.fecha_creacion) ORDER BY c.fecha_creacion DESC) as rn
            FROM cliente_residencial c
            JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE c.numero_agente IS NOT NULL AND c.numero_agente != ''
        ) asesor_identificado ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = asesor_identificado.numero_agente COLLATE utf8mb4_unicode_ci
                               AND DATE(asl.fecha_extraida) = asesor_identificado.fecha_uso
                               AND asesor_identificado.rn = 1
        -- ===== FIN DE NUEVA LÓGICA =====
        WHERE DATE(asl.fecha_extraida) = :fecha
        GROUP BY AgentesActivos.coordinador_id
    )
    SELECT COALESCE(s.nombre, 'Sin Sede') as sede_nombre,
           CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre,
           coord.codi_usuario as coordinador_id,
           (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
           COALESCE(lr.total_leads_registrados, 0) as leads_registrado,
           COALESCE(lr.bien_registrado, 0) as bien_registrado,
           COALESCE(lr.mal_registrado, 0) as mal_registrado,
           COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
    FROM usuarios coord
    LEFT JOIN sedes s ON coord.sede_id = s.id
    LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
    LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
    WHERE coord.role = 'COORDINADOR'
      AND (lr.total_leads_registrados IS NOT NULL OR ah.total_audios_huerfanos IS NOT NULL)
    ORDER BY sede_nombre, coordinador_nombre
    """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorFecha(@Param("fecha") LocalDate fecha);

        /**
         * LÓGICA CENTRALIZADA Y CORREGIDA (FILTRADO POR SEDE Y FECHA)
         */
        @Query(value = """
    WITH leads_registrados_por_coord AS (
        SELECT u.coordinador_id, COUNT(c.id) as total_leads_registrados,
               SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
               SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
        FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
        WHERE u.sede_id = :sedeId AND DATE(c.fecha_creacion) = :fecha 
          AND c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
        GROUP BY u.coordinador_id
    ),
    audios_huerfanos_por_coord AS (
        SELECT AgentesActivos.coordinador_id, COUNT(DISTINCT asl.id) as total_audios_huerfanos
        FROM (
            SELECT DISTINCT u.coordinador_id, c.numero_agente FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE u.sede_id = :sedeId AND DATE(c.fecha_creacion) = :fecha
        ) AS AgentesActivos
        JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = AgentesActivos.numero_agente COLLATE utf8mb4_unicode_ci
        JOIN (
            SELECT c.numero_agente, DATE(c.fecha_creacion) as fecha_uso,
                   ROW_NUMBER() OVER (PARTITION BY c.numero_agente, DATE(c.fecha_creacion) ORDER BY c.fecha_creacion DESC) as rn
            FROM cliente_residencial c
            JOIN usuarios u ON c.usuario_id = u.codi_usuario
            WHERE c.numero_agente IS NOT NULL AND c.numero_agente != ''
        ) asesor_identificado ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = asesor_identificado.numero_agente COLLATE utf8mb4_unicode_ci
                               AND DATE(asl.fecha_extraida) = asesor_identificado.fecha_uso
                               AND asesor_identificado.rn = 1
        WHERE DATE(asl.fecha_extraida) = :fecha
        GROUP BY AgentesActivos.coordinador_id
    )
    SELECT COALESCE(s.nombre, 'Sin Sede') as sede_nombre,
           CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre,
           coord.codi_usuario as coordinador_id,
           (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
           COALESCE(lr.total_leads_registrados, 0) as leads_registrado,
           COALESCE(lr.bien_registrado, 0) as bien_registrado,
           COALESCE(lr.mal_registrado, 0) as mal_registrado,
           COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
    FROM usuarios coord
    LEFT JOIN sedes s ON coord.sede_id = s.id
    LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
    LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
    WHERE coord.role = 'COORDINADOR' AND coord.sede_id = :sedeId
      AND (lr.total_leads_registrados IS NOT NULL OR ah.total_audios_huerfanos IS NOT NULL)
    ORDER BY sede_nombre, coordinador_nombre
    """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYFecha(
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha
        );

        @Query(value = """
    WITH coordinadores_activos AS (
        -- Identificar coordinadores que tienen actividad en el rango de fechas
        SELECT DISTINCT coord.codi_usuario as coordinador_id,
               CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre,
               COALESCE(s.nombre, 'Sin Sede') as sede_nombre
        FROM usuarios coord
        LEFT JOIN sedes s ON coord.sede_id = s.id
        WHERE coord.role = 'COORDINADOR'
    ),
    leads_registrados_por_coord AS (
        SELECT u.coordinador_id, 
               COUNT(c.id) as total_leads_registrados,
               SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
               SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
        FROM cliente_residencial c 
        JOIN usuarios u ON c.usuario_id = u.codi_usuario
        WHERE DATE(c.fecha_creacion) BETWEEN :fechaInicio AND :fechaFin 
          AND c.nombre_archivo_mp3 IS NOT NULL 
          AND c.nombre_archivo_mp3 <> ''
        GROUP BY u.coordinador_id
    ),
    audios_huerfanos_por_coord AS (
        -- VERSIÓN OPTIMIZADA: Contar directamente sin detalles complejos
        SELECT u.coordinador_id, COUNT(DISTINCT asl.id) as total_audios_huerfanos
        FROM audio_sin_lead asl
        INNER JOIN cliente_residencial c ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = c.numero_agente COLLATE utf8mb4_unicode_ci
                                         AND DATE(asl.fecha_extraida) = DATE(c.fecha_creacion)
        INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
        WHERE DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin
          AND c.numero_agente IS NOT NULL
          AND c.numero_agente != ''
        GROUP BY u.coordinador_id
    )
    SELECT ca.sede_nombre,
           ca.coordinador_nombre,
           ca.coordinador_id,
           (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
           COALESCE(lr.total_leads_registrados, 0) as leads_registrado,
           COALESCE(lr.bien_registrado, 0) as bien_registrado,
           COALESCE(lr.mal_registrado, 0) as mal_registrado,
           COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
    FROM coordinadores_activos ca
    LEFT JOIN leads_registrados_por_coord lr ON ca.coordinador_id = lr.coordinador_id
    LEFT JOIN audios_huerfanos_por_coord ah ON ca.coordinador_id = ah.coordinador_id
    WHERE (lr.total_leads_registrados IS NOT NULL OR ah.total_audios_huerfanos IS NOT NULL)
    ORDER BY ca.sede_nombre, ca.coordinador_nombre
    """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorRangoFechas(
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin
        );
        /**
         * NUEVA LÓGICA: Obtiene estadísticas por coordinador CON AUDIOS HUÉRFANOS filtrado por sede y rango de fechas
         * CORREGIDO: Se asegura que los leads registrados tengan audio y la cuenta de huérfanos sea precisa.
         */
        @Query(value = """
    WITH leads_registrados_por_coord AS (
        SELECT u.coordinador_id, COUNT(c.id) as total_leads_registrados,
               SUM(CASE WHEN c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
               SUM(CASE WHEN c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
        FROM cliente_residencial c JOIN usuarios u ON c.usuario_id = u.codi_usuario
        WHERE u.sede_id = :sedeId AND DATE(c.fecha_creacion) BETWEEN :fechaInicio AND :fechaFin
          AND c.nombre_archivo_mp3 IS NOT NULL AND c.nombre_archivo_mp3 <> ''
        GROUP BY u.coordinador_id
    ),
    audios_huerfanos_por_coord AS (
        -- VERSIÓN OPTIMIZADA: Contar directamente sin detalles complejos
        SELECT u.coordinador_id, COUNT(DISTINCT asl.id) as total_audios_huerfanos
        FROM audio_sin_lead asl
        INNER JOIN cliente_residencial c ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = c.numero_agente COLLATE utf8mb4_unicode_ci
                                         AND DATE(asl.fecha_extraida) = DATE(c.fecha_creacion)
        INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
        INNER JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario
        WHERE DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin
          AND coord.sede_id = :sedeId
          AND c.numero_agente IS NOT NULL
          AND c.numero_agente != ''
        GROUP BY u.coordinador_id
    )
    SELECT COALESCE(s.nombre, 'Sin Sede') as sede_nombre,
           CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre,
           coord.codi_usuario as coordinador_id,
           (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
           COALESCE(lr.total_leads_registrados, 0) as leads_registrado,
           COALESCE(lr.bien_registrado, 0) as bien_registrado,
           COALESCE(lr.mal_registrado, 0) as mal_registrado,
           COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
    FROM usuarios coord
    LEFT JOIN sedes s ON coord.sede_id = s.id
    LEFT JOIN leads_registrados_por_coord lr ON coord.codi_usuario = lr.coordinador_id
    LEFT JOIN audios_huerfanos_por_coord ah ON coord.codi_usuario = ah.coordinador_id
    WHERE coord.role = 'COORDINADOR' AND coord.sede_id = :sedeId
      AND (lr.total_leads_registrados IS NOT NULL OR ah.total_audios_huerfanos IS NOT NULL)
    ORDER BY sede_nombre, coordinador_nombre
    """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYRangoFechas(
                @Param("sedeId") Long sedeId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin
        );
        // ===== NUEVOS MÉTODOS PARA ESTADÍSTICAS DE ASESORES CON AUDIOS HUÉRFANOS =====

        /**
         * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS
         * Incluye audios de Google Drive que no tienen lead correspondiente
         *
         * LEADS CONTACTO = Total de audios de Google Drive del asesor (registrados + audios sin lead)
         * LEADS REGISTRADO = Solo audios que tienen lead en cliente_residencial
         * BIEN REGISTRADO = Leads con nota_agente_comparador_ia >= 50%
         * MAL REGISTRADO = Leads con nota_agente_comparador_ia < 50%
         * NO REGISTRADO = Audios en audio_sin_lead de números de agente del asesor
         *
         * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
         */
        @Query(value = """
                WITH asesor_stats AS (
                    SELECT
                        CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre,
                        u.codi_usuario as asesor_id
                    FROM usuarios u
                    WHERE u.role = 'ASESOR'
                ),
                leads_registrados AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT c.id) as total_leads_registrados,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' THEN 1 ELSE 0 END) as leads_con_transcripcion,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    GROUP BY ases.asesor_nombre
                ),
                audios_huerfanos AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT asl.id) as total_audios_huerfanos
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    LEFT JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = c.numero_agente COLLATE utf8mb4_unicode_ci
                    WHERE c.numero_agente IS NOT NULL AND c.numero_agente != ''
                    GROUP BY ases.asesor_nombre
                )
                SELECT
                    lr.asesor_nombre,
                    (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
                    COALESCE(lr.leads_con_transcripcion, 0) as leads_registrado,
                    COALESCE(lr.bien_registrado, 0) as bien_registrado,
                    COALESCE(lr.mal_registrado, 0) as mal_registrado,
                    COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
                FROM leads_registrados lr
                LEFT JOIN audios_huerfanos ah ON lr.asesor_nombre = ah.asesor_nombre
                WHERE (lr.total_leads_registrados > 0 OR ah.total_audios_huerfanos > 0)
                ORDER BY lr.asesor_nombre
                """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanos();

        /**
         * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por sede
         *
         * @param sedeId ID de la sede para filtrar
         * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
         */
        @Query(value = """
                WITH asesor_stats AS (
                    SELECT
                        CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre,
                        u.codi_usuario as asesor_id
                    FROM usuarios u
                    LEFT JOIN sedes s ON u.sede_id = s.id
                    WHERE u.role = 'ASESOR' AND s.id = :sedeId
                ),
                leads_registrados AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT c.id) as total_leads_registrados,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' THEN 1 ELSE 0 END) as leads_con_transcripcion,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    GROUP BY ases.asesor_nombre
                ),
                audios_huerfanos AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT asl.id) as total_audios_huerfanos
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    LEFT JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = c.numero_agente COLLATE utf8mb4_unicode_ci
                    WHERE c.numero_agente IS NOT NULL AND c.numero_agente != ''
                    GROUP BY ases.asesor_nombre
                )
                SELECT
                    lr.asesor_nombre,
                    (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
                    COALESCE(lr.leads_con_transcripcion, 0) as leads_registrado,
                    COALESCE(lr.bien_registrado, 0) as bien_registrado,
                    COALESCE(lr.mal_registrado, 0) as mal_registrado,
                    COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
                FROM leads_registrados lr
                LEFT JOIN audios_huerfanos ah ON lr.asesor_nombre = ah.asesor_nombre
                WHERE (lr.total_leads_registrados > 0 OR ah.total_audios_huerfanos > 0)
                ORDER BY lr.asesor_nombre
                """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSede(@Param("sedeId") Long sedeId);

        /**
         * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por fecha específica
         *
         * @param fecha Fecha específica para filtrar
         * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
         */
        @Query(value = """
                WITH asesor_stats AS (
                    SELECT
                        CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre,
                        u.codi_usuario as asesor_id
                    FROM usuarios u
                    WHERE u.role = 'ASESOR'
                ),
                leads_registrados AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT c.id) as total_leads_registrados,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' THEN 1 ELSE 0 END) as leads_con_transcripcion,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    WHERE DATE(c.fecha_creacion) = :fecha
                    GROUP BY ases.asesor_nombre
                ),
                audios_huerfanos AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT asl.id) as total_audios_huerfanos
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    LEFT JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = c.numero_agente COLLATE utf8mb4_unicode_ci
                    WHERE DATE(asl.fecha_extraida) = :fecha AND c.numero_agente IS NOT NULL AND c.numero_agente != ''
                    GROUP BY ases.asesor_nombre
                )
                SELECT
                    lr.asesor_nombre,
                    (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
                    COALESCE(lr.leads_con_transcripcion, 0) as leads_registrado,
                    COALESCE(lr.bien_registrado, 0) as bien_registrado,
                    COALESCE(lr.mal_registrado, 0) as mal_registrado,
                    COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
                FROM leads_registrados lr
                LEFT JOIN audios_huerfanos ah ON lr.asesor_nombre = ah.asesor_nombre
                WHERE (lr.total_leads_registrados > 0 OR ah.total_audios_huerfanos > 0)
                ORDER BY lr.asesor_nombre
                """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorFecha(@Param("fecha") LocalDate fecha);

        /**
         * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por sede y fecha específica
         *
         * @param sedeId ID de la sede para filtrar
         * @param fecha Fecha específica para filtrar
         * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
         */
        @Query(value = """
                WITH asesor_stats AS (
                    SELECT
                        CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre,
                        u.codi_usuario as asesor_id
                    FROM usuarios u
                    LEFT JOIN sedes s ON u.sede_id = s.id
                    WHERE u.role = 'ASESOR' AND s.id = :sedeId
                ),
                leads_registrados AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT c.id) as total_leads_registrados,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' THEN 1 ELSE 0 END) as leads_con_transcripcion,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    WHERE DATE(c.fecha_creacion) = :fecha
                    GROUP BY ases.asesor_nombre
                ),
                audios_huerfanos AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT asl.id) as total_audios_huerfanos
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    LEFT JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = c.numero_agente COLLATE utf8mb4_unicode_ci
                    WHERE DATE(asl.fecha_extraida) = :fecha AND c.numero_agente IS NOT NULL AND c.numero_agente != ''
                    GROUP BY ases.asesor_nombre
                )
                SELECT
                    lr.asesor_nombre,
                    (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
                    COALESCE(lr.leads_con_transcripcion, 0) as leads_registrado,
                    COALESCE(lr.bien_registrado, 0) as bien_registrado,
                    COALESCE(lr.mal_registrado, 0) as mal_registrado,
                    COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
                FROM leads_registrados lr
                LEFT JOIN audios_huerfanos ah ON lr.asesor_nombre = ah.asesor_nombre
                WHERE (lr.total_leads_registrados > 0 OR ah.total_audios_huerfanos > 0)
                ORDER BY lr.asesor_nombre
                """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYFecha(
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha
        );

        /**
         * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por rango de fechas
         *
         * @param fechaInicio Fecha de inicio del rango
         * @param fechaFin Fecha de fin del rango
         * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
         */
        @Query(value = """
                WITH asesor_stats AS (
                    SELECT
                        CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre,
                        u.codi_usuario as asesor_id
                    FROM usuarios u
                    WHERE u.role = 'ASESOR'
                ),
                leads_registrados AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT c.id) as total_leads_registrados,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' THEN 1 ELSE 0 END) as leads_con_transcripcion,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    WHERE DATE(c.fecha_creacion) BETWEEN :fechaInicio AND :fechaFin
                    GROUP BY ases.asesor_nombre
                ),
                audios_huerfanos AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT asl.id) as total_audios_huerfanos
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    LEFT JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = c.numero_agente COLLATE utf8mb4_unicode_ci
                    WHERE DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin AND c.numero_agente IS NOT NULL AND c.numero_agente != ''
                    GROUP BY ases.asesor_nombre
                )
                SELECT
                    lr.asesor_nombre,
                    (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
                    COALESCE(lr.leads_con_transcripcion, 0) as leads_registrado,
                    COALESCE(lr.bien_registrado, 0) as bien_registrado,
                    COALESCE(lr.mal_registrado, 0) as mal_registrado,
                    COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
                FROM leads_registrados lr
                LEFT JOIN audios_huerfanos ah ON lr.asesor_nombre = ah.asesor_nombre
                WHERE (lr.total_leads_registrados > 0 OR ah.total_audios_huerfanos > 0)
                ORDER BY lr.asesor_nombre
                """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorRangoFechas(
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin
        );

        /**
         * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por sede y rango de fechas
         *
         * @param sedeId ID de la sede para filtrar
         * @param fechaInicio Fecha de inicio del rango
         * @param fechaFin Fecha de fin del rango
         * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
         */
        @Query(value = """
                WITH asesor_stats AS (
                    SELECT
                        CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre,
                        u.codi_usuario as asesor_id
                    FROM usuarios u
                    LEFT JOIN sedes s ON u.sede_id = s.id
                    WHERE u.role = 'ASESOR' AND s.id = :sedeId
                ),
                leads_registrados AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT c.id) as total_leads_registrados,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' THEN 1 ELSE 0 END) as leads_con_transcripcion,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia >= 50.0 THEN 1 ELSE 0 END) as bien_registrado,
                        SUM(CASE WHEN c.texto_transcription IS NOT NULL AND TRIM(c.texto_transcription) != '' AND c.nota_agente_comparador_ia < 50.0 THEN 1 ELSE 0 END) as mal_registrado
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    WHERE DATE(c.fecha_creacion) BETWEEN :fechaInicio AND :fechaFin
                    GROUP BY ases.asesor_nombre
                ),
                audios_huerfanos AS (
                    SELECT
                        ases.asesor_nombre,
                        COUNT(DISTINCT asl.id) as total_audios_huerfanos
                    FROM asesor_stats ases
                    LEFT JOIN cliente_residencial c ON c.usuario_id = ases.asesor_id
                    LEFT JOIN audio_sin_lead asl ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = c.numero_agente COLLATE utf8mb4_unicode_ci
                    WHERE DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin AND c.numero_agente IS NOT NULL AND c.numero_agente != ''
                    GROUP BY ases.asesor_nombre
                )
                SELECT
                    lr.asesor_nombre,
                    (COALESCE(lr.total_leads_registrados, 0) + COALESCE(ah.total_audios_huerfanos, 0)) as leads_contacto,
                    COALESCE(lr.leads_con_transcripcion, 0) as leads_registrado,
                    COALESCE(lr.bien_registrado, 0) as bien_registrado,
                    COALESCE(lr.mal_registrado, 0) as mal_registrado,
                    COALESCE(ah.total_audios_huerfanos, 0) as no_registrado
                FROM leads_registrados lr
                LEFT JOIN audios_huerfanos ah ON lr.asesor_nombre = ah.asesor_nombre
                WHERE (lr.total_leads_registrados > 0 OR ah.total_audios_huerfanos > 0)
                ORDER BY lr.asesor_nombre
                """, nativeQuery = true)
        List<Object[]> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYRangoFechas(
                @Param("sedeId") Long sedeId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin
        );

        // ===== MÉTODOS PARA OBTENER DETALLES DE MÉTRICAS CON AUDIOS HUÉRFANOS =====

        /**
         * COORDINADORES: Obtiene leads registrados (BIEN REGISTRADO) de un coordinador específico
         * Leads con nota_agente_comparador_ia >= 50%
         */
        // Reemplaza el método obtenerLeadsBienRegistradosPorCoordinador existente

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END, " +
                // --- INICIO DE CAMPOS NUEVOS ---
                "cr.id, " +
                "cr.urlDriveTranscripcion" +
                // ---- FIN DE CAMPOS NUEVOS ----
                ") " +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "LEFT JOIN u.sede s " +
                "WHERE CONCAT(c.nombre, ' ', c.apellido) = :nombreCoordinador " +
                "AND (:sedeId IS NULL OR s.id = :sedeId) " +
                "AND (:fecha IS NULL OR DATE(cr.fechaCreacion) = :fecha) " +
                "AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(cr.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin) " +
                "AND cr.notaAgenteComparadorIA IS NOT NULL " +
                "AND cr.notaAgenteComparadorIA >= 50.0 " +
                "ORDER BY cr.fechaCreacion DESC")
        Page<ClienteConUsuarioDTO> obtenerLeadsBienRegistradosPorCoordinador(
                @Param("nombreCoordinador") String nombreCoordinador,
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable
        );
        /**
         * COORDINADORES: Obtiene leads registrados (MAL REGISTRADO) de un coordinador específico
         * Leads con nota_agente_comparador_ia < 50%
         */
        // Reemplaza el método obtenerLeadsMalRegistradosPorCoordinador existente

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END, " +
                // --- INICIO DE CAMPOS NUEVOS ---
                "cr.id, " +
                "cr.urlDriveTranscripcion" +
                // ---- FIN DE CAMPOS NUEVOS ----
                ") " +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "LEFT JOIN u.sede s " +
                "WHERE CONCAT(c.nombre, ' ', c.apellido) = :nombreCoordinador " +
                "AND (:sedeId IS NULL OR s.id = :sedeId) " +
                "AND (:fecha IS NULL OR DATE(cr.fechaCreacion) = :fecha) " +
                "AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(cr.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin) " +
                "AND cr.notaAgenteComparadorIA IS NOT NULL " +
                "AND cr.notaAgenteComparadorIA < 50.0 " +
                "ORDER BY cr.fechaCreacion DESC")
        Page<ClienteConUsuarioDTO> obtenerLeadsMalRegistradosPorCoordinador(
                @Param("nombreCoordinador") String nombreCoordinador,
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable
        );

        /**
         * COORDINADORES: Obtiene todos los leads registrados (LEADS REGISTRADO) de un coordinador específico.
         * CORRECTO: Filtra TODOS los leads que tienen un audio asociado en Google Drive.
         */
        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END, " +
                "cr.id, " +
                "cr.urlDriveTranscripcion" +
                ") " +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "JOIN u.coordinador c " +
                "LEFT JOIN u.sede s " +
                "WHERE CONCAT(c.nombre, ' ', c.apellido) = :nombreCoordinador " +
                "AND cr.nombreArchivoMp3 IS NOT NULL AND cr.nombreArchivoMp3 <> '' " +
                "AND (:sedeId IS NULL OR s.id = :sedeId) " +
                "AND (:fecha IS NULL OR DATE(cr.fechaCreacion) = :fecha) " +
                "AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(cr.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin) " +
                "ORDER BY cr.fechaCreacion DESC")
        Page<ClienteConUsuarioDTO> obtenerLeadsRegistradosPorCoordinador(
                @Param("nombreCoordinador") String nombreCoordinador,
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable
        );

        /**
         * ASESORES: Obtiene leads registrados (BIEN REGISTRADO) de un asesor específico
         * Leads con nota_agente_comparador_ia >= 50%
         */
        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) " +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "LEFT JOIN u.sede s " +
                "WHERE CONCAT(u.nombre, ' ', u.apellido) = :nombreAsesor " +
                "AND (:sedeId IS NULL OR s.id = :sedeId) " +
                "AND (:fecha IS NULL OR DATE(cr.fechaCreacion) = :fecha) " +
                "AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(cr.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin) " +
                "AND cr.notaAgenteComparadorIA IS NOT NULL " +
                "AND cr.notaAgenteComparadorIA >= 50.0 " +
                "ORDER BY cr.fechaCreacion DESC")
        Page<ClienteConUsuarioDTO> obtenerLeadsBienRegistradosPorAsesor(
                @Param("nombreAsesor") String nombreAsesor,
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable
        );

        /**
         * ASESORES: Obtiene leads registrados (MAL REGISTRADO) de un asesor específico
         * Leads con nota_agente_comparador_ia < 50%
         */
        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) " +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "LEFT JOIN u.sede s " +
                "WHERE CONCAT(u.nombre, ' ', u.apellido) = :nombreAsesor " +
                "AND (:sedeId IS NULL OR s.id = :sedeId) " +
                "AND (:fecha IS NULL OR DATE(cr.fechaCreacion) = :fecha) " +
                "AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(cr.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin) " +
                "AND cr.notaAgenteComparadorIA IS NOT NULL " +
                "AND cr.notaAgenteComparadorIA < 50.0 " +
                "ORDER BY cr.fechaCreacion DESC")
        Page<ClienteConUsuarioDTO> obtenerLeadsMalRegistradosPorAsesor(
                @Param("nombreAsesor") String nombreAsesor,
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable
        );

        /**
         * ASESORES: Obtiene todos los leads registrados (LEADS REGISTRADO) de un asesor específico
         * Todos los leads que tienen registro en cliente_residencial
         */
        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) " +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "LEFT JOIN u.sede s " +
                "WHERE CONCAT(u.nombre, ' ', u.apellido) = :nombreAsesor " +
                "AND (:sedeId IS NULL OR s.id = :sedeId) " +
                "AND (:fecha IS NULL OR DATE(cr.fechaCreacion) = :fecha) " +
                "AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(cr.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin) " +
                "ORDER BY cr.fechaCreacion DESC")
        Page<ClienteConUsuarioDTO> obtenerLeadsRegistradosPorAsesor(
                @Param("nombreAsesor") String nombreAsesor,
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable
        );

        // Archivo: ClienteResidencialRepository.java
// REEMPLAZA el método obtenerAudiosHuerfanosPorCoordinador con esta versión FINAL

        @Query(value = """
    -- Consulta principal que une audios huérfanos con el asesor más probable
    SELECT
        asl.agente_extraido as agente,
        ap.asesor_nombre as nombre_asesor,
        asl.fecha_extraida as fecha_creacion,
        asl.movil_extraido as movil_contacto,
        ap.coordinador_nombre as nombre_coordinador,
        asl.nombre_archivo,
        ap.last_lead_id as lead_id_referencia,
        ap.asesor_nombre as lead_nombre_asesor_referencia,
        ap.coordinador_nombre as lead_nombre_coordinador_referencia,
        cr_ref.movil_contacto as lead_telefono_referencia,
        cr_ref.fecha_creacion as lead_fecha_referencia
    FROM audio_sin_lead asl

    -- 1. Identifica los agentes que pertenecen al coordinador solicitado
    INNER JOIN (
        SELECT DISTINCT
            c.numero_agente,
            CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre
        FROM cliente_residencial c
        INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
        INNER JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario
        LEFT JOIN sedes s ON u.sede_id = s.id
        WHERE CONCAT(coord.nombre, ' ', coord.apellido) = :nombreCoordinador
          AND (:sedeId IS NULL OR s.id = :sedeId)
          AND c.numero_agente IS NOT NULL AND c.numero_agente <> ''
    ) coord_agentes ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = coord_agentes.numero_agente COLLATE utf8mb4_unicode_ci

    -- 2. Identifica al asesor principal para cada agente en un día específico
    INNER JOIN (
        -- Subconsulta para encontrar al asesor más probable (SOLO del coordinador solicitado)
        WITH AsesorConteo AS (
            SELECT
                c.numero_agente,
                DATE(c.fecha_creacion) as fecha_uso,
                CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre,
                CONCAT(coord.nombre, ' ', coord.apellido) as coordinador_nombre,
                COUNT(*) as cantidad_leads,
                MAX(c.id) as last_lead_id -- ID del último lead para desempate y referencia
            FROM cliente_residencial c
            INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
            INNER JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario
            LEFT JOIN sedes s ON u.sede_id = s.id
            WHERE c.numero_agente IS NOT NULL AND c.numero_agente <> ''
              AND CONCAT(coord.nombre, ' ', coord.apellido) = :nombreCoordinador  -- ✅ FILTRAR SOLO ASESORES DEL COORDINADOR
              AND (:sedeId IS NULL OR s.id = :sedeId)
            GROUP BY c.numero_agente, DATE(c.fecha_creacion), CONCAT(u.nombre, ' ', u.apellido), CONCAT(coord.nombre, ' ', coord.apellido)
        ),
        AsesorPrincipal AS (
            SELECT *,
                   ROW_NUMBER() OVER (
                       PARTITION BY numero_agente, fecha_uso
                       ORDER BY cantidad_leads DESC, last_lead_id DESC -- Desempate con el último lead
                   ) as rn
            FROM AsesorConteo
        )
        SELECT * FROM AsesorPrincipal WHERE rn = 1
    ) ap ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = ap.numero_agente COLLATE utf8mb4_unicode_ci
        AND DATE(asl.fecha_extraida) = ap.fecha_uso

    -- 3. (Opcional) Trae datos del lead usado como referencia para mayor contexto
    LEFT JOIN cliente_residencial cr_ref ON ap.last_lead_id = cr_ref.id

    WHERE (:fecha IS NULL OR DATE(asl.fecha_extraida) = :fecha)
      AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin)
    ORDER BY asl.fecha_extraida DESC
""",
                countQuery = """
    -- Count Query que refleja la misma lógica para una paginación precisa
    SELECT COUNT(asl.id)
    FROM audio_sin_lead asl
    INNER JOIN (
        SELECT DISTINCT c.numero_agente
        FROM cliente_residencial c
        INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
        INNER JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario
        LEFT JOIN sedes s ON u.sede_id = s.id
        WHERE CONCAT(coord.nombre, ' ', coord.apellido) = :nombreCoordinador
          AND (:sedeId IS NULL OR s.id = :sedeId)
          AND c.numero_agente IS NOT NULL AND c.numero_agente <> ''
    ) coord_agentes ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = coord_agentes.numero_agente COLLATE utf8mb4_unicode_ci
    INNER JOIN (
        -- Replicamos la lógica para asegurar que el audio tiene un asesor identificable ese día DEL COORDINADOR ESPECÍFICO
        SELECT DISTINCT c.numero_agente, DATE(c.fecha_creacion) as fecha_uso
        FROM cliente_residencial c
        INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
        INNER JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario
        LEFT JOIN sedes s ON u.sede_id = s.id
        WHERE c.numero_agente IS NOT NULL AND c.numero_agente <> ''
          AND CONCAT(coord.nombre, ' ', coord.apellido) = :nombreCoordinador
          AND (:sedeId IS NULL OR s.id = :sedeId)
    ) asesor_identificable ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = asesor_identificable.numero_agente COLLATE utf8mb4_unicode_ci
                           AND DATE(asl.fecha_extraida) = asesor_identificable.fecha_uso
    WHERE (:fecha IS NULL OR DATE(asl.fecha_extraida) = :fecha)
      AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin)
""", nativeQuery = true)
        Page<Object[]> obtenerAudiosHuerfanosPorCoordinador(
                @Param("nombreCoordinador") String nombreCoordinador,
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable
        );
// MODIFICACIÓN EN ClienteResidencialRepository.java
// Reemplaza el método obtenerAudiosHuerfanosPorAsesor existente

        @Query(value = """
    SELECT
        asl.agente_extraido as agente,
        asesor_mismo_dia.asesor_nombre as nombre_completo,
        asl.fecha_extraida as fecha_creacion,
        asl.movil_extraido as movil_contacto,
        CONCAT(u.nombre, ' ', u.apellido) as asesor,
        asl.nombre_archivo
    FROM audio_sin_lead asl
    INNER JOIN (
        SELECT DISTINCT
            c.numero_agente,
            u.codi_usuario,
            CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre_ref
        FROM cliente_residencial c
        INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
        LEFT JOIN sedes s ON u.sede_id = s.id
        WHERE u.role = 'ASESOR'
        AND CONCAT(u.nombre, ' ', u.apellido) = :nombreAsesor
        AND (:sedeId IS NULL OR s.id = :sedeId)
        AND c.numero_agente IS NOT NULL
        AND c.numero_agente != ''
    ) asesor_agentes ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = asesor_agentes.numero_agente COLLATE utf8mb4_unicode_ci
    INNER JOIN usuarios u ON asesor_agentes.codi_usuario = u.codi_usuario
    
    INNER JOIN (
        SELECT 
            c.numero_agente,
            CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre,
            DATE(c.fecha_creacion) as fecha_uso,
            ROW_NUMBER() OVER (PARTITION BY c.numero_agente, DATE(c.fecha_creacion) ORDER BY c.fecha_creacion DESC) as rn
        FROM cliente_residencial c
        INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
        WHERE c.numero_agente IS NOT NULL AND c.numero_agente != ''
    ) asesor_mismo_dia ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = asesor_mismo_dia.numero_agente COLLATE utf8mb4_unicode_ci
                       AND DATE(asl.fecha_extraida) = asesor_mismo_dia.fecha_uso
                       AND asesor_mismo_dia.rn = 1
    
    WHERE (:fecha IS NULL OR DATE(asl.fecha_extraida) = :fecha)
    AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin)
    AND asesor_mismo_dia.asesor_nombre IS NOT NULL  -- ESTA LÍNEA EXCLUYE LOS NO IDENTIFICADOS
    ORDER BY asl.fecha_extraida DESC
    """,
                countQuery = """
    SELECT COUNT(*)
    FROM audio_sin_lead asl
    INNER JOIN (
        SELECT DISTINCT c.numero_agente, u.codi_usuario
        FROM cliente_residencial c
        INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
        LEFT JOIN sedes s ON u.sede_id = s.id
        WHERE u.role = 'ASESOR'
        AND CONCAT(u.nombre, ' ', u.apellido) = :nombreAsesor
        AND (:sedeId IS NULL OR s.id = :sedeId)
        AND c.numero_agente IS NOT NULL AND c.numero_agente != ''
    ) asesor_agentes ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = asesor_agentes.numero_agente COLLATE utf8mb4_unicode_ci
    INNER JOIN usuarios u ON asesor_agentes.codi_usuario = u.codi_usuario
    INNER JOIN (
        SELECT 
            c.numero_agente,
            CONCAT(u.nombre, ' ', u.apellido) as asesor_nombre,
            DATE(c.fecha_creacion) as fecha_uso,
            ROW_NUMBER() OVER (PARTITION BY c.numero_agente, DATE(c.fecha_creacion) ORDER BY c.fecha_creacion DESC) as rn
        FROM cliente_residencial c
        INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario
        WHERE c.numero_agente IS NOT NULL AND c.numero_agente != ''
    ) asesor_mismo_dia ON asl.agente_extraido COLLATE utf8mb4_unicode_ci = asesor_mismo_dia.numero_agente COLLATE utf8mb4_unicode_ci
                       AND DATE(asl.fecha_extraida) = asesor_mismo_dia.fecha_uso
                       AND asesor_mismo_dia.rn = 1
    WHERE (:fecha IS NULL OR DATE(asl.fecha_extraida) = :fecha)
    AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin)
    AND asesor_mismo_dia.asesor_nombre IS NOT NULL  -- ESTA LÍNEA EXCLUYE LOS NO IDENTIFICADOS
    """,
                nativeQuery = true)
        Page<Object[]> obtenerAudiosHuerfanosPorAsesor(
                @Param("nombreAsesor") String nombreAsesor,
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable
        );

        /**
         * Para el servicio obtenerNumerosAgentePorCoordinador:
         * busca todos los leads de clientes cuyo usuario tenga coordinador con este nombre
         * y cuya fecha de creación esté entre from–to.
         */
        @Query("""
        SELECT c
          FROM ClienteResidencial c
         WHERE CONCAT(c.usuario.coordinador.nombre, ' ', c.usuario.coordinador.apellido) = :coordinadorNombre
           AND (:sedeId IS NULL OR c.usuario.sede.id = :sedeId)
           AND c.fechaCreacion BETWEEN :desde AND :hasta
    """)
        List<ClienteResidencial> findAllByCoordinadorAndFechas(
                @Param("coordinadorNombre") String coordinadorNombre,
                @Param("sedeId") Long sedeId,
                @Param("desde") LocalDateTime desde,
                @Param("hasta") LocalDateTime hasta
        );

        /**
         * Devuelve la lista de números de agente (distinct) de todos los clientes residenciales
         * cuya propiedad usuario.coordinador.id coincide con coordId,
         * y que pertenezcan a la sede dada.
         */
        @Query("""
      SELECT DISTINCT c.numeroAgente
        FROM ClienteResidencial c
        JOIN c.usuario u
       WHERE u.coordinador.id   = :coordId
         AND (:sedeId IS NULL OR u.sede.id = :sedeId)
         AND c.numeroAgente IS NOT NULL
         AND c.numeroAgente <> ''
    """)
        List<String> obtenerNumerosAgentePorCoordinador(
                @Param("coordId") Long coordId,
                @Param("sedeId")  Long sedeId     // si quieres todos los coordinadores de cualquier sede, pásale null
        );


        // Filtra por nombre de coordinador, por sede y por rango de fechaCreacion
        List<ClienteResidencial>
        findAllByUsuario_Coordinador_NombreAndUsuario_Sede_IdAndFechaCreacionBetween(
                String nombreCoordinador,
                Long sedeId,
                LocalDateTime desde,
                LocalDateTime hasta
        );

        @Query("""
  SELECT DISTINCT c.numeroAgente
    FROM ClienteResidencial c
   WHERE c.usuario.coordinador.id = :coordinadorId
     AND (:sedeId IS NULL OR c.usuario.sede.id = :sedeId)
     AND c.fechaCreacion BETWEEN :desde AND :hasta
""")
        List<String> findDistinctNumeroAgenteByCoordinadorAndSedeAndFechaRange(
                @Param("coordinadorId") Long coordinadorId,
                @Param("sedeId")        Long sedeId,
                @Param("desde")         LocalDateTime desde,
                @Param("hasta")         LocalDateTime hasta
        );

        /**
         * Finds the most recent user (advisor) associated with a specific agent number on a given date.
         * @param numeroAgente The agent number to search for.
         * @param fecha The specific date of the lead creation.
         * @param pageable A pageable object to limit the result to one.
         * @return A list containing at most one User.
         */
        @Query("""
    SELECT c.usuario FROM ClienteResidencial c
    WHERE c.numeroAgente = :numeroAgente
    AND FUNCTION('DATE', c.fechaCreacion) = :fecha
    ORDER BY c.fechaCreacion DESC
""")
        List<User> findTopUsuarioByNumeroAgenteAndFechaCreacion(
                @Param("numeroAgente") String numeroAgente,
                @Param("fecha") LocalDate fecha,
                Pageable pageable
        );

        @Query("SELECT cr FROM ClienteResidencial cr WHERE cr.numeroAgente IN :agentes AND cr.fechaCreacion BETWEEN :desde AND :hasta")
        List<ClienteResidencial> findLeadsRegistradosParaDetalle(@Param("agentes") List<String> agentes, @Param("desde") LocalDateTime desde, @Param("hasta") LocalDateTime hasta);

// Reemplaza el método findCombinedContactDetailsPaginated con esta versión que devuelve la URL.

        @Query(value = """
    SELECT * FROM (
        -- Parte 1: Leads Registrados
        SELECT
            'Registrado' as tipo,
            cr.numero_agente COLLATE utf8mb4_unicode_ci as dni,
            CONCAT(u.nombre, ' ', u.apellido) COLLATE utf8mb4_unicode_ci as nombreCompleto,
            cr.movil_contacto COLLATE utf8mb4_unicode_ci as movilContacto,
            cr.fecha_creacion as fechaCreacion,
            CONCAT(coord.nombre, ' ', coord.apellido) COLLATE utf8mb4_unicode_ci as coordinador,
            cr.id as leadId,
            -- ==================== CAMBIO AQUÍ ====================
            cr.url_drive_transcripcion COLLATE utf8mb4_unicode_ci as urlTranscripcion
            -- =====================================================
        FROM cliente_residencial cr
        JOIN usuarios u ON cr.usuario_id = u.codi_usuario
        JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario
        WHERE u.coordinador_id = :coordinadorId
              AND (:sedeId IS NULL OR u.sede_id = :sedeId)
              AND cr.fecha_creacion BETWEEN :desde AND :hasta
              AND cr.nombre_archivo_mp3 IS NOT NULL AND cr.nombre_archivo_mp3 <> ''

        UNION ALL

        -- Parte 2: Audios Huérfanos
        SELECT
            'Huérfano' as tipo,
            asl.agente_extraido COLLATE utf8mb4_unicode_ci as dni,
            'Asesor no identificado (agente no registró leads ese día)' COLLATE utf8mb4_unicode_ci as nombreCompleto,
            asl.movil_extraido COLLATE utf8mb4_unicode_ci as movilContacto,
            asl.fecha_extraida as fechaCreacion,
            (SELECT CONCAT(c.nombre, ' ', c.apellido) FROM usuarios c WHERE c.codi_usuario = :coordinadorId) COLLATE utf8mb4_unicode_ci as coordinador,
            NULL as leadId,
            -- ==================== CAMBIO AQUÍ ====================
            asl.url_google_drive COLLATE utf8mb4_unicode_ci as urlTranscripcion
            -- =====================================================
        FROM audio_sin_lead asl
        WHERE
            EXISTS (
                SELECT 1
                FROM cliente_residencial cr
                JOIN usuarios u ON cr.usuario_id = u.codi_usuario
                WHERE u.coordinador_id = :coordinadorId
                  AND cr.numero_agente COLLATE utf8mb4_unicode_ci = asl.agente_extraido COLLATE utf8mb4_unicode_ci
            )
            AND EXISTS (
                SELECT 1
                FROM cliente_residencial c
                WHERE c.numero_agente COLLATE utf8mb4_unicode_ci = asl.agente_extraido COLLATE utf8mb4_unicode_ci
                  AND DATE(c.fecha_creacion) = DATE(asl.fecha_extraida)
            )
            AND asl.fecha_extraida BETWEEN :desde AND :hasta
    ) as contactos_combinados
    ORDER BY fechaCreacion DESC
    """,
                countQuery = """
    SELECT SUM(total) FROM (
        (SELECT COUNT(*) as total FROM cliente_residencial cr JOIN usuarios u ON cr.usuario_id = u.codi_usuario
         WHERE u.coordinador_id = :coordinadorId AND (:sedeId IS NULL OR u.sede_id = :sedeId) AND cr.fecha_creacion BETWEEN :desde AND :hasta
         AND cr.nombre_archivo_mp3 IS NOT NULL AND cr.nombre_archivo_mp3 <> '')
        UNION ALL
        (SELECT COUNT(*) as total FROM audio_sin_lead asl
         WHERE EXISTS (
             SELECT 1 FROM cliente_residencial cr JOIN usuarios u ON cr.usuario_id = u.codi_usuario
             WHERE u.coordinador_id = :coordinadorId
               AND cr.numero_agente COLLATE utf8mb4_unicode_ci = asl.agente_extraido COLLATE utf8mb4_unicode_ci
         ) AND EXISTS (
             SELECT 1 FROM cliente_residencial c
             WHERE c.numero_agente COLLATE utf8mb4_unicode_ci = asl.agente_extraido COLLATE utf8mb4_unicode_ci
               AND DATE(c.fecha_creacion) = DATE(asl.fecha_extraida)
         ) AND asl.fecha_extraida BETWEEN :desde AND :hasta)
    ) as conteo_total
    """,
                nativeQuery = true)
        Page<Object[]> findCombinedContactDetailsPaginated(
                @Param("coordinadorId") Long coordinadorId,
                @Param("sedeId") Long sedeId,
                @Param("desde") LocalDateTime desde,
                @Param("hasta") LocalDateTime hasta,
                Pageable pageable
        );

        // DEBUG: Consulta para verificar audios huérfanos (CON FECHA EXACTA como dashboard)
        @Query(value = """
        SELECT COUNT(*) as total_huerfanos
        FROM audio_sin_lead asl
        WHERE EXISTS (
            SELECT 1
            FROM cliente_residencial cr
            JOIN usuarios u ON cr.usuario_id = u.codi_usuario
            WHERE u.coordinador_id = :coordinadorId
              AND cr.numero_agente COLLATE utf8mb4_unicode_ci = asl.agente_extraido COLLATE utf8mb4_unicode_ci
              AND DATE(cr.fecha_creacion) = DATE(asl.fecha_extraida)
              AND cr.numero_agente IS NOT NULL AND cr.numero_agente != ''
        )
        AND asl.fecha_extraida BETWEEN :desde AND :hasta
        """, nativeQuery = true)
        Long countAudiosHuerfanosParaCoordinador(
                @Param("coordinadorId") Long coordinadorId,
                @Param("desde") LocalDateTime desde,
                @Param("hasta") LocalDateTime hasta
        );

        // DEBUG: Consulta para verificar audios huérfanos SIN restricción de coordinador
        @Query(value = """
        SELECT COUNT(*) as total_huerfanos_sin_restriccion
        FROM audio_sin_lead asl
        WHERE asl.fecha_extraida BETWEEN :desde AND :hasta
        """, nativeQuery = true)
        Long countTodosAudiosHuerfanos(
                @Param("desde") LocalDateTime desde,
                @Param("hasta") LocalDateTime hasta
        );

        // DEBUG: Consulta para verificar leads registrados
        @Query(value = """
        SELECT COUNT(*) as total_registrados
        FROM cliente_residencial cr
        JOIN usuarios u ON cr.usuario_id = u.codi_usuario
        WHERE u.coordinador_id = :coordinadorId
          AND cr.fecha_creacion BETWEEN :desde AND :hasta
          AND cr.nombre_archivo_mp3 IS NOT NULL AND cr.nombre_archivo_mp3 <> ''
        """, nativeQuery = true)
        Long countLeadsRegistradosParaCoordinador(
                @Param("coordinadorId") Long coordinadorId,
                @Param("desde") LocalDateTime desde,
                @Param("hasta") LocalDateTime hasta
        );
}