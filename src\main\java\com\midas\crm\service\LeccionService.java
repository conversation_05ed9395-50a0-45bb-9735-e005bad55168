package com.midas.crm.service;

import com.midas.crm.entity.DTO.leccion.LeccionCreateDTO;
import com.midas.crm.entity.DTO.leccion.LeccionDTO;
import com.midas.crm.entity.DTO.leccion.LeccionUpdateDTO;

import java.util.List;

public interface LeccionService {
    LeccionDTO createLeccion(LeccionCreateDTO dto);
    List<LeccionDTO> listLecciones();
    List<LeccionDTO> listLeccionesBySeccionId(Long seccionId);
    List<LeccionDTO> listLeccionesByModuloId(Long moduloId);
    List<LeccionDTO> listLeccionesByCursoId(Long cursoId);
    LeccionDTO getLeccionById(Long id);
    LeccionDTO updateLeccion(Long id, LeccionUpdateDTO dto);
    void deleteLeccion(Long id);
}
