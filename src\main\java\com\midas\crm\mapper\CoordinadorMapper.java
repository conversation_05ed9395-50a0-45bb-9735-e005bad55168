package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.coordinador.CoordinadorDTO;
import com.midas.crm.entity.User;

import java.util.ArrayList;
import java.util.stream.Collectors;

public class CoordinadorMapper {

    public static CoordinadorDTO toDTO(User coordinador) {
        CoordinadorDTO dto = new CoordinadorDTO();
        dto.setId(coordinador.getId());
        dto.setNombre(coordinador.getNombre());
        dto.setApellido(coordinador.getApellido());
        dto.setUsername(coordinador.getUsername());
        dto.setDni(coordinador.getDni());
        dto.setEmail(coordinador.getEmail());
        dto.setTelefono(coordinador.getTelefono());

        // Obtener el nombre de la sede, primero desde sedeNombre y si es null, desde la relación sede
        String nombreSede = coordinador.getSedeNombre();
        if (nombreSede == null && coordinador.getSede() != null) {
            nombreSede = coordinador.getSede().getNombre();
        }
        dto.setSede(nombreSede);

        if (coordinador.getAsesores() != null) {
            dto.setAsesores(coordinador.getAsesores().stream()
                    .map(AsesorMapper::toDTO)
                    .collect(Collectors.toList()));
        } else {
            dto.setAsesores(new ArrayList<>());
        }

        return dto;
    }
}
