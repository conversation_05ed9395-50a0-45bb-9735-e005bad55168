package com.midas.crm.validator;

import com.midas.crm.entity.DTO.manual.ManualDTO;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

@Component
public class ManualValidator implements Validator {

    @Override
    public boolean supports(Class<?> clazz) {
        return ManualDTO.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        ManualDTO manual = (ManualDTO) target;

        // Determinar si es una operación de creación o actualización
        boolean isUpdate = isUpdateOperation();

        // En creación, validar campos obligatorios
        // En actualización, solo validar si los campos no son nulos
        if (!isUpdate) {
            // Validación estricta para creación
            if (manual.getNombre() == null || manual.getNombre().trim().isEmpty()) {
                errors.rejectValue("nombre", "manual.nombre.required", "El nombre es obligatorio");
            }

            if (manual.getTipo() == null || manual.getTipo().trim().isEmpty()) {
                errors.rejectValue("tipo", "manual.tipo.required", "El tipo es obligatorio");
            } else if (!manual.getTipo().matches("^[SBMROTI]$")) {
                errors.rejectValue("tipo", "manual.tipo.format", "El tipo debe ser S, B, M, R, O, T o I");
            }
        } else {
            // Validación flexible para actualización
            // Solo validar formato si los campos no son nulos
            if (manual.getNombre() != null && manual.getNombre().trim().isEmpty()) {
                errors.rejectValue("nombre", "manual.nombre.invalid", "El nombre no puede estar vacío");
            }

            if (manual.getTipo() != null && manual.getTipo().trim().isEmpty()) {
                errors.rejectValue("tipo", "manual.tipo.invalid", "El tipo no puede estar vacío");
            }

            // Validar formato del tipo si no es nulo
            if (manual.getTipo() != null && !manual.getTipo().trim().isEmpty() &&
                    !manual.getTipo().matches("^[SBMROTI]$")) {
                errors.rejectValue("tipo", "manual.tipo.format", "El tipo debe ser S, B, M, R, O, T o I");
            }
        }
    }

    /**
     * Determina si la operación actual es una actualización (PUT) o creación (POST)
     */
    private boolean isUpdateOperation() {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                    .getRequest();
            return request.getMethod().equals(RequestMethod.PUT.name());
        } catch (Exception e) {
            // Si no se puede determinar, asumir que es creación para validación más
            // estricta
            return false;
        }
    }
}
