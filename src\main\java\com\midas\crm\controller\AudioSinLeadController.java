package com.midas.crm.controller;

import com.midas.crm.entity.AudioSinLead;
import com.midas.crm.service.AudioSinLeadService;
import com.midas.crm.service.ExcelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Controlador para manejar audios sin leads correspondientes
 */
@RestController
@RequestMapping("/api/audios-sin-lead")
@Slf4j
public class AudioSinLeadController {

    private final AudioSinLeadService audioSinLeadService;
    private final ExcelService excelService;

    public AudioSinLeadController(AudioSinLeadService audioSinLeadService, ExcelService excelService) {
        this.audioSinLeadService = audioSinLeadService;
        this.excelService = excelService;
    }

    /**
     * Obtiene resumen de audios sin lead
     */
    @GetMapping("/resumen")
    public ResponseEntity<Map<String, Object>> getResumen() {
        Map<String, Object> resumen = audioSinLeadService.getResumenAudiosSinLead();
        return ResponseEntity.ok(resumen);
    }

    /**
     * Busca audios sin lead por fecha con paginación
     */
    @GetMapping
    public ResponseEntity<Page<AudioSinLead>> getAudiosSinLead(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaDesde,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaHasta,
            @RequestParam(required = false) String estado,
            @RequestParam(required = false) String movil,
            @RequestParam(required = false) String agente,
            @RequestParam(required = false) String carpetaOrigen,
            @RequestParam(required = false) String prioridad,
            @RequestParam(required = false) Boolean requiereProcesamiento,
            @RequestParam(required = false) Boolean busquedaLeadRealizada) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("fechaEncontrado").descending());

        // Determinar el rango de fechas
        LocalDateTime inicio = null;
        LocalDateTime fin = null;

        if (fecha != null) {
            // Si se especifica fecha exacta, usar esa fecha
            inicio = fecha.atStartOfDay();
            fin = fecha.atTime(LocalTime.MAX);
        } else if (fechaDesde != null || fechaHasta != null) {
            // Si se especifica rango de fechas
            inicio = fechaDesde != null ? fechaDesde.atStartOfDay() : LocalDateTime.of(2020, 1, 1, 0, 0);
            fin = fechaHasta != null ? fechaHasta.atTime(LocalTime.MAX) : LocalDateTime.now();
        }

        // Convertir fechas a formato string para búsqueda en nombre de archivo
        String fechaInicioStr = null;
        String fechaFinStr = null;

        if (inicio != null) {
            fechaInicioStr = inicio.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        if (fin != null) {
            fechaFinStr = fin.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }

        // Usar el nuevo método con filtros múltiples
        Page<AudioSinLead> result = audioSinLeadService.findWithFilters(
                estado, prioridad, movil, agente, carpetaOrigen,
                requiereProcesamiento, busquedaLeadRealizada,
                inicio, fin, fechaInicioStr, fechaFinStr, pageable);

        return ResponseEntity.ok(result);
    }

    /**
     * Busca audio por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<AudioSinLead> getAudioById(@PathVariable Long id) {
        Optional<AudioSinLead> audio = audioSinLeadService.findById(id);
        return audio.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Busca audios por móvil
     */
    @GetMapping("/por-movil/{movil}")
    public ResponseEntity<List<AudioSinLead>> getAudiosPorMovil(@PathVariable String movil) {
        List<AudioSinLead> audios = audioSinLeadService.findByMovilExtraido(movil);
        return ResponseEntity.ok(audios);
    }

    /**
     * Busca audios por agente
     */
    @GetMapping("/por-agente/{agente}")
    public ResponseEntity<List<AudioSinLead>> getAudiosPorAgente(@PathVariable String agente) {
        List<AudioSinLead> audios = audioSinLeadService.findByAgenteExtraido(agente);
        return ResponseEntity.ok(audios);
    }

    /**
     * Busca audios por estado
     */
    @GetMapping("/por-estado/{estado}")
    public ResponseEntity<List<AudioSinLead>> getAudiosPorEstado(@PathVariable String estado) {
        List<AudioSinLead> audios = audioSinLeadService.findByEstado(estado);
        return ResponseEntity.ok(audios);
    }

    /**
     * Busca audios recientes (últimas 24 horas) y que requieren procesamiento con paginación
     */
    @GetMapping("/recientes")
    public ResponseEntity<Page<AudioSinLead>> getRecientesYPorProcesar(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "fechaEncontrado") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String prioridad) { // Acepta el parámetro de prioridad


        // Crear el objeto Sort
        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                Sort.by(sortBy).descending() :
                Sort.by(sortBy).ascending();

        // Crear el Pageable
        Pageable pageable = PageRequest.of(page, size, sort);

        // **FIXED**: Pasa el parámetro de prioridad al servicio
        Page<AudioSinLead> audios = audioSinLeadService.findRecientesYRequierenProcesamiento(prioridad, pageable);

        return ResponseEntity.ok(audios);
    }

    /**
     * Busca audios recientes sin paginación (para compatibilidad)
     */
    @GetMapping("/recientes/todos")
    public ResponseEntity<List<AudioSinLead>> getTodosRecientesYPorProcesar(@RequestParam(required = false) String prioridad) {
        // **FIXED**: Pasa el parámetro de prioridad al servicio
        List<AudioSinLead> audios = audioSinLeadService.findRecientesYRequierenProcesamiento(prioridad);
        return ResponseEntity.ok(audios);
    }

    /**
     * Busca audios con información válida
     */
    @GetMapping("/validos")
    public ResponseEntity<List<AudioSinLead>> getAudiosValidos() {
        List<AudioSinLead> audios = audioSinLeadService.findConInformacionValida();
        return ResponseEntity.ok(audios);
    }

    /**
     * Busca audios con información incompleta
     */
    @GetMapping("/incompletos")
    public ResponseEntity<List<AudioSinLead>> getAudiosIncompletos() {
        List<AudioSinLead> audios = audioSinLeadService.findConInformacionIncompleta();
        return ResponseEntity.ok(audios);
    }

    /**
     * Obtiene estadísticas por fecha
     */
    @GetMapping("/estadisticas/por-fecha")
    public ResponseEntity<List<Map<String, Object>>> getEstadisticasPorFecha(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin) {


        LocalDateTime inicio = fechaInicio.atStartOfDay();
        LocalDateTime fin = fechaFin.atTime(LocalTime.MAX);

        List<Map<String, Object>> estadisticas = audioSinLeadService.getEstadisticasPorFecha(inicio, fin);
        return ResponseEntity.ok(estadisticas);
    }

    /**
     * Obtiene estadísticas por agente
     */
    @GetMapping("/estadisticas/por-agente")
    public ResponseEntity<List<Map<String, Object>>> getEstadisticasPorAgente(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin) {


        LocalDateTime inicio = fechaInicio.atStartOfDay();
        LocalDateTime fin = fechaFin.atTime(LocalTime.MAX);

        List<Map<String, Object>> estadisticas = audioSinLeadService.getEstadisticasPorAgente(inicio, fin);
        return ResponseEntity.ok(estadisticas);
    }

    /**
     * Actualiza estado de un audio
     */
    @PutMapping("/{id}/estado")
    public ResponseEntity<AudioSinLead> updateEstado(
            @PathVariable Long id,
            @RequestParam String nuevoEstado,
            @RequestParam(required = false) String observaciones) {

        try {
            AudioSinLead updated = audioSinLeadService.updateEstado(id, nuevoEstado, observaciones);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Marca audio como procesado
     */
    @PutMapping("/{id}/procesar")
    public ResponseEntity<AudioSinLead> marcarComoProcesado(
            @PathVariable Long id,
            @RequestParam(required = false) String observaciones) {

        try {
            AudioSinLead updated = audioSinLeadService.marcarComoProcesado(id, observaciones);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Marca audio como ignorado
     */
    @PutMapping("/{id}/ignorar")
    public ResponseEntity<AudioSinLead> marcarComoIgnorado(
            @PathVariable Long id,
            @RequestParam String motivo) {


        try {
            AudioSinLead updated = audioSinLeadService.marcarComoIgnorado(id, motivo);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Elimina audio por ID
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAudio(@PathVariable Long id) {

        try {
            audioSinLeadService.deleteById(id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Busca audios por patrón en el nombre
     */
    @GetMapping("/buscar")
    public ResponseEntity<List<AudioSinLead>> buscarPorNombre(@RequestParam String patron) {
        List<AudioSinLead> audios = audioSinLeadService.findByNombreArchivoContaining(patron);
        return ResponseEntity.ok(audios);
    }

    /**
     * Cuenta audios por estado
     */
    @GetMapping("/contar/{estado}")
    public ResponseEntity<Long> contarPorEstado(@PathVariable String estado) {
        long count = audioSinLeadService.countByEstado(estado);
        return ResponseEntity.ok(count);
    }

    /**
     * Busca leads correspondientes para todos los audios sin lead
     */
    @PostMapping("/buscar-leads-masivo")
    public ResponseEntity<Map<String, Object>> buscarLeadsMasivo() {
        try {
            log.info("🔍 Iniciando búsqueda masiva de leads para audios sin lead");
            Map<String, Object> resultado = audioSinLeadService.buscarLeadsMasivo();
            log.info("✅ Búsqueda masiva completada: {}", resultado);
            return ResponseEntity.ok(resultado);
        } catch (Exception e) {
            log.error("❌ Error en búsqueda masiva de leads", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Exporta audios sin lead a Excel con filtros aplicados
     */
    @GetMapping("/exportar")
    public ResponseEntity<byte[]> exportarAudiosSinLead(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaDesde,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaHasta,
            @RequestParam(required = false) String estado,
            @RequestParam(required = false) String movil,
            @RequestParam(required = false) String agente,
            @RequestParam(required = false) String carpetaOrigen,
            @RequestParam(required = false) String prioridad,
            @RequestParam(required = false) Boolean requiereProcesamiento,
            @RequestParam(required = false) Boolean busquedaLeadRealizada) {

        try {
            // Determinar el rango de fechas
            LocalDateTime inicio = null;
            LocalDateTime fin = null;

            if (fecha != null) {
                // Si se especifica fecha exacta, usar esa fecha
                inicio = fecha.atStartOfDay();
                fin = fecha.atTime(LocalTime.MAX);
            } else if (fechaDesde != null || fechaHasta != null) {
                // Si se especifica rango de fechas
                inicio = fechaDesde != null ? fechaDesde.atStartOfDay() : LocalDateTime.of(2020, 1, 1, 0, 0);
                fin = fechaHasta != null ? fechaHasta.atTime(LocalTime.MAX) : LocalDateTime.now();
            }

            // Convertir fechas a formato string para búsqueda en nombre de archivo
            String fechaInicioStr = null;
            String fechaFinStr = null;

            if (inicio != null) {
                fechaInicioStr = inicio.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
            if (fin != null) {
                fechaFinStr = fin.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            }

            // Obtener todos los audios que coincidan con los filtros (sin paginación para exportar todo)
            List<AudioSinLead> audios = audioSinLeadService.findWithFilters(
                    estado, prioridad, movil, agente, carpetaOrigen,
                    requiereProcesamiento, busquedaLeadRealizada,
                    inicio, fin, fechaInicioStr, fechaFinStr);

            // Generar archivo Excel
            byte[] excelData = excelService.exportarAudiosSinLead(audios);

            // Configurar headers para descarga
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment",
                    "audios-sin-lead-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss")) + ".xlsx");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelData);

        } catch (Exception e) {
            log.error("Error al exportar audios sin lead", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}