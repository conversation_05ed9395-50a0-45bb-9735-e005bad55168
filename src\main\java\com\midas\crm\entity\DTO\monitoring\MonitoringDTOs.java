package com.midas.crm.entity.DTO.monitoring;

import com.midas.crm.config.RequestTracingInterceptor.RequestInfo;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

// Usamos records de Java para DTOs concisos e inmutables
public class MonitoringDTOs {

    /**
     * DTO para la respuesta de peticiones activas.
     */
    public record ActiveRequestsDTO(
            int totalActiveRequests,
            Date timestamp,
            Collection<RequestInfo> requests
    ) {}

    /**
     * DTO para la respuesta de peticiones de larga duración.
     */
    public record LongRunningRequestsDTO(
            int totalLongRequests,
            long maxDurationMs,
            Date timestamp,
            Map<String, Long> methodBreakdown,
            Collection<RequestInfo> requests
    ) {}

    /**
     * DTO para las estadísticas generales de peticiones.
     */
    public record RequestStatsDTO(
            int totalActive,
            int totalLongRunning,
            Date timestamp,
            Map<String, Long> activeMethodBreakdown,
            List<Map.Entry<String, Long>> topActiveUris
    ) {}
}