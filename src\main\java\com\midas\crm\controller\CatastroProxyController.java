package com.midas.crm.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * Controlador que actúa como proxy para la API del Catastro
 * Soluciona el problema de CORS al acceder directamente a la API desde el frontend
 * api.route.catastro=/api/catastro
 */
@RestController
@RequestMapping("${api.route.catastro}")
@Slf4j
public class CatastroProxyController {

    private final WebClient webClient;
    private final String BASE_URL = "https://www1.sedecatastro.gob.es/CYCBienInmueble/OVCBusqueda.aspx";

    @Value("${api.route.catastro}")
    private String catastroRoute;

    public CatastroProxyController(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.baseUrl(BASE_URL).build();
    }

    /**
     * Endpoint para obtener provincias del Catastro
     * @param requestMap Mapa con el filtro opcional para buscar provincias por nombre
     *                  Formato esperado: {"filtro": "NOMBRE_PROVINCIA"}
     * @return ResponseEntity con la respuesta de la API del Catastro
     */
    @PostMapping("/provincias")
    public ResponseEntity<String> getProvincias(@RequestBody(required = false) Map<String, String> requestMap) {
        try {
            // Formatear el cuerpo de la solicitud según lo requerido por la API del Catastro
            // El formato esperado es {"filtro": "NOMBRE_PROVINCIA"}
            String filtro;
            if (requestMap != null && requestMap.containsKey("filtro")) {
                filtro = "{\"filtro\": \"" + requestMap.get("filtro") + "\"}";
            } else {
                filtro = "{\"filtro\": \"\"}";
            }

            String response = webClient.post()
                    .uri("/ObtenerProvincias")
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON)
                    .bodyValue(filtro)
                    .retrieve()
                    .onStatus(status -> status.isError(),
                            clientResponse -> clientResponse.bodyToMono(String.class)
                                    .flatMap(errorBody -> {
                                        return Mono.<Throwable>error(new RuntimeException(errorBody));
                                    })
                    )
                    .bodyToMono(String.class)
                    .block();
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error interno en el servidor: " + ex.getMessage());
        }
    }

    /**
     * Endpoint para obtener municipios del Catastro por provincia
     * @param requestMap Mapa con el código de provincia y el filtro opcional para buscar municipios por nombre
     *                  Formato esperado: {"provincia": 28, "filtro": ""}
     * @return ResponseEntity con la respuesta de la API del Catastro
     */
    @PostMapping("/municipios")
    public ResponseEntity<String> getMunicipios(@RequestBody(required = false) Map<String, Object> requestMap) {
        try {
            // Formatear el cuerpo de la solicitud según lo requerido por la API del Catastro
            // El formato esperado es {"filtro": "", "provincia": 28}
            String filtro;

            if (requestMap == null) {
                return ResponseEntity.badRequest().body("Se requiere el código de provincia");
            }

            // Verificar que exista la provincia
            if (!requestMap.containsKey("provincia")) {
                return ResponseEntity.badRequest().body("Se requiere el código de provincia");
            }

            // Construir el cuerpo de la solicitud
            String provinciaValue = String.valueOf(requestMap.get("provincia"));
            String filtroValue = requestMap.containsKey("filtro") ? String.valueOf(requestMap.get("filtro")) : "";

            filtro = "{\"filtro\": \"" + filtroValue + "\", \"provincia\": " + provinciaValue + "}";

            String response = webClient.post()
                    .uri("/ObtenerMunicipios")
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON)
                    .bodyValue(filtro)
                    .retrieve()
                    .onStatus(status -> status.isError(),
                            clientResponse -> clientResponse.bodyToMono(String.class)
                                    .flatMap(errorBody -> {
                                        return Mono.<Throwable>error(new RuntimeException(errorBody));
                                    })
                    )
                    .bodyToMono(String.class)
                    .block();

            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error interno en el servidor: " + ex.getMessage());
        }
    }

    /**
     * Endpoint para obtener vías del Catastro por provincia y municipio
     * @param requestMap Mapa con el código de provincia, código de municipio y el filtro opcional para buscar vías por nombre
     *                  Formato esperado: {"provincia": 28, "municipio": 112, "filtro": ""}
     * @return ResponseEntity con la respuesta de la API del Catastro
     */
    @PostMapping("/vias")
    public ResponseEntity<String> getVias(@RequestBody(required = false) Map<String, Object> requestMap) {
        try {
            // Formatear el cuerpo de la solicitud según lo requerido por la API del Catastro
            // El formato esperado es {"filtro": "", "provincia": 28, "municipio": 112}
            String filtro;

            if (requestMap == null) {
                return ResponseEntity.badRequest().body("Se requieren los códigos de provincia y municipio");
            }

            // Verificar que existan la provincia y el municipio
            if (!requestMap.containsKey("provincia")) {
                return ResponseEntity.badRequest().body("Se requiere el código de provincia");
            }

            if (!requestMap.containsKey("municipio")) {
                return ResponseEntity.badRequest().body("Se requiere el código de municipio");
            }

            // Construir el cuerpo de la solicitud
            String provinciaValue = String.valueOf(requestMap.get("provincia"));
            String municipioValue = String.valueOf(requestMap.get("municipio"));
            String filtroValue = requestMap.containsKey("filtro") ? String.valueOf(requestMap.get("filtro")) : "";

            // Corregir el formato de la carga útil para que coincida exactamente con lo que espera la API del Catastro
            filtro = "{\"filtro\": \"" + filtroValue + "\", \"provincia\": " + provinciaValue + ", \"municipio\": " + municipioValue + "}";

            String response = webClient.post()
                    .uri("/ObtenerVias")
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON)
                    .bodyValue(filtro)
                    .retrieve()
                    .onStatus(status -> status.isError(),
                            clientResponse -> clientResponse.bodyToMono(String.class)
                                    .flatMap(errorBody -> {
                                        return Mono.<Throwable>error(new RuntimeException(errorBody));
                                    })
                    )
                    .bodyToMono(String.class)
                    .block();

            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error interno en el servidor: " + ex.getMessage());
        }
    }
}