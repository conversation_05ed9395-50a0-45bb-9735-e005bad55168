package com.midas.crm.service;

import com.midas.crm.entity.DTO.user.DeleteUserDTO;
import com.midas.crm.entity.DTO.user.UserDTO;
import com.midas.crm.entity.DTO.user.UserPageDTO;
import com.midas.crm.entity.DTO.user.UserUpdateDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.utils.GenericResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface UserService {
    Page<User> findAllUsers(Pageable pageable);

    Page<User> findAllUsersBySede(Long sedeId, Pageable pageable);

    User saveUserIndividual(User user);

    User saveUser(User user);

    Optional<User> findByUsername(String username);

    Optional<User> findByEmail(String email);

    Optional<User> findByDni(String dni);

    void changeRole(Role newRole, String username);

    User findByUsernameReturnToken(String username);

    User findUserById(Long userId);

    void saveUsers(List<User> users);

    void saveUsersBackOffice(List<User> users);

    User updateUser(Long userId, User updateUser);

    @Transactional
    User updateUserProfile(Long userId, User profileData);

    /**
     * Elimina un usuario (baja lógica o definitiva según el estado actual)
     *
     * @param userId ID del usuario a eliminar
     * @return true si se eliminó correctamente, false en caso contrario
     */
    boolean deleteUser(Long userId);

    /**
     * Elimina un usuario según el DTO de eliminación
     *
     * @param deleteUserDTO DTO con información de eliminación
     * @return true si se eliminó correctamente, false en caso contrario
     */
    boolean deleteUser(DeleteUserDTO deleteUserDTO);

    Page<User> searchAllFields(String query, Pageable pageable);

    Page<User> searchAllFieldsBySede(String query, Long sedeId, Pageable pageable);

    List<User> findAllCoordinadores();

    // Métodos para el controlador
    ResponseEntity<GenericResponse<User>> registrarUsuario(User user);

    ResponseEntity<GenericResponse<String>> createBackofficeUsersFromExcel(MultipartFile file);

    ResponseEntity<GenericResponse<String>> createUsersFromExcel(MultipartFile file);

    ResponseEntity<GenericResponse<UserPageDTO>> listUsers(int page, int size, Long sedeId);

    ResponseEntity<GenericResponse<UserPageDTO>> listUsers(int page, int size, Long sedeId, boolean soloConectados);

    ResponseEntity<GenericResponse<UserPageDTO>> listUsers(int page, int size, Long sedeId, boolean soloConectados, String estado);

    ResponseEntity<GenericResponse<Map<String, Object>>> getCurrentUser(Object userPrincipal,
                                                                        HttpServletRequest request);

    ResponseEntity<GenericResponse<User>> getUsuarioById(Long userId);

    /**
     * Elimina un usuario por su ID (baja lógica o definitiva según el estado
     * actual)
     *
     * @param userId ID del usuario a eliminar
     * @return Respuesta con el resultado de la operación
     */
    ResponseEntity<GenericResponse<Void>> deleteUserById(Long userId);

    /**
     * Elimina un usuario según el DTO de eliminación
     *
     * @param deleteUserDTO DTO con información de eliminación
     * @return Respuesta con el resultado de la operación
     */
    ResponseEntity<GenericResponse<Void>> deleteUserById(DeleteUserDTO deleteUserDTO);

    ResponseEntity<GenericResponse<Map<String, Object>>> searchUsers(String query, int page, int size, Long sedeId);

    ResponseEntity<GenericResponse<Map<String, Object>>> searchUsers(String query, int page, int size, Long sedeId, String estado);

    ResponseEntity<GenericResponse<User>> updateUserById(Long userId, User updateUser);

    /**
     * Crea un nuevo usuario en el sistema
     *
     * @param user El usuario a crear
     * @return El usuario creado
     */
    User createUser(User user);

    /**
     * Obtiene usuarios por rol específico
     *
     * @param role Rol a filtrar
     * @return Lista de usuarios con el rol especificado
     */
    List<UserDTO> getUsersByRole(Role role);

    /**
     * Obtiene usuarios por sede específica
     *
     * @param sedeId ID de la sede
     * @return Lista de usuarios de la sede especificada
     */
    List<UserDTO> getUsersBySede(Long sedeId);

    /**
     * Obtiene usuarios por sede y rol específicos
     *
     * @param sedeId ID de la sede
     * @param role   Rol a filtrar
     * @return Lista de usuarios de la sede y rol especificados
     */
    List<UserDTO> getUsersBySedeAndRole(Long sedeId, Role role);
}
