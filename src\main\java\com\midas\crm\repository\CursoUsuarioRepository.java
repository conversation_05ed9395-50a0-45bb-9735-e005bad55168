package com.midas.crm.repository;

import com.midas.crm.entity.CursoUsuario;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@Repository
public interface CursoUsuarioRepository extends JpaRepository<CursoUsuario, Long> {
    List<CursoUsuario> findByUsuarioId(Long usuarioId);

    List<CursoUsuario> findByCursoId(Long cursoId);

    boolean existsByCursoIdAndUsuarioId(Long cursoId, Long usuarioId);

    CursoUsuario findByCursoIdAndUsuarioId(Long cursoId, Long usuarioId);

    void deleteByCursoId(Long cursoId);

    // En CursoUsuarioRepository.java
    @Query("SELECT cu FROM CursoUsuario cu JOIN FETCH cu.curso JOIN FETCH cu.usuario WHERE cu.usuario.id = :usuarioId")
    List<CursoUsuario> findByUsuarioIdWithDetails(@PathVariable Long usuarioId);

    // Método para obtener usuarios asignados a un curso con detalles
    @Query("SELECT cu FROM CursoUsuario cu JOIN FETCH cu.curso JOIN FETCH cu.usuario WHERE cu.curso.id = :cursoId")
    List<CursoUsuario> findByCursoIdWithDetails(@PathVariable Long cursoId);
}
