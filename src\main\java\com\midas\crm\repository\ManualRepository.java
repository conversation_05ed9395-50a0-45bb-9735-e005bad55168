package com.midas.crm.repository;

import com.midas.crm.entity.Manual;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface ManualRepository extends JpaRepository<Manual, Integer> {
        // Métodos para búsqueda paginada que usa indexByRole
        Page<Manual> findByNombreContainingIgnoreCaseOrTipoContainingIgnoreCase(String nombre, String tipo,
                                                                                Pageable pageable);

        Page<Manual> findByNombreContainingIgnoreCaseOrTipoContainingIgnoreCaseAndIsActiveTrue(String nombre,
                                                                                               String tipo,
                                                                                               Pageable pageable);

        Page<Manual> findAllByIsActiveTrue(Pageable pageable);

        // --- Resto de tus métodos ---

        @Modifying
        @Transactional
        @Query(value = "UPDATE manuales SET deleted_at = NULL, is_active = true, horario = NULL, user_delete_id = NULL WHERE id = :id", nativeQuery = true)
        int restoreById(@Param("id") Integer id);

        @Query(value = "SELECT * FROM manuales WHERE id = :id", nativeQuery = true)
        Optional<Manual> findByIdIncludingDeleted(@Param("id") Integer id);

        @Query(value = "SELECT * FROM manuales", nativeQuery = true)
        List<Manual> findAllIncludingDeleted();

        @Query(value = "SELECT * FROM manuales WHERE is_active = true", nativeQuery = true)
        List<Manual> findAllActiveIncludingDeleted();

        @Query(value = "SELECT * FROM manuales WHERE is_active = true AND deleted_at IS NULL", nativeQuery = true)
        List<Manual> findAllActive();

        @Modifying
        @Transactional
        @Query(value = "UPDATE manuales SET nombre = :nombre, tipo = :tipo, archivo = :archivo, horario = :horario, user_update_id = :userUpdateId, updated_at = NOW() WHERE id = :id", nativeQuery = true)
        int updateManualById(@Param("id") Integer id, @Param("nombre") String nombre, @Param("tipo") String tipo,
                             @Param("archivo") String archivo, @Param("horario") String horario,
                             @Param("userUpdateId") Long userUpdateId);

        @Modifying
        @Transactional
        @Query(value = "UPDATE manuales SET nombre = :nombre, tipo = :tipo, archivo = :archivo, horario = :horario, is_active = :isActive, user_update_id = :userUpdateId, updated_at = NOW() WHERE id = :id", nativeQuery = true)
        int updateManualWithStateById(@Param("id") Integer id, @Param("nombre") String nombre,
                                      @Param("tipo") String tipo,
                                      @Param("archivo") String archivo, @Param("horario") String horario,
                                      @Param("isActive") Boolean isActive, @Param("userUpdateId") Long userUpdateId);

        @Modifying
        @Transactional
        @Query(value = "UPDATE manuales SET nombre = :nombre, tipo = :tipo, archivo = :archivo, horario = :horario, is_active = :isActive, sede_id = :sedeId, user_update_id = :userUpdateId, updated_at = NOW() WHERE id = :id", nativeQuery = true)
        int updateManualWithStateAndSedeById(@Param("id") Integer id, @Param("nombre") String nombre,
                                             @Param("tipo") String tipo,
                                             @Param("archivo") String archivo, @Param("horario") String horario,
                                             @Param("isActive") Boolean isActive, @Param("sedeId") Long sedeId,
                                             @Param("userUpdateId") Long userUpdateId);

        @Modifying
        @Transactional
        @Query(value = "UPDATE manuales SET is_active = false, horario = '00000', user_delete_id = :userDeleteId, updated_at = NOW() WHERE id = :id", nativeQuery = true)
        int markAsInactiveById(@Param("id") Integer id, @Param("userDeleteId") Long userDeleteId);

        @Query(value = "SELECT COUNT(*) > 0 FROM manuales WHERE id = :id AND horario = '00000' AND is_active = false", nativeQuery = true)
        boolean isInactiveById(@Param("id") Integer id);

        @Modifying
        @Transactional
        @Query(value = "UPDATE manuales SET deleted_at = CURRENT_TIMESTAMP, user_delete_id = :userDeleteId WHERE id = :id AND horario = '00000' AND is_active = false", nativeQuery = true)
        int permanentDeleteById(@Param("id") Integer id, @Param("userDeleteId") Long userDeleteId);

        @Modifying
        @Transactional
        @Query(value = "UPDATE manuales SET deleted_at = CURRENT_TIMESTAMP, is_active = false, user_delete_id = :userDeleteId WHERE id = :id", nativeQuery = true)
        int softDeleteById(@Param("id") Integer id, @Param("userDeleteId") Long userDeleteId);

        @Query("SELECT m FROM Manual m WHERE m.sede.id = :sedeId AND m.isActive = true")
        List<Manual> findBySedeIdAndIsActiveTrue(@Param("sedeId") Long sedeId);

        @Query("SELECT m FROM Manual m WHERE m.sede.id = :sedeId")
        List<Manual> findBySedeId(@Param("sedeId") Long sedeId);

        @Query("SELECT m FROM Manual m WHERE m.sede.id = :sedeId AND m.isActive = true")
        Page<Manual> findBySedeIdAndIsActiveTrue(@Param("sedeId") Long sedeId, Pageable pageable);

        @Query("SELECT m FROM Manual m WHERE m.sede.id = :sedeId")
        Page<Manual> findBySedeId(@Param("sedeId") Long sedeId, Pageable pageable);

        @Query("SELECT m FROM Manual m WHERE m.sede.id = :sedeId AND m.isActive = true AND " +
                "(LOWER(m.nombre) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
                "LOWER(m.tipo) LIKE LOWER(CONCAT('%', :search, '%')))")
        Page<Manual> findBySedeIdAndIsActiveTrueAndSearch(@Param("sedeId") Long sedeId, @Param("search") String search,
                                                          Pageable pageable);

        @Query("SELECT m FROM Manual m WHERE (m.sede IS NULL OR m.sede.id = :sedeId) AND m.isActive = true")
        List<Manual> findGlobalOrBySedeIdAndIsActiveTrue(@Param("sedeId") Long sedeId);

        @Query("SELECT m FROM Manual m WHERE (m.sede IS NULL OR m.sede.id = :sedeId) AND m.isActive = true")
        Page<Manual> findGlobalOrBySedeIdAndIsActiveTrue(@Param("sedeId") Long sedeId, Pageable pageable);

        @Query("SELECT m FROM Manual m WHERE (m.sede IS NULL OR m.sede.id = :sedeId) AND m.isActive = true AND " +
                "(LOWER(m.nombre) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
                "LOWER(m.tipo) LIKE LOWER(CONCAT('%', :search, '%')))")
        Page<Manual> findGlobalOrBySedeIdAndIsActiveTrueAndSearch(@Param("sedeId") Long sedeId,
                                                                  @Param("search") String search, Pageable pageable);
}