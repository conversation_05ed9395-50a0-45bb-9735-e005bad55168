package com.midas.crm.config;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.extensions.java6.auth.oauth2.AuthorizationCodeInstalledApp;
import com.google.api.client.extensions.jetty.auth.oauth2.LocalServerReceiver;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.midas.crm.service.GoogleOAuthService;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.FileDataStoreFactory;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.DriveScopes;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.midas.crm.utils.GoogleDriveOrganizationHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;

import java.io.*;
import java.security.GeneralSecurityException;
import java.util.Collections;

/**
 * Configuración para Google Drive API
 * Maneja la autenticación y configuración del servicio de Google Drive
 */
@Configuration
@Slf4j
public class GoogleDriveConfig {

    private static final String APPLICATION_NAME = "MIDAS CRM";
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();

    private final GoogleOAuthService googleOAuthService;

    public GoogleDriveConfig(GoogleOAuthService googleOAuthService) {
        this.googleOAuthService = googleOAuthService;
    }

    @Value("${google.drive.credentials.file:client_secret_com.json}")
    private String credentialsFilePath;

    @Value("${google.drive.service-account.file:service-account-key.json}")
    private String serviceAccountFilePath;

    @Value("${google.drive.auth.type:service-account}")
    private String authType;

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    @Value("${google.drive.oauth.tokens.file:tokens.json}")
    private String tokensFilePath;

    @Bean
    public GoogleDriveOrganizationHelper googleDriveOrganizationHelper(Drive driveService) {
        return new GoogleDriveOrganizationHelper(driveService);
    }

    /**
     * Crea el servicio de Google Drive usando Service Account o OAuth según configuración
     */
    @Bean
    @Primary
    public Drive googleDriveService() throws GeneralSecurityException, IOException {
        //log.info("Inicializando Google Drive Service con tipo de autenticación: {} en perfil: {}", authType, activeProfile);

        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();

        if ("oauth".equalsIgnoreCase(authType)) {
          //  log.info("Usando OAuth en perfil: {}", activeProfile);

            // Verificar si hay credenciales OAuth válidas
            if (googleOAuthService.hasValidCredentials()) {
            //    log.info("Usando credenciales OAuth existentes");
                Credential credential = googleOAuthService.getValidCredential();
                return new Drive.Builder(HTTP_TRANSPORT, JSON_FACTORY, credential)
                        .setApplicationName(APPLICATION_NAME)
                        .build();
            } else if (!"prod".equalsIgnoreCase(activeProfile)) {
                // Solo en desarrollo, usar LocalServerReceiver
              //  log.info("Usando OAuth con LocalServerReceiver en desarrollo");
                Credential credential = getOAuthCredential(HTTP_TRANSPORT);
                return new Drive.Builder(HTTP_TRANSPORT, JSON_FACTORY, credential)
                        .setApplicationName(APPLICATION_NAME)
                        .build();
            } else {
                // En producción, usar Service Account temporalmente hasta que se autorice OAuth
              //  log.warn("OAuth configurado en producción pero no hay credenciales válidas. Usando Service Account temporalmente.");
              //  log.info("Para acceso completo a archivos, visite /oauth2/authorize para autorizar OAuth.");
                GoogleCredentials credentials = getServiceAccountCredentials();
                return new Drive.Builder(HTTP_TRANSPORT, JSON_FACTORY, new HttpCredentialsAdapter(credentials))
                        .setApplicationName(APPLICATION_NAME)
                        .build();
            }
        } else {
            // Usar Service Account
           // log.info("Usando Service Account para autenticación");
            GoogleCredentials credentials = getServiceAccountCredentials();
            return new Drive.Builder(HTTP_TRANSPORT, JSON_FACTORY, new HttpCredentialsAdapter(credentials))
                    .setApplicationName(APPLICATION_NAME)
                    .build();
        }
    }

    /**
     * Obtiene las credenciales según el tipo de autenticación configurado
     */
    private GoogleCredentials getCredentials() throws IOException {
        if ("service-account".equals(authType)) {
            return getServiceAccountCredentials();
        } else {
            return getOAuthCredentials();
        }
    }

    /**
     * Autenticación usando Service Account (recomendado para aplicaciones servidor)
     */
    private GoogleCredentials getServiceAccountCredentials() throws IOException {
        try {
            // Intentar cargar desde classpath primero
            ClassPathResource resource = new ClassPathResource(serviceAccountFilePath);
            if (resource.exists()) {
            //    log.info("Cargando credenciales de Service Account desde classpath: {}", serviceAccountFilePath);
                try (InputStream inputStream = resource.getInputStream()) {
                    return ServiceAccountCredentials.fromStream(inputStream)
                            .createScoped(Collections.singletonList(DriveScopes.DRIVE_FILE));
                }
            }

            // Si no existe en classpath, intentar cargar desde sistema de archivos
           // log.info("Cargando credenciales de Service Account desde archivo: {}", serviceAccountFilePath);
            try (FileInputStream inputStream = new FileInputStream(serviceAccountFilePath)) {
                return ServiceAccountCredentials.fromStream(inputStream)
                        .createScoped(Collections.singletonList(DriveScopes.DRIVE_FILE));
            }
        } catch (IOException e) {
         //   log.error("Error al cargar credenciales de Service Account: {}", e.getMessage());
            throw new IOException("No se pudieron cargar las credenciales de Service Account. " +
                    "Asegúrate de que el archivo " + serviceAccountFilePath + " existe y es válido.", e);
        }
    }

    /**
     * Autenticación usando OAuth para cuenta de usuario
     */
    private Credential getOAuthCredential(NetHttpTransport httpTransport) throws IOException {
        try {
            // Cargar client secrets
            InputStream in = getCredentialsInputStream();
            GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));

            // Configurar el flujo de autorización
            GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(
                    httpTransport, JSON_FACTORY, clientSecrets, Collections.singletonList(DriveScopes.DRIVE))
                    .setDataStoreFactory(new FileDataStoreFactory(new File(tokensFilePath)))
                    .setAccessType("offline")
                    .build();

            // Configurar el receptor local
            LocalServerReceiver receiver = new LocalServerReceiver.Builder()
                    .setPort(8888)
                    .build();

            // Autorizar y obtener credenciales
          //  log.info("Iniciando flujo de autorización OAuth...");
            return new AuthorizationCodeInstalledApp(flow, receiver).authorize("user");

        } catch (IOException e) {
           // log.error("Error al obtener credenciales OAuth: {}", e.getMessage());
            throw new IOException("No se pudieron obtener las credenciales OAuth. " +
                    "Asegúrate de que el archivo " + credentialsFilePath + " existe y es válido.", e);
        }
    }

    /**
     * Obtiene el InputStream de las credenciales
     */
    private InputStream getCredentialsInputStream() throws IOException {
        // Intentar cargar desde classpath primero
        ClassPathResource resource = new ClassPathResource(credentialsFilePath);
        if (resource.exists()) {
           // log.info("Cargando credenciales desde classpath: {}", credentialsFilePath);
            return resource.getInputStream();
        }

        // Si no existe en classpath, intentar cargar desde sistema de archivos
        //log.info("Cargando credenciales desde archivo: {}", credentialsFilePath);
        return new FileInputStream(credentialsFilePath);
    }

    /**
     * Autenticación usando OAuth (método legacy - mantener para compatibilidad)
     */
    private GoogleCredentials getOAuthCredentials() throws IOException {
        try {
            InputStream inputStream = getCredentialsInputStream();
            return GoogleCredentials.fromStream(inputStream)
                    .createScoped(Collections.singletonList(DriveScopes.DRIVE_FILE));
        } catch (IOException e) {
           // log.error("Error al cargar credenciales OAuth: {}", e.getMessage());
            throw new IOException("No se pudieron cargar las credenciales OAuth. " +
                    "Asegúrate de que el archivo " + credentialsFilePath + " existe y es válido.", e);
        }
    }
}
