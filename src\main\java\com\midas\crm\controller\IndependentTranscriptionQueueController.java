package com.midas.crm.controller;

import com.midas.crm.entity.DTO.queue.IndependentTranscriptionQueueMessage;
import com.midas.crm.entity.DTO.GoogleDriveFileDTO;
import com.midas.crm.service.IndependentTranscriptionQueueService;
import com.midas.crm.utils.GenericResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controlador para gestionar las colas de transcripciones independientes
 */
@RestController
@RequestMapping("/api/independent-transcription-queue")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class IndependentTranscriptionQueueController {

    private final IndependentTranscriptionQueueService independentTranscriptionQueueService;

    /**
     * Procesa múltiples archivos de Google Drive para transcripción
     */
    @PostMapping("/process-multiple")
    public ResponseEntity<GenericResponse<String>> processMultipleFiles(
            @RequestBody ProcessMultipleFilesRequest request) {

        try {
            System.out.println("🎵 Recibida petición de procesamiento múltiple:");
            System.out.println("  📁 Archivos: " + (request.getGoogleDriveFiles() != null ? request.getGoogleDriveFiles().size() : 0));
            System.out.println("  👤 Usuario: " + request.getUserId());
            System.out.println("  🎙️ Modelo: " + request.getWhisperModel());
            System.out.println("  💻 Device: " + request.getDevice());

            if (request.getGoogleDriveFiles() == null || request.getGoogleDriveFiles().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(0, "No se proporcionaron archivos para procesar", null));
            }

            String batchId = independentTranscriptionQueueService.processMultipleFiles(
                    request.getGoogleDriveFiles(),
                    request.getUserId(),
                    request.getUserName(),
                    request.getWhisperModel(),
                    request.getDevice(),
                    request.getTargetLanguage(),
                    request.getTags(),
                    request.getNotes()
            );

            System.out.println("✅ Lote creado exitosamente con ID: " + batchId);
            return ResponseEntity.ok(new GenericResponse<>(1,
                    "Lote de transcripciones enviado a la cola exitosamente", batchId));

        } catch (Exception e) {
            System.err.println("❌ Error en processMultipleFiles: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Procesa un archivo individual
     */
    @PostMapping("/process-single")
    public ResponseEntity<GenericResponse<String>> processSingleFile(
            @RequestBody ProcessSingleFileRequest request) {

        try {

            String batchId = independentTranscriptionQueueService.processSingleFile(
                    request.getGoogleDriveFile(),
                    request.getUserId(),
                    request.getUserName(),
                    request.getWhisperModel(),
                    request.getDevice(),
                    request.getTargetLanguage(),
                    request.getTags(),
                    request.getNotes()
            );

            return ResponseEntity.ok(new GenericResponse<>(1,
                    "Archivo enviado a transcripción exitosamente", batchId));

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Obtiene el estado de un lote
     */
    @GetMapping("/batch-status/{batchId}")
    public ResponseEntity<GenericResponse<IndependentTranscriptionQueueMessage>> getBatchStatus(
            @PathVariable String batchId) {

        try {
            IndependentTranscriptionQueueMessage batch = independentTranscriptionQueueService.getBatchStatus(batchId);

            if (batch != null) {
                return ResponseEntity.ok(new GenericResponse<>(1, "Estado del lote obtenido", batch));
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Obtiene estadísticas de la cola
     */
    @GetMapping("/statistics")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getQueueStatistics() {
        try {
            Map<String, Object> stats = independentTranscriptionQueueService.getQueueStatistics();
            return ResponseEntity.ok(new GenericResponse<>(1, "Estadísticas obtenidas", stats));

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Cancela un lote
     */
    @PostMapping("/cancel/{batchId}")
    public ResponseEntity<GenericResponse<Boolean>> cancelBatch(@PathVariable String batchId) {
        try {
            boolean cancelled = independentTranscriptionQueueService.cancelBatch(batchId);

            if (cancelled) {
                return ResponseEntity.ok(new GenericResponse<>(1, "Lote cancelado exitosamente", true));
            } else {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(0, "No se pudo cancelar el lote", false));
            }

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), false));
        }
    }

    /**
     * Reintenta archivos fallidos de un lote
     */
    @PostMapping("/retry/{batchId}")
    public ResponseEntity<GenericResponse<Boolean>> retryFailedFiles(@PathVariable String batchId) {
        try {
            boolean retried = independentTranscriptionQueueService.retryFailedFiles(batchId);

            if (retried) {
                return ResponseEntity.ok(new GenericResponse<>(1, "Archivos fallidos reenviados", true));
            } else {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(0, "No se pudieron reintentar los archivos", false));
            }

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), false));
        }
    }

    /**
     * Obtiene lotes de un usuario
     */
    @GetMapping("/user-batches")
    public ResponseEntity<GenericResponse<List<IndependentTranscriptionQueueMessage>>> getUserBatches(
            @RequestParam String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            List<IndependentTranscriptionQueueMessage> batches =
                    independentTranscriptionQueueService.getUserBatches(userId, page, size);

            return ResponseEntity.ok(new GenericResponse<>(1, "Lotes del usuario obtenidos", batches));

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Obtiene lotes activos
     */
    @GetMapping("/active-batches")
    public ResponseEntity<GenericResponse<List<IndependentTranscriptionQueueMessage>>> getActiveBatches(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            List<IndependentTranscriptionQueueMessage> batches =
                    independentTranscriptionQueueService.getActiveBatches(page, size);

            return ResponseEntity.ok(new GenericResponse<>(1, "Lotes activos obtenidos", batches));

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Procesa archivos GSM de Google Drive con conversión automática a MP3
     * Endpoint específico para archivos GSM que requieren conversión
     */
    @PostMapping("/process-gsm-files")
    public ResponseEntity<GenericResponse<String>> processGsmFiles(
            @RequestBody ProcessMultipleFilesRequest request) {

        try {

            // Validar que los archivos sean GSM
            for (GoogleDriveFileDTO file : request.getGoogleDriveFiles()) {
                if (!isGsmFile(file.getName())) {
                    return ResponseEntity.badRequest()
                            .body(new GenericResponse<>(0,
                                    "El archivo " + file.getName() + " no es un archivo GSM válido", null));
                }
            }

            // Procesar archivos usando el servicio existente
            // El servicio ya maneja la conversión GSM a MP3 internamente
            String batchId = independentTranscriptionQueueService.processMultipleFiles(
                    request.getGoogleDriveFiles(),
                    request.getUserId(),
                    request.getUserName(),
                    request.getWhisperModel(),
                    request.getDevice(),
                    request.getTargetLanguage(),
                    request.getTags(),
                    request.getNotes()
            );

            return ResponseEntity.ok(new GenericResponse<>(1,
                    "Archivos GSM enviados a procesamiento exitosamente", batchId));

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Verifica si un archivo es GSM basándose en su nombre
     */
    private boolean isGsmFile(String fileName) {
        if (fileName == null) return false;
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".gsm") || lowerFileName.contains(".gsm");
    }

    /**
     * Pausa el procesamiento
     */
    @PostMapping("/pause")
    public ResponseEntity<GenericResponse<String>> pauseProcessing() {
        try {
            independentTranscriptionQueueService.pauseProcessing();
            return ResponseEntity.ok(new GenericResponse<>(1, "Procesamiento pausado", "PAUSED"));

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Reanuda el procesamiento
     */
    @PostMapping("/resume")
    public ResponseEntity<GenericResponse<String>> resumeProcessing() {
        try {
            independentTranscriptionQueueService.resumeProcessing();
            return ResponseEntity.ok(new GenericResponse<>(1, "Procesamiento reanudado", "ACTIVE"));

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Limpia lotes antiguos
     */
    @PostMapping("/cleanup")
    public ResponseEntity<GenericResponse<Integer>> cleanupOldBatches(
            @RequestParam(defaultValue = "7") int daysOld) {

        try {
            int cleaned = independentTranscriptionQueueService.cleanupOldBatches(daysOld);
            return ResponseEntity.ok(new GenericResponse<>(1,
                    "Lotes antiguos limpiados: " + cleaned, cleaned));

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    // ===== DTOs de Request =====

    /**
     * Request para procesar múltiples archivos
     */
    public static class ProcessMultipleFilesRequest {
        private List<GoogleDriveFileDTO> googleDriveFiles;
        private String userId;
        private String userName;
        private String whisperModel;
        private String device;
        private String targetLanguage;
        private List<String> tags;
        private String notes;

        // Getters y Setters
        public List<GoogleDriveFileDTO> getGoogleDriveFiles() { return googleDriveFiles; }
        public void setGoogleDriveFiles(List<GoogleDriveFileDTO> googleDriveFiles) { this.googleDriveFiles = googleDriveFiles; }
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public String getWhisperModel() { return whisperModel; }
        public void setWhisperModel(String whisperModel) { this.whisperModel = whisperModel; }
        public String getDevice() { return device; }
        public void setDevice(String device) { this.device = device; }
        public String getTargetLanguage() { return targetLanguage; }
        public void setTargetLanguage(String targetLanguage) { this.targetLanguage = targetLanguage; }
        public List<String> getTags() { return tags; }
        public void setTags(List<String> tags) { this.tags = tags; }
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }

    /**
     * Request para procesar un archivo individual
     */
    public static class ProcessSingleFileRequest {
        private GoogleDriveFileDTO googleDriveFile;
        private String userId;
        private String userName;
        private String whisperModel;
        private String device;
        private String targetLanguage;
        private List<String> tags;
        private String notes;

        // Getters y Setters
        public GoogleDriveFileDTO getGoogleDriveFile() { return googleDriveFile; }
        public void setGoogleDriveFile(GoogleDriveFileDTO googleDriveFile) { this.googleDriveFile = googleDriveFile; }
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public String getWhisperModel() { return whisperModel; }
        public void setWhisperModel(String whisperModel) { this.whisperModel = whisperModel; }
        public String getDevice() { return device; }
        public void setDevice(String device) { this.device = device; }
        public String getTargetLanguage() { return targetLanguage; }
        public void setTargetLanguage(String targetLanguage) { this.targetLanguage = targetLanguage; }
        public List<String> getTags() { return tags; }
        public void setTags(List<String> tags) { this.tags = tags; }
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }
}
