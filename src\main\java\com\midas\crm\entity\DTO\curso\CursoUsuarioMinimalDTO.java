package com.midas.crm.entity.DTO.curso;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO mínimo para información de asignación de curso a usuario
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CursoUsuarioMinimalDTO {
    private Long id;
    private LocalDateTime fechaAsignacion;
    private String estado;
    private Boolean completado;
    private LocalDateTime fechaCompletado;
    private Integer porcentajeCompletado;
    private LocalDateTime ultimaVisualizacion;
}
