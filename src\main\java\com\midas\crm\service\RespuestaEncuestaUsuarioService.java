package com.midas.crm.service;

import com.midas.crm.entity.DTO.encuesta.DetalleRespuestaEncuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.encuesta.RespuestaEncuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.encuesta.RespuestaEncuestaUsuarioDTO;

import java.util.List;
import java.util.Map;

/**
 * Servicio para gestionar respuestas de usuarios a encuestas
 */
public interface RespuestaEncuestaUsuarioService {

    /**
     * Inicia una nueva respuesta de usuario a una encuesta
     */
    RespuestaEncuestaUsuarioDTO iniciarRespuestaEncuesta(RespuestaEncuestaUsuarioCreateDTO dto);

    /**
     * Guarda una respuesta a una pregunta específica
     */
    RespuestaEncuestaUsuarioDTO responderPregunta(Long respuestaEncuestaUsuarioId, DetalleRespuestaEncuestaUsuarioCreateDTO dto);

    /**
     * Finaliza una respuesta de encuesta
     */
    RespuestaEncuestaUsuarioDTO finalizarRespuestaEncuesta(Long respuestaEncuestaUsuarioId);

    /**
     * Obtiene una respuesta de encuesta por su ID
     */
    RespuestaEncuestaUsuarioDTO getRespuestaEncuestaUsuarioById(Long id);

    /**
     * Obtiene todas las respuestas de un usuario a una encuesta específica
     */
    List<RespuestaEncuestaUsuarioDTO> getRespuestasByUsuarioAndEncuesta(Long usuarioId, Long encuestaId);

    /**
     * Obtiene todas las respuestas a una encuesta específica
     */
    List<RespuestaEncuestaUsuarioDTO> getRespuestasByEncuesta(Long encuestaId);

    /**
     * Verifica si un usuario ha completado una encuesta
     */
    boolean hasUsuarioCompletadoEncuesta(Long usuarioId, Long encuestaId);

    /**
     * Obtiene estadísticas de respuestas para una encuesta
     */
    Map<String, Object> getEstadisticasRespuestas(Long encuestaId);
}
