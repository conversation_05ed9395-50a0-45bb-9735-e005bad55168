package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.video.VideoInfoCreateDTO;
import com.midas.crm.entity.DTO.video.VideoInfoDTO;
import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.VideoInfo;

public final class VideoInfoMapper {

    private VideoInfoMapper() {}

    public static VideoInfo toEntity(VideoInfoCreateDTO dto, Leccion leccion) {
        VideoInfo videoInfo = new VideoInfo();
        videoInfo.setLeccion(leccion);
        videoInfo.setFormato(dto.getFormato());
        videoInfo.setResolucion(dto.getResolucion());
        videoInfo.setTamanoBytes(dto.getTamanoBytes());
        videoInfo.setDuracionSegundos(dto.getDuracionSegundos());
        videoInfo.setFechaSubida(dto.getFechaSubida());
        return videoInfo;
    }

    public static VideoInfoDTO toDTO(VideoInfo videoInfo) {
        if (videoInfo == null) return null;

        return new VideoInfoDTO(
                videoInfo.getId(),
                videoInfo.getLeccion() != null ? videoInfo.getLeccion().getId() : null,
                videoInfo.getFormato(),
                videoInfo.getResolucion(),
                videoInfo.getTamanoBytes(),
                videoInfo.getDuracionSegundos(),
                videoInfo.getFechaSubida(),
                videoInfo.getFechaCreacion()
        );
    }
}
