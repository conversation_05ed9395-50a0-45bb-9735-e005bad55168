package com.midas.crm.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.RetryInterceptorBuilder;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.retry.RepublishMessageRecoverer;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.interceptor.RetryOperationsInterceptor;

@Configuration
public class RabbitMQConfig {

    /* ───── Nombres ───── */
    public static final String TRANSCRIPTION_QUEUE   = "transcription.queue";
    public static final String TRANSCRIPTION_DLQ     = "transcription.dlq";
    public static final String COMPARISON_QUEUE      = "comparison.queue";
    public static final String COMPARISON_DLQ        = "comparison.dlq";

    // Nueva cola para transcripciones independientes
    public static final String INDEPENDENT_TRANSCRIPTION_QUEUE = "independent.transcription.queue";
    public static final String INDEPENDENT_TRANSCRIPTION_DLQ   = "independent.transcription.dlq";

    public static final String TRANSCRIPTION_EXCHANGE = "transcription.exchange";
    public static final String COMPARISON_EXCHANGE    = "comparison.exchange";
    public static final String DLX_EXCHANGE           = "dlx.exchange";

    // Nuevo exchange para transcripciones independientes
    public static final String INDEPENDENT_TRANSCRIPTION_EXCHANGE = "independent.transcription.exchange";

    public static final String TRANSCRIPTION_ROUTING_KEY = "transcription.process";
    public static final String COMPARISON_ROUTING_KEY    = "comparison.process";
    public static final String DLQ_ROUTING_KEY           = "dlq";

    // Nueva routing key para transcripciones independientes
    public static final String INDEPENDENT_TRANSCRIPTION_ROUTING_KEY = "independent.transcription.process";

    /* ───── Conversor JSON ───── */
    @Bean
    public MessageConverter jsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    /* ───── RabbitTemplate ───── */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory cf) {
        RabbitTemplate tpl = new RabbitTemplate(cf);
        tpl.setMessageConverter(jsonMessageConverter());
        tpl.setMandatory(true);                       // devuelve ReturnedMessage si no hay binding
        return tpl;
    }

    /**
     * Container factory en ACK manual + retry + DLX.
     */
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
            ConnectionFactory cf,
            RabbitTemplate rabbitTemplate) {

        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(cf);
        factory.setMessageConverter(jsonMessageConverter());

        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);      // 👈 ACK manual
        factory.setPrefetchCount(1);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);

        factory.setAdviceChain(
                RetryInterceptorBuilder.stateless()
                        .maxAttempts(5)
                        .recoverer(new RepublishMessageRecoverer(
                                rabbitTemplate,
                                DLX_EXCHANGE,
                                DLQ_ROUTING_KEY))
                        .build());

        return factory;
    }

    @Bean
    public SimpleRabbitListenerContainerFactory autoAckFactory(ConnectionFactory cf,
                                                               MessageConverter converter) {
        SimpleRabbitListenerContainerFactory f = new SimpleRabbitListenerContainerFactory();
        f.setConnectionFactory(cf);
        f.setMessageConverter(converter);
        f.setAcknowledgeMode(AcknowledgeMode.AUTO);
        f.setPrefetchCount(2);
        return f;
    }

    /* ───── Exchanges ───── */
    @Bean DirectExchange transcriptionExchange() { return new DirectExchange(TRANSCRIPTION_EXCHANGE, true, false); }
    @Bean DirectExchange comparisonExchange()    { return new DirectExchange(COMPARISON_EXCHANGE,  true, false); }
    @Bean DirectExchange dlxExchange()           { return new DirectExchange(DLX_EXCHANGE,          true, false); }
    @Bean DirectExchange independentTranscriptionExchange() { return new DirectExchange(INDEPENDENT_TRANSCRIPTION_EXCHANGE, true, false); }

    /* ───── Queues ───── */
    @Bean
    public Queue transcriptionQueue() {
        return QueueBuilder.durable(TRANSCRIPTION_QUEUE)
                .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", DLQ_ROUTING_KEY)
                .withArgument("x-message-ttl", 86_400_000)   // 24 h
                .build();
    }
    @Bean
    public Queue comparisonQueue() {
        return QueueBuilder.durable(COMPARISON_QUEUE)
                .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", DLQ_ROUTING_KEY)
                .withArgument("x-message-ttl", 86_400_000)   // 24 h
                .build();
    }
    @Bean
    public Queue independentTranscriptionQueue() {
        return QueueBuilder.durable(INDEPENDENT_TRANSCRIPTION_QUEUE)
                .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", DLQ_ROUTING_KEY)
                .withArgument("x-message-ttl", 86_400_000)   // 24 h
                .build();
    }
    @Bean Queue transcriptionDlq() { return QueueBuilder.durable(TRANSCRIPTION_DLQ).build(); }
    @Bean Queue comparisonDlq()    { return QueueBuilder.durable(COMPARISON_DLQ).build();    }
    @Bean Queue independentTranscriptionDlq() { return QueueBuilder.durable(INDEPENDENT_TRANSCRIPTION_DLQ).build(); }

    /* ───── Bindings ───── */
    @Bean Binding transcriptionBinding() {
        return BindingBuilder.bind(transcriptionQueue())
                .to(transcriptionExchange())
                .with(TRANSCRIPTION_ROUTING_KEY);
    }
    @Bean Binding comparisonBinding() {
        return BindingBuilder.bind(comparisonQueue())
                .to(comparisonExchange())
                .with(COMPARISON_ROUTING_KEY);
    }
    @Bean Binding transcriptionDlqBinding() {
        return BindingBuilder.bind(transcriptionDlq())
                .to(dlxExchange())
                .with(DLQ_ROUTING_KEY);
    }
    @Bean Binding comparisonDlqBinding() {
        return BindingBuilder.bind(comparisonDlq())
                .to(dlxExchange())
                .with(DLQ_ROUTING_KEY);
    }
    @Bean Binding independentTranscriptionBinding() {
        return BindingBuilder.bind(independentTranscriptionQueue())
                .to(independentTranscriptionExchange())
                .with(INDEPENDENT_TRANSCRIPTION_ROUTING_KEY);
    }
    @Bean Binding independentTranscriptionDlqBinding() {
        return BindingBuilder.bind(independentTranscriptionDlq())
                .to(dlxExchange())
                .with(DLQ_ROUTING_KEY);
    }
}
