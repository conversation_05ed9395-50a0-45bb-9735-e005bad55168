package com.midas.crm.entity.DTO.asesor;

import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AsesorConClientesDTO {
    private Long id;
    private String nombreCompleto;
    private String dni;
    private List<ClienteConUsuarioDTO> clientesAsignados;
}
