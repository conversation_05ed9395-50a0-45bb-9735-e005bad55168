package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * Entidad que representa el detalle de una respuesta de un usuario a una pregunta específica de una encuesta
 */
@Entity
@Table(name = "detalles_respuesta_encuesta_usuario")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetalleRespuestaEncuestaUsuario {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "respuesta_encuesta_usuario_id", nullable = false)
    private RespuestaEncuestaUsuario respuestaEncuestaUsuario;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pregunta_id", nullable = false)
    private PreguntaEncuesta pregunta;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "opcion_id")
    private OpcionRespuestaEncuesta opcion; // Puede ser null para preguntas de texto libre, fecha o número

    @Column(name = "respuesta_texto", columnDefinition = "TEXT")
    private String respuestaTexto; // Para preguntas de texto libre

    @Column(name = "respuesta_numero")
    private Double respuestaNumero; // Para preguntas numéricas

    @Column(name = "respuesta_fecha")
    private LocalDateTime respuestaFecha; // Para preguntas de fecha

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;
}
