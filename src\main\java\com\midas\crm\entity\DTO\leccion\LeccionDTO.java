package com.midas.crm.entity.DTO.leccion;

import com.midas.crm.entity.Leccion.TipoLeccion;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LeccionDTO {
    private Long id;
    private String titulo;
    private String descripcion;
    private Integer orden;
    private TipoLeccion tipoLeccion;
    private String videoUrl;
    private String subtitlesUrl;
    private String duracion;
    private String thumbnailUrl;
    private String pdfUrl;
    private Long seccionId;
    private String seccionTitulo;
    private String estado;
    private LocalDateTime fechaCreacion;
}
