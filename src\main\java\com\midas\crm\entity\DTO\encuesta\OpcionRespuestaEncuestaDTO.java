package com.midas.crm.entity.DTO.encuesta;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO para transferir información de opciones de respuesta de encuesta
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpcionRespuestaEncuestaDTO {
    private Long id;
    private String texto;
    private Integer orden;
    private Integer valor;
    private Long preguntaId;
    private String estado;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
    
    // Estadísticas
    private Long cantidadRespuestas;
    private Double porcentajeRespuestas;
}
