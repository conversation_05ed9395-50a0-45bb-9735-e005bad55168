package com.midas.crm.entity.DTO.user;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * DTO para respuestas de usuario
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserResponseDTO {
    private Long id;
    private String username;
    private String nombre;
    private String apellido;
    private String dni;
    private String telefono;
    private String email;
    private LocalDateTime fechaCreacion;
    private LocalDate fechaCese;
    private String estado;
    private Role role;
    private String sede;
    private Long sede_id;
    private UserDTO coordinador;
    private String picture;
}
