package com.midas.crm.service;

import com.midas.crm.entity.DTO.encuesta.EncuestaCreateDTO;
import com.midas.crm.entity.DTO.encuesta.EncuestaDTO;
import com.midas.crm.entity.DTO.encuesta.EncuestaUpdateDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * Servicio para gestionar encuestas
 */
public interface EncuestaService {

    /**
     * Crea una nueva encuesta
     */
    EncuestaDTO createEncuesta(EncuestaCreateDTO dto, Long creadorId);

    /**
     * Obtiene una encuesta por su ID
     */
    EncuestaDTO getEncuestaById(Long id);

    /**
     * Obtiene una encuesta por su ID con todas sus preguntas y opciones
     */
    EncuestaDTO getEncuestaCompleta(Long id);

    /**
     * Actualiza una encuesta existente
     */
    EncuestaDTO updateEncuesta(Long id, EncuestaUpdateDTO dto);

    /**
     * Elimina una encuesta (cambio de estado a inactivo)
     */
    void deleteEncuesta(Long id);

    /**
     * Obtiene todas las encuestas
     */
    List<EncuestaDTO> getAllEncuestas();

    /**
     * Obtiene todas las encuestas activas
     */
    List<EncuestaDTO> getAllEncuestasActivas();

    /**
     * Obtiene todas las encuestas disponibles para un usuario específico
     */
    List<EncuestaDTO> getEncuestasDisponiblesParaUsuario(Long usuarioId);

    /**
     * Obtiene todas las encuestas creadas por un usuario específico
     */
    List<EncuestaDTO> getEncuestasByCreador(Long creadorId);

    /**
     * Obtiene todas las encuestas con paginación y filtro
     */
    Page<EncuestaDTO> getEncuestasPageable(String search, Pageable pageable);

    /**
     * Obtiene estadísticas generales de una encuesta
     */
    Map<String, Object> getEstadisticasEncuesta(Long encuestaId);
}
