package com.midas.crm.service.serviceImpl;

import com.midas.crm.service.ConnectionMonitorService;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.user.SimpUserRegistry;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Implementación del servicio para monitorear y gestionar conexiones
 * Proporciona métodos para monitorear y limpiar conexiones de base de datos y WebSocket
 */
@Service
@Slf4j
public class ConnectionMonitorServiceImpl implements ConnectionMonitorService {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private UserConnectionServiceImpl userConnectionService;

    @Autowired
    private SimpUserRegistry simpUserRegistry;

    // Contador de limpiezas realizadas
    private final AtomicInteger cleanupCounter = new AtomicInteger(0);

    // Historial de conexiones para detectar tendencias
    private final Map<LocalDateTime, Integer> connectionHistory = new HashMap<>();

    /**
     * Monitorea automáticamente las conexiones cada minuto
     * Si detecta un aumento significativo, realiza una limpieza automática
     */
    @Scheduled(fixedRate = 60000) // 1 minuto
    public void autoMonitorConnections() {
        try {
            // Obtener estadísticas actuales
            int totalConnections = getTotalConnections();
            
            // Guardar en el historial
            connectionHistory.put(LocalDateTime.now(), totalConnections);
            
            // Limpiar historial antiguo (mantener solo las últimas 60 entradas = 1 hora)
            if (connectionHistory.size() > 60) {
                LocalDateTime oldestKey = connectionHistory.keySet().stream()
                        .min(LocalDateTime::compareTo)
                        .orElse(null);
                if (oldestKey != null) {
                    connectionHistory.remove(oldestKey);
                }
            }
            
            // Detectar tendencia de crecimiento
            boolean rapidGrowth = detectRapidGrowth();
            
            // Si hay más de 1000 conexiones o se detecta un crecimiento rápido, limpiar automáticamente
            if (totalConnections > 1000 || rapidGrowth) {
                log.warn("Detectado alto número de conexiones ({}) o crecimiento rápido ({}). Iniciando limpieza automática.", 
                        totalConnections, rapidGrowth);
                cleanupConnections();
            }
            
            // Cada 10 minutos, realizar una limpieza preventiva
            if (cleanupCounter.incrementAndGet() % 10 == 0) {
                log.info("Realizando limpieza preventiva programada");
                cleanupConnections();
            }
        } catch (Exception e) {
            log.error("Error en monitoreo automático de conexiones: {}", e.getMessage(), e);
        }
    }

    /**
     * Detecta si hay un crecimiento rápido en el número de conexiones
     * @return true si se detecta un crecimiento rápido, false en caso contrario
     */
    private boolean detectRapidGrowth() {
        if (connectionHistory.size() < 5) {
            return false;
        }
        
        // Obtener las últimas 5 mediciones
        LocalDateTime[] times = connectionHistory.keySet().stream()
                .sorted(LocalDateTime::compareTo)
                .skip(connectionHistory.size() - 5)
                .toArray(LocalDateTime[]::new);
        
        if (times.length < 5) {
            return false;
        }
        
        int firstValue = connectionHistory.get(times[0]);
        int lastValue = connectionHistory.get(times[4]);
        
        // Si hay un aumento de más del 20% en 5 minutos, considerar crecimiento rápido
        return lastValue > firstValue && (lastValue - firstValue) > (firstValue * 0.2);
    }

    @Override
    public String monitorConnections() {
        StringBuilder report = new StringBuilder();
        report.append("=== REPORTE DE CONEXIONES ===\n");
        report.append("Fecha y hora: ").append(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append("\n\n");
        
        // Información de conexiones de base de datos
        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                HikariPoolMXBean poolMXBean = hikariDataSource.getHikariPoolMXBean();
                
                report.append("CONEXIONES DE BASE DE DATOS (HikariCP):\n");
                report.append("- Conexiones activas: ").append(poolMXBean.getActiveConnections()).append("\n");
                report.append("- Conexiones inactivas: ").append(poolMXBean.getIdleConnections()).append("\n");
                report.append("- Conexiones totales: ").append(poolMXBean.getTotalConnections()).append("\n");
                report.append("- Tamaño máximo del pool: ").append(hikariDataSource.getMaximumPoolSize()).append("\n");
                report.append("- Tiempo de espera de conexión: ").append(hikariDataSource.getConnectionTimeout()).append("ms\n");
                report.append("- Tiempo máximo de vida de conexión: ").append(hikariDataSource.getMaxLifetime()).append("ms\n\n");
            } else {
                report.append("CONEXIONES DE BASE DE DATOS: No se pudo obtener información (no es HikariDataSource)\n\n");
            }
        } catch (Exception e) {
            report.append("ERROR al obtener información de conexiones de base de datos: ").append(e.getMessage()).append("\n\n");
        }
        
        // Información de conexiones WebSocket
        try {
            report.append("CONEXIONES WEBSOCKET:\n");
            report.append("- Usuarios conectados: ").append(userConnectionService.getConnectedUsers().size()).append("\n");
            report.append("- Sesiones WebSocket activas: ").append(simpUserRegistry.getUserCount()).append("\n\n");
        } catch (Exception e) {
            report.append("ERROR al obtener información de conexiones WebSocket: ").append(e.getMessage()).append("\n\n");
        }
        
        // Información de memoria
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapMemoryUsage = memoryBean.getHeapMemoryUsage();
            
            report.append("INFORMACIÓN DE MEMORIA:\n");
            report.append("- Memoria heap usada: ").append(formatSize(heapMemoryUsage.getUsed())).append("\n");
            report.append("- Memoria heap comprometida: ").append(formatSize(heapMemoryUsage.getCommitted())).append("\n");
            report.append("- Memoria heap máxima: ").append(formatSize(heapMemoryUsage.getMax())).append("\n");
            report.append("- Porcentaje de uso: ").append(String.format("%.2f%%", (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax() * 100)).append("\n\n");
        } catch (Exception e) {
            report.append("ERROR al obtener información de memoria: ").append(e.getMessage()).append("\n\n");
        }
        
        return report.toString();
    }

    @Override
    public int cleanupConnections() {
        int totalCleaned = 0;
        
        // Limpiar conexiones de base de datos
        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                HikariPoolMXBean poolMXBean = hikariDataSource.getHikariPoolMXBean();
                
                int beforeTotal = poolMXBean.getTotalConnections();
                
                // Suavemente cerrar las conexiones inactivas
                poolMXBean.softEvictConnections();
                
                // Esperar un momento para que se completen las operaciones
                Thread.sleep(500);
                
                int afterTotal = poolMXBean.getTotalConnections();
                int cleaned = beforeTotal - afterTotal;
                totalCleaned += cleaned;
                
                log.info("Limpieza de conexiones de base de datos completada: {} conexiones liberadas", cleaned);
            }
        } catch (Exception e) {
            log.error("Error al limpiar conexiones de base de datos: {}", e.getMessage(), e);
        }
        
        // Limpiar sesiones WebSocket huérfanas
        try {
            userConnectionService.cleanupOrphanedSessions();
            userConnectionService.cleanupInactiveSessions();
            
            // No podemos saber exactamente cuántas sesiones se limpiaron, pero asumimos al menos 1
            totalCleaned += 1;
            
            log.info("Limpieza de sesiones WebSocket completada");
        } catch (Exception e) {
            log.error("Error al limpiar sesiones WebSocket: {}", e.getMessage(), e);
        }
        
        return totalCleaned;
    }

    @Override
    public String getConnectionStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== ESTADÍSTICAS DETALLADAS DE CONEXIONES ===\n");
        stats.append("Fecha y hora: ").append(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append("\n\n");
        
        // Estadísticas de conexiones de base de datos
        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                
                stats.append("ESTADÍSTICAS DE HIKARICP:\n");
                stats.append("- URL de la base de datos: ").append(hikariDataSource.getJdbcUrl()).append("\n");
                stats.append("- Nombre del pool: ").append(hikariDataSource.getPoolName()).append("\n");
                stats.append("- Tamaño máximo del pool: ").append(hikariDataSource.getMaximumPoolSize()).append("\n");
                stats.append("- Mínimo de conexiones inactivas: ").append(hikariDataSource.getMinimumIdle()).append("\n");
                stats.append("- Tiempo de espera de conexión: ").append(hikariDataSource.getConnectionTimeout()).append("ms\n");
                stats.append("- Tiempo de inactividad: ").append(hikariDataSource.getIdleTimeout()).append("ms\n");
                stats.append("- Tiempo máximo de vida: ").append(hikariDataSource.getMaxLifetime()).append("ms\n");
                stats.append("- Tiempo de validación: ").append(hikariDataSource.getValidationTimeout()).append("ms\n");
                stats.append("- Tiempo de keepalive: ").append(hikariDataSource.getKeepaliveTime()).append("ms\n");
                stats.append("- Umbral de detección de fugas: ").append(hikariDataSource.getLeakDetectionThreshold()).append("ms\n");
                stats.append("- Auto-commit: ").append(hikariDataSource.isAutoCommit()).append("\n");
                stats.append("- Consulta de prueba de conexión: ").append(hikariDataSource.getConnectionTestQuery()).append("\n");
                stats.append("- Registro de MBeans: ").append(hikariDataSource.isRegisterMbeans()).append("\n\n");
                
                // Estadísticas actuales del pool
                HikariPoolMXBean poolMXBean = hikariDataSource.getHikariPoolMXBean();
                stats.append("ESTADÍSTICAS ACTUALES DEL POOL:\n");
                stats.append("- Conexiones activas: ").append(poolMXBean.getActiveConnections()).append("\n");
                stats.append("- Conexiones inactivas: ").append(poolMXBean.getIdleConnections()).append("\n");
                stats.append("- Conexiones totales: ").append(poolMXBean.getTotalConnections()).append("\n");
                stats.append("- Hilos esperando conexión: ").append(poolMXBean.getThreadsAwaitingConnection()).append("\n\n");
            } else {
                stats.append("ESTADÍSTICAS DE BASE DE DATOS: No se pudo obtener información (no es HikariDataSource)\n\n");
            }
        } catch (Exception e) {
            stats.append("ERROR al obtener estadísticas de conexiones de base de datos: ").append(e.getMessage()).append("\n\n");
        }
        
        // Estadísticas de conexiones WebSocket
        try {
            stats.append("ESTADÍSTICAS DE WEBSOCKET:\n");
            stats.append("- Usuarios conectados: ").append(userConnectionService.getConnectedUsers().size()).append("\n");
            stats.append("- Sesiones WebSocket activas: ").append(simpUserRegistry.getUserCount()).append("\n");
            stats.append("- Usuarios por sesión: ").append(simpUserRegistry.getUserCount() > 0 ? 
                    String.format("%.2f", (double) userConnectionService.getConnectedUsers().size() / simpUserRegistry.getUserCount()) : 
                    "N/A").append("\n\n");
        } catch (Exception e) {
            stats.append("ERROR al obtener estadísticas de conexiones WebSocket: ").append(e.getMessage()).append("\n\n");
        }
        
        // Historial de conexiones
        stats.append("HISTORIAL DE CONEXIONES (últimos ").append(connectionHistory.size()).append(" minutos):\n");
        connectionHistory.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> stats.append("- ")
                        .append(entry.getKey().format(DateTimeFormatter.ISO_LOCAL_TIME))
                        .append(": ")
                        .append(entry.getValue())
                        .append(" conexiones\n"));
        
        return stats.toString();
    }

    @Override
    public String forceCleanup() {
        StringBuilder result = new StringBuilder();
        result.append("=== LIMPIEZA FORZADA DE CONEXIONES ===\n");
        result.append("Fecha y hora: ").append(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append("\n\n");
        
        // Forzar limpieza de conexiones de base de datos
        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                HikariPoolMXBean poolMXBean = hikariDataSource.getHikariPoolMXBean();
                
                int beforeActive = poolMXBean.getActiveConnections();
                int beforeIdle = poolMXBean.getIdleConnections();
                int beforeTotal = poolMXBean.getTotalConnections();
                
                result.append("ESTADO INICIAL DEL POOL:\n");
                result.append("- Conexiones activas: ").append(beforeActive).append("\n");
                result.append("- Conexiones inactivas: ").append(beforeIdle).append("\n");
                result.append("- Conexiones totales: ").append(beforeTotal).append("\n\n");
                
                // Suspender el pool para evitar nuevas conexiones
                poolMXBean.suspendPool();
                
                // Esperar un momento para que se completen las operaciones en curso
                Thread.sleep(1000);
                
                // Forzar cierre de todas las conexiones inactivas
                poolMXBean.softEvictConnections();
                
                // Esperar otro momento
                Thread.sleep(1000);
                
                // Reanudar el pool
                poolMXBean.resumePool();
                
                // Obtener estadísticas después de la limpieza
                int afterActive = poolMXBean.getActiveConnections();
                int afterIdle = poolMXBean.getIdleConnections();
                int afterTotal = poolMXBean.getTotalConnections();
                
                result.append("ESTADO FINAL DEL POOL:\n");
                result.append("- Conexiones activas: ").append(afterActive).append("\n");
                result.append("- Conexiones inactivas: ").append(afterIdle).append("\n");
                result.append("- Conexiones totales: ").append(afterTotal).append("\n");
                result.append("- Conexiones liberadas: ").append(beforeTotal - afterTotal).append("\n\n");
            } else {
                result.append("LIMPIEZA DE BASE DE DATOS: No se pudo realizar (no es HikariDataSource)\n\n");
            }
        } catch (Exception e) {
            result.append("ERROR al forzar limpieza de conexiones de base de datos: ").append(e.getMessage()).append("\n\n");
        }
        
        // Forzar limpieza de sesiones WebSocket
        try {
            int beforeConnectedUsers = userConnectionService.getConnectedUsers().size();
            
            // Limpiar sesiones huérfanas y inactivas
            userConnectionService.cleanupOrphanedSessions();
            userConnectionService.cleanupInactiveSessions();
            
            // Obtener estadísticas después de la limpieza
            int afterConnectedUsers = userConnectionService.getConnectedUsers().size();
            
            result.append("LIMPIEZA DE SESIONES WEBSOCKET:\n");
            result.append("- Usuarios conectados antes: ").append(beforeConnectedUsers).append("\n");
            result.append("- Usuarios conectados después: ").append(afterConnectedUsers).append("\n");
            result.append("- Usuarios desconectados: ").append(beforeConnectedUsers - afterConnectedUsers).append("\n\n");
        } catch (Exception e) {
            result.append("ERROR al forzar limpieza de sesiones WebSocket: ").append(e.getMessage()).append("\n\n");
        }
        
        result.append("LIMPIEZA FORZADA COMPLETADA\n");
        
        return result.toString();
    }

    /**
     * Obtiene el número total de conexiones de base de datos
     * @return Número total de conexiones
     */
    private int getTotalConnections() {
        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                return hikariDataSource.getHikariPoolMXBean().getTotalConnections();
            }
        } catch (Exception e) {
            log.error("Error al obtener el número total de conexiones: {}", e.getMessage(), e);
        }
        return 0;
    }

    /**
     * Formatea un tamaño en bytes a una representación legible
     * @param bytes Tamaño en bytes
     * @return Representación legible del tamaño
     */
    private String formatSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }
}
