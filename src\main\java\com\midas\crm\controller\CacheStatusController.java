package com.midas.crm.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Controlador público para verificar el estado básico del cache
 * No requiere autenticación para facilitar el monitoreo
 */
@RestController
@RequestMapping("/api/public/cache")
@RequiredArgsConstructor
@Slf4j
public class CacheStatusController {

    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * Endpoint público para verificar el estado básico del cache
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            // Información básica del CacheManager
            status.put("cacheManagerType", cacheManager.getClass().getSimpleName());
            status.put("cacheNames", cacheManager.getCacheNames());
            status.put("timestamp", new Date());
            
            // Verificar conexión a Redis
            try {
                Set<String> allKeys = redisTemplate.keys("*");
                status.put("redisConnected", true);
                status.put("totalRedisKeys", allKeys != null ? allKeys.size() : 0);

                // Contar claves de la aplicación
                if (allKeys != null) {
                    long appKeys = allKeys.stream()
                            .filter(key -> key.contains("midas") || key.contains("anuncio") ||
                                         key.contains("notification") || key.contains("cliente") ||
                                         key.contains("manual") || key.contains("curso") ||
                                         key.contains("asesor"))
                            .count();
                    status.put("appCacheKeys", appKeys);

                    // Mostrar algunas claves de ejemplo
                    List<String> sampleKeys = allKeys.stream()
                            .filter(key -> key.contains("midas") || key.contains("anuncio"))
                            .limit(5)
                            .sorted()
                            .toList();
                    status.put("sampleKeys", sampleKeys);
                }
                
                status.put("status", "OK");
                
            } catch (Exception redisError) {
                status.put("redisConnected", false);
                status.put("redisError", redisError.getMessage());
                status.put("status", "REDIS_ERROR");
            }
            
        } catch (Exception e) {
            status.put("status", "ERROR");
            status.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(status);
    }

    /**
     * Endpoint para obtener estadísticas básicas del cache
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // Estadísticas básicas
            stats.put("cacheManagerType", cacheManager.getClass().getSimpleName());
            stats.put("availableCaches", cacheManager.getCacheNames());
            
            // Estadísticas de Redis si está disponible
            try {
                Set<String> allKeys = redisTemplate.keys("*");

                if (allKeys != null) {
                    // Contar por tipo de cache
                    Map<String, Integer> keysByType = new HashMap<>();
                    for (String key : allKeys) {
                        String type = extractCacheType(key);
                        keysByType.put(type, keysByType.getOrDefault(type, 0) + 1);
                    }

                    stats.put("totalKeys", allKeys.size());
                    stats.put("keysByType", keysByType);
                    stats.put("redisAvailable", true);
                } else {
                    stats.put("totalKeys", 0);
                    stats.put("redisAvailable", true);
                }
                
            } catch (Exception e) {
                stats.put("redisAvailable", false);
                stats.put("redisError", e.getMessage());
            }
            
            stats.put("timestamp", new Date());
            
        } catch (Exception e) {
            stats.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(stats);
    }

    /**
     * Endpoint simple para verificar si el cache está funcionando
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getCacheHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // Verificar CacheManager
            boolean cacheManagerOk = cacheManager != null && !cacheManager.getCacheNames().isEmpty();
            health.put("cacheManager", cacheManagerOk ? "UP" : "DOWN");
            
            // Verificar Redis
            try {
                redisTemplate.hasKey("health-check");
                health.put("redis", "UP");
            } catch (Exception e) {
                health.put("redis", "DOWN");
                health.put("redisError", e.getMessage());
            }
            
            // Estado general
            boolean isHealthy = cacheManagerOk;
            health.put("status", isHealthy ? "UP" : "DOWN");
            health.put("timestamp", new Date());
            
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(health);
    }

    /**
     * Extrae el tipo de cache de una clave Redis
     */
    private String extractCacheType(String key) {
        if (key.contains("anuncio")) return "anuncio";
        if (key.contains("notification")) return "notification";
        if (key.contains("cliente")) return "cliente";
        if (key.contains("manual")) return "manual";
        if (key.contains("curso")) return "curso";
        if (key.contains("asesor")) return "asesor";
        if (key.contains("midas")) return "midas";
        return "other";
    }

}
