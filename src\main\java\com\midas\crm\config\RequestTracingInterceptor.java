package com.midas.crm.config;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * Interceptor para rastrear peticiones HTTP, detectar peticiones largas y exponer métricas de monitoreo.
 * Agrega un header X-Request-ID y registra métricas para peticiones activas y de larga duración.
 */
@Component
@Slf4j
public class RequestTracingInterceptor implements HandlerInterceptor {

    private static final String REQUEST_ID_HEADER = "X-Request-ID";
    private static final String REQUEST_START_TIME_ATTR = "requestStartTime";
    private static final String REQUEST_ID_ATTR = "requestId";

    // Umbral para considerar una petición como "larga" (30 segundos)
    private static final long LONG_REQUEST_THRESHOLD_MS = 30_000;

    // Mapa para rastrear peticiones activas
    private final ConcurrentMap<String, RequestInfo> activeRequests = new ConcurrentHashMap<>();

    /**
     * Constructor que inyecta el registro de métricas de Micrometer y define los Gauges personalizados.
     * @param meterRegistry El registro central de métricas de la aplicación.
     */
    public RequestTracingInterceptor(MeterRegistry meterRegistry) {
        // Crear una métrica de tipo "Gauge" para las peticiones activas.
        // El valor de esta métrica será el resultado de llamar al método activeRequests::size en tiempo real.
        Gauge.builder("http.requests.active", activeRequests, ConcurrentMap::size)
                .description("Número de peticiones HTTP activas actualmente en el servidor")
                .register(meterRegistry);

        // Crear una métrica para las peticiones que han superado el umbral de tiempo.
        // El valor se calcula llamando a nuestro método getLongRunningRequests().size().
        Gauge.builder("http.requests.long.running", this, me -> me.getLongRunningRequests().size())
                .description("Número de peticiones que actualmente han excedido el umbral de tiempo largo")
                .register(meterRegistry);

    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        long startTime = System.currentTimeMillis();

        // Generar o usar X-Request-ID existente para un mejor seguimiento entre sistemas.
        String requestId = request.getHeader(REQUEST_ID_HEADER);
        if (requestId == null || requestId.trim().isEmpty()) {
            requestId = UUID.randomUUID().toString();
        }

        // Agregar el ID de la petición como un header en la respuesta.
        response.setHeader(REQUEST_ID_HEADER, requestId);

        // Guardar información en los atributos del request para usarla en afterCompletion.
        request.setAttribute(REQUEST_START_TIME_ATTR, startTime);
        request.setAttribute(REQUEST_ID_ATTR, requestId);

        // Registrar la petición como activa.
        RequestInfo requestInfo = new RequestInfo(
                requestId,
                request.getMethod(),
                request.getRequestURI(),
                startTime,
                request.getRemoteAddr()
        );
        activeRequests.put(requestId, requestInfo);

        // Log específico para peticiones POST, que suelen ser más críticas.
        if ("POST".equalsIgnoreCase(request.getMethod())) {
            log.info("POST Request started - ID: {}, URI: {}, IP: {}",
                    requestId, request.getRequestURI(), request.getRemoteAddr());
        }

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        Long startTime = (Long) request.getAttribute(REQUEST_START_TIME_ATTR);
        String requestId = (String) request.getAttribute(REQUEST_ID_ATTR);

        if (startTime != null && requestId != null) {
            long duration = System.currentTimeMillis() - startTime;

            // La petición ha finalizado, la removemos de la lista de activas.
            activeRequests.remove(requestId);

            // Log para peticiones que tomaron más tiempo del umbral definido.
            if (duration > LONG_REQUEST_THRESHOLD_MS) {
                log.warn("LONG REQUEST DETECTED - ID: {}, Method: {}, URI: {}, Duration: {}ms, Status: {}",
                        requestId, request.getMethod(), request.getRequestURI(), duration, response.getStatus());
            }

            if ("POST".equalsIgnoreCase(request.getMethod())) {
                log.info("POST Request completed - ID: {}, Duration: {}ms, Status: {}",
                        requestId, duration, response.getStatus());
            }

            // Si hubo una excepción no controlada, la registramos en los logs.
            if (ex != null) {
                log.error("Request failed with exception - ID: {}, Method: {}, URI: {}, Duration: {}ms, Error: {}",
                        requestId, request.getMethod(), request.getRequestURI(), duration, ex.getMessage());
            }
        }
    }

    /**
     * Obtiene una copia de la información de las peticiones activas actualmente (para monitoreo).
     * @return Un mapa con la información de las peticiones activas.
     */
    public Map<String, RequestInfo> getActiveRequests() {
        return new ConcurrentHashMap<>(activeRequests);
    }

    /**
     * Obtiene una lista de las peticiones que llevan más tiempo del umbral definido.
     * @return Un mapa con la información de las peticiones de larga duración.
     */
    public Map<String, RequestInfo> getLongRunningRequests() {
        long currentTime = System.currentTimeMillis();
        return activeRequests.values().stream()
                .filter(info -> (currentTime - info.getStartTime()) > LONG_REQUEST_THRESHOLD_MS)
                .collect(Collectors.toMap(RequestInfo::getRequestId, info -> info));
    }

    /**
     * Clase interna para almacenar información relevante de una petición HTTP activa.
     */
    public static class RequestInfo {
        private final String requestId;
        private final String method;
        private final String uri;
        private final long startTime;
        private final String clientIp;

        public RequestInfo(String requestId, String method, String uri, long startTime, String clientIp) {
            this.requestId = requestId;
            this.method = method;
            this.uri = uri;
            this.startTime = startTime;
            this.clientIp = clientIp;
        }

        public String getRequestId() { return requestId; }
        public String getMethod() { return method; }
        public String getUri() { return uri; }
        public long getStartTime() { return startTime; }
        public String getClientIp() { return clientIp; }

        public long getDuration() {
            return System.currentTimeMillis() - startTime;
        }

        @Override
        public String toString() {
            return String.format("RequestInfo{id='%s', method='%s', uri='%s', duration=%dms, ip='%s'}",
                    requestId, method, uri, getDuration(), clientIp);
        }
    }
}