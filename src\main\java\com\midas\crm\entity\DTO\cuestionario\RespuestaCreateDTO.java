package com.midas.crm.entity.DTO.cuestionario;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RespuestaCreateDTO {
    @NotBlank(message = "El texto de la respuesta es obligatorio")
    private String texto;
    
    @NotNull(message = "Debe indicar si la respuesta es correcta")
    private Boolean esCorrecta = false;
    
    @NotNull(message = "El orden de la respuesta es obligatorio")
    private Integer orden;
    
    private Long preguntaId;
}
