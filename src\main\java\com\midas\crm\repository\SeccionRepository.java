package com.midas.crm.repository;

import com.midas.crm.entity.Modulo;
import com.midas.crm.entity.Seccion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SeccionRepository extends JpaRepository<Seccion, Long> {
    List<Seccion> findByModuloIdOrderByOrdenAsc(Long moduloId);
    List<Seccion> findByModuloAndEstadoOrderByOrdenAsc(Modulo modulo, String estado);
    Optional<Seccion> findByTituloAndModuloId(String titulo, Long moduloId);
    boolean existsByTituloAndModuloId(String titulo, Long moduloId);
}
