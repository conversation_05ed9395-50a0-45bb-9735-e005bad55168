package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.AudioSinLead;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.repository.AudioSinLeadRepository;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.service.AudioSinLeadService;
import com.midas.crm.utils.AgentNormalizationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Implementación del servicio para manejar audios sin leads correspondientes
 */
@Service
@Slf4j
@Transactional
public class AudioSinLeadServiceImpl implements AudioSinLeadService {

    private final AudioSinLeadRepository audioSinLeadRepository;
    private final ClienteResidencialRepository clienteResidencialRepository;

    public AudioSinLeadServiceImpl(AudioSinLeadRepository audioSinLeadRepository,
                                   ClienteResidencialRepository clienteResidencialRepository) {
        this.audioSinLeadRepository = audioSinLeadRepository;
        this.clienteResidencialRepository = clienteResidencialRepository;
    }

    @Override
    public AudioSinLead registrarAudioSinLead(Map<String, Object> audioInfo, String motivoSinLead) {
        try {
            // Extraer información del audio
            Map<String, String> infoExtraida = procesarInformacionAudio(audioInfo);

            String fileName = (String) audioInfo.get("name");
            String audioUrl = (String) audioInfo.get("webContentLink");
            String googleDriveFileId = (String) audioInfo.get("id");

            // Verificar si ya existe por nombre de archivo
            if (existsByNombreArchivo(fileName)) {
                return findByNombreArchivo(fileName).orElse(null);
            }

            // Verificar si ya existe por Google Drive File ID
            if (googleDriveFileId != null && existsByGoogleDriveFileId(googleDriveFileId)) {
                return audioSinLeadRepository.findByGoogleDriveFileId(googleDriveFileId).orElse(null);
            }

            // Crear nuevo registro
            AudioSinLead audioSinLead = AudioSinLead.builder()
                    .nombreArchivo(fileName)
                    .urlGoogleDrive(audioUrl)
                    .googleDriveFileId(googleDriveFileId)
                    .movilExtraido(infoExtraida.get("movil"))
                    .agenteExtraido(infoExtraida.get("agente"))
                    .fechaExtraida(parseFechaExtraida(infoExtraida.get("fecha")))
                    .fechaEncontrado(LocalDateTime.now())
                    .fechaProcesamiento(LocalDateTime.now())
                    .estado("ENCONTRADO")
                    .tamanoArchivo(getLongValue(audioInfo.get("size")))
                    .tipoMime((String) audioInfo.get("mimeType"))
                    .carpetaOrigen(extractCarpetaOrigen(audioInfo))
                    .busquedaLeadRealizada(true)
                    .criteriosBusqueda(generarCriteriosBusqueda(infoExtraida))
                    .motivoSinLead(motivoSinLead)
                    .registradoPor("SISTEMA_TRANSCRIPCION")
                    .prioridad("MEDIA")
                    .requiereProcesamiento(true)
                    .build();

            AudioSinLead saved = audioSinLeadRepository.save(audioSinLead);

            return saved;

        } catch (Exception e) {
            throw new RuntimeException("Error al registrar audio sin lead", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AudioSinLead> findById(Long id) {
        return audioSinLeadRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AudioSinLead> findByNombreArchivo(String nombreArchivo) {
        return audioSinLeadRepository.findByNombreArchivo(nombreArchivo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findByMovilExtraido(String movilExtraido) {
        return audioSinLeadRepository.findByMovilExtraido(movilExtraido);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findByAgenteExtraido(String agenteExtraido) {
        return audioSinLeadRepository.findByAgenteExtraido(agenteExtraido);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findByEstado(String estado) {
        return audioSinLeadRepository.findByEstado(estado);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findByFechaProcesamientoBetween(LocalDateTime inicio, LocalDateTime fin) {
        return audioSinLeadRepository.findByFechaProcesamientoBetween(inicio, fin);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AudioSinLead> findByFechaProcesamientoBetween(LocalDateTime inicio, LocalDateTime fin, Pageable pageable) {
        return audioSinLeadRepository.findByFechaProcesamientoBetween(inicio, fin, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findAudiosRequierenProcesamiento() {
        return audioSinLeadRepository.findByRequiereProcesamientoTrue();
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findByPrioridad(String prioridad) {
        return audioSinLeadRepository.findByPrioridad(prioridad);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findByMovilYAgente(String movilExtraido, String agenteExtraido) {
        return audioSinLeadRepository.findByMovilExtraidoAndAgenteExtraido(movilExtraido, agenteExtraido);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByNombreArchivo(String nombreArchivo) {
        return audioSinLeadRepository.existsByNombreArchivo(nombreArchivo);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByGoogleDriveFileId(String googleDriveFileId) {
        return audioSinLeadRepository.existsByGoogleDriveFileId(googleDriveFileId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countByEstado(String estado) {
        return audioSinLeadRepository.countByEstado(estado);
    }

    @Override
    @Transactional(readOnly = true)
    public long countAudiosRequierenProcesamiento() {
        return audioSinLeadRepository.countByRequiereProcesamientoTrue();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getEstadisticasPorFecha(LocalDateTime inicio, LocalDateTime fin) {
        List<Object[]> results = audioSinLeadRepository.getEstadisticasPorFecha(inicio, fin);
        List<Map<String, Object>> estadisticas = new ArrayList<>();

        for (Object[] result : results) {
            Map<String, Object> stat = new HashMap<>();
            stat.put("fecha", result[0]);
            stat.put("total", result[1]);
            stat.put("encontrados", result[2]);
            stat.put("procesados", result[3]);
            stat.put("ignorados", result[4]);
            estadisticas.add(stat);
        }

        return estadisticas;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getEstadisticasPorAgente(LocalDateTime inicio, LocalDateTime fin) {
        List<Object[]> results = audioSinLeadRepository.getEstadisticasPorAgente(inicio, fin);
        List<Map<String, Object>> estadisticas = new ArrayList<>();

        for (Object[] result : results) {
            Map<String, Object> stat = new HashMap<>();
            stat.put("agente", result[0]);
            stat.put("total", result[1]);
            stat.put("encontrados", result[2]);
            stat.put("procesados", result[3]);
            estadisticas.add(stat);
        }

        return estadisticas;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findDuplicados(String movil, String agente, LocalDateTime fecha) {
        return audioSinLeadRepository.findDuplicados(movil, agente, fecha);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findAudiosRecientes() {
        LocalDateTime hace24Horas = LocalDateTime.now().minusHours(24);
        return audioSinLeadRepository.findRecientes(hace24Horas);
    }

    // **FIXED**: This method now passes the filter to the repository
    @Override
    @Transactional(readOnly = true)
    public Page<AudioSinLead> findRecientesYRequierenProcesamiento(String prioridad, Pageable pageable) {
        // Removed the time constraint
        return audioSinLeadRepository.findRecientesYRequieren(prioridad, pageable);
    }

    // **FIXED**: This method now passes the filter to the repository
    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findRecientesYRequierenProcesamiento(String prioridad) {
        // Removed the time constraint
        List<AudioSinLead> lista = audioSinLeadRepository.findRecientesYRequieren(prioridad);
        Map<Long, AudioSinLead> mapa = new LinkedHashMap<>();
        lista.forEach(a -> mapa.put(a.getId(), a));
        return new ArrayList<>(mapa.values());
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findByCarpetaOrigen(String carpetaOrigen) {
        return audioSinLeadRepository.findByCarpetaOrigen(carpetaOrigen);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findConInformacionValida() {
        return audioSinLeadRepository.findConInformacionValida();
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findConInformacionIncompleta() {
        return audioSinLeadRepository.findConInformacionIncompleta();
    }

    @Override
    public AudioSinLead updateEstado(Long id, String nuevoEstado, String observaciones) {
        Optional<AudioSinLead> audioOpt = audioSinLeadRepository.findById(id);
        if (audioOpt.isPresent()) {
            AudioSinLead audio = audioOpt.get();
            audio.setEstado(nuevoEstado);
            audio.setObservaciones(observaciones);
            audio.setFechaActualizacion(LocalDateTime.now());
            return audioSinLeadRepository.save(audio);
        }
        throw new RuntimeException("Audio sin lead no encontrado con ID: " + id);
    }

    @Override
    public void updateEstadoMultiple(List<Long> ids, String nuevoEstado) {
        audioSinLeadRepository.updateEstadoMultiple(ids, nuevoEstado, LocalDateTime.now());
    }

    @Override
    public AudioSinLead marcarComoProcesado(Long id, String observaciones) {
        return updateEstado(id, "PROCESADO", observaciones);
    }

    @Override
    public AudioSinLead marcarComoIgnorado(Long id, String motivo) {
        return updateEstado(id, "IGNORADO", motivo);
    }

    @Override
    public void deleteById(Long id) {
        audioSinLeadRepository.deleteById(id);
    }

    @Override
    public void deleteAudiosAntiguos(int diasAntiguedad) {
        LocalDateTime fechaLimite = LocalDateTime.now().minusDays(diasAntiguedad);
        audioSinLeadRepository.deleteAntiguos(fechaLimite);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AudioSinLead> findByNombreArchivoContaining(String patron) {
        return audioSinLeadRepository.findByNombreArchivoContaining(patron);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getResumenAudiosSinLead() {
        Map<String, Object> resumen = new HashMap<>();
        resumen.put("total", audioSinLeadRepository.count());
        resumen.put("encontrados", countByEstado("ENCONTRADO"));
        resumen.put("procesados", countByEstado("PROCESADO"));
        resumen.put("ignorados", countByEstado("IGNORADO"));
        resumen.put("requierenProcesamiento", countAudiosRequierenProcesamiento());
        resumen.put("recientes24h", findAudiosRecientes().size());
        return resumen;
    }

    @Override
    public Map<String, String> procesarInformacionAudio(Map<String, Object> audioFile) {
        Map<String, String> info = new HashMap<>();
        String fileName = (String) audioFile.get("name");

        if (fileName != null) {
            String[] parts = fileName.split("_");

            if (parts.length >= 3) {
                String datePart = parts[0];
                if (datePart.contains("-")) {
                    String dateOnly = datePart.split("-")[0];
                    if (dateOnly.length() == 8) {
                        String year = dateOnly.substring(0, 4);
                        String month = dateOnly.substring(4, 6);
                        String day = dateOnly.substring(6, 8);
                        info.put("fecha", year + "-" + month + "-" + day);
                    }
                }

                String movilPart = parts[1];
                if (movilPart.matches("\\d+")) {
                    info.put("movil", movilPart);
                }

                for (String part : parts) {
                    if (part.toLowerCase().contains("agent")) {
                        String agenteNumero = part.toLowerCase().replaceAll("\\s+", "");
                        agenteNumero = agenteNumero.replaceFirst("^(agente|agent|agen|te)([:\\-_\\s])?", "");
                        agenteNumero = agenteNumero.replaceAll("[^0-9]", "");
                        agenteNumero = agenteNumero.replaceFirst("^0+", "");

                        if (agenteNumero.isEmpty()) {
                            agenteNumero = "0";
                        }

                        if (!agenteNumero.isEmpty()) {
                            info.put("agente", agenteNumero);
                        }
                        break;
                    }
                }
            }
        }

        return info;
    }

    @Override
    public boolean isInformacionSuficiente(Map<String, String> infoExtraida) {
        return infoExtraida.get("movil") != null &&
                infoExtraida.get("agente") != null &&
                !infoExtraida.get("movil").trim().isEmpty() &&
                !infoExtraida.get("agente").trim().isEmpty();
    }

    private LocalDateTime parseFechaExtraida(String fechaStr) {
        try {
            if (fechaStr != null && fechaStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                return LocalDateTime.parse(fechaStr + "T00:00:00");
            }
        } catch (Exception e) {
            // ignore
        }
        return null;
    }

    private Long getLongValue(Object value) {
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }

    private String extractCarpetaOrigen(Map<String, Object> audioInfo) {
        Object parents = audioInfo.get("parents");
        if (parents instanceof List && !((List<?>) parents).isEmpty()) {
            return ((List<?>) parents).get(0).toString();
        }
        return null;
    }

    private String generarCriteriosBusqueda(Map<String, String> infoExtraida) {
        return String.format("movil=%s, agente=%s, fecha=%s",
                infoExtraida.get("movil"),
                infoExtraida.get("agente"),
                infoExtraida.get("fecha"));
    }

    @Override
    public Page<AudioSinLead> findWithFilters(String estado, String prioridad, String movil, String agente,
                                              String carpetaOrigen, Boolean requiereProcesamiento,
                                              Boolean busquedaLeadRealizada, LocalDateTime fechaInicio,
                                              LocalDateTime fechaFin, String fechaInicioStr,
                                              String fechaFinStr, Pageable pageable) {
        return audioSinLeadRepository.findWithFilters(estado, prioridad, movil, agente, carpetaOrigen,
                requiereProcesamiento, busquedaLeadRealizada,
                fechaInicio, fechaFin, fechaInicioStr, fechaFinStr, pageable);
    }

    @Override
    public List<AudioSinLead> findWithFilters(String estado, String prioridad, String movil, String agente,
                                              String carpetaOrigen, Boolean requiereProcesamiento,
                                              Boolean busquedaLeadRealizada, LocalDateTime fechaInicio,
                                              LocalDateTime fechaFin, String fechaInicioStr,
                                              String fechaFinStr) {
        return audioSinLeadRepository.findWithFilters(estado, prioridad, movil, agente, carpetaOrigen,
                requiereProcesamiento, busquedaLeadRealizada,
                fechaInicio, fechaFin, fechaInicioStr, fechaFinStr);
    }

    @Override
    @Transactional
    public Map<String, Object> buscarLeadsMasivo() {
        log.info("🔍 Iniciando búsqueda masiva de leads para audios sin lead");

        Map<String, Object> resultado = new HashMap<>();
        List<Map<String, Object>> leadsEncontrados = new ArrayList<>();
        List<Map<String, Object>> noEncontrados = new ArrayList<>();
        int totalProcesados = 0;

        try {
            // Obtener todos los audios sin lead que requieren procesamiento
            List<AudioSinLead> audiosParaProcesar = audioSinLeadRepository.findByRequiereProcesamientoTrue();
            log.info("📊 Total de audios para procesar: {}", audiosParaProcesar.size());

            for (AudioSinLead audio : audiosParaProcesar) {
                totalProcesados++;

                try {
                    // Extraer información del audio
                    String movil = audio.getMovilExtraido();
                    String agente = audio.getAgenteExtraido();
                    LocalDate fecha = extraerFechaDelNombreArchivo(audio.getNombreArchivo());

                    if (!StringUtils.hasText(movil) || !StringUtils.hasText(agente)) {
                        log.debug("⚠️ Audio {} sin información válida: móvil={}, agente={}",
                                audio.getNombreArchivo(), movil, agente);
                        agregarNoEncontrado(noEncontrados, audio, "Información incompleta (móvil o agente faltante)");
                        continue;
                    }

                    // Normalizar agente
                    String agenteNormalizado = AgentNormalizationUtils.normalizeAgentNumber(agente);

                    // Buscar lead correspondiente
                    ClienteResidencial leadEncontrado = buscarLeadExistente(movil, agenteNormalizado, fecha);

                    if (leadEncontrado != null) {
                        // Lead encontrado
                        log.debug("✅ Lead encontrado para audio {}: Lead ID {}",
                                audio.getNombreArchivo(), leadEncontrado.getId());

                        agregarLeadEncontrado(leadsEncontrados, audio, leadEncontrado, agenteNormalizado, fecha);

                        // Actualizar el audio como procesado
                        audio.setEstado("PROCESADO");
                        audio.setObservaciones("Lead encontrado - ID: " + leadEncontrado.getId());
                        audio.setFechaProcesamiento(LocalDateTime.now());
                        audio.setRequiereProcesamiento(false);
                        audioSinLeadRepository.save(audio);

                    } else {
                        // Lead no encontrado
                        log.debug("❌ No se encontró lead para audio {}: móvil={}, agente={}, fecha={}",
                                audio.getNombreArchivo(), movil, agenteNormalizado, fecha);

                        agregarNoEncontrado(noEncontrados, audio,
                                String.format("No existe lead con móvil %s, agente %s en fecha %s",
                                        movil, agenteNormalizado, fecha));
                    }

                } catch (Exception e) {
                    log.error("❌ Error procesando audio {}: {}", audio.getNombreArchivo(), e.getMessage());
                    agregarNoEncontrado(noEncontrados, audio, "Error en procesamiento: " + e.getMessage());
                }
            }

            // Preparar resultado
            resultado.put("totalProcesados", totalProcesados);
            resultado.put("leadsEncontrados", leadsEncontrados);
            resultado.put("noEncontrados", noEncontrados);
            resultado.put("timestamp", LocalDateTime.now().toString());

            log.info("✅ Búsqueda masiva completada: {} procesados, {} encontrados, {} no encontrados",
                    totalProcesados, leadsEncontrados.size(), noEncontrados.size());

            return resultado;

        } catch (Exception e) {
            log.error("❌ Error en búsqueda masiva de leads", e);
            throw new RuntimeException("Error en búsqueda masiva de leads: " + e.getMessage(), e);
        }
    }

    /**
     * Busca un lead existente usando múltiples estrategias
     */
    private ClienteResidencial buscarLeadExistente(String movil, String agenteNormalizado, LocalDate fecha) {
        try {
            if (fecha != null) {
                LocalDateTime startOfDay = fecha.atStartOfDay();
                LocalDateTime endOfDay = fecha.atTime(LocalTime.MAX);

                // Buscar por móvil, agente y fecha exacta
                List<ClienteResidencial> resultados = clienteResidencialRepository
                        .findLeadsByAudioPattern(movil, agenteNormalizado, startOfDay, endOfDay);

                if (!resultados.isEmpty()) {
                    return resultados.get(0);
                }
            }

            // Buscar solo por móvil (cualquier fecha/agente)
            List<ClienteResidencial> resultadosPorMovil = clienteResidencialRepository.findByMovilContacto(movil);
            if (!resultadosPorMovil.isEmpty()) {
                // Filtrar por agente normalizado si es posible
                for (ClienteResidencial lead : resultadosPorMovil) {
                    String agenteLeadNormalizado = AgentNormalizationUtils.normalizeAgentNumber(lead.getNumeroAgente());
                    if (agenteNormalizado.equals(agenteLeadNormalizado)) {
                        return lead;
                    }
                }
                // Si no hay coincidencia exacta de agente, retornar el primero
                return resultadosPorMovil.get(0);
            }

            return null;

        } catch (Exception e) {
            log.error("Error buscando lead para móvil {} y agente {}: {}", movil, agenteNormalizado, e.getMessage());
            return null;
        }
    }

    /**
     * Extrae la fecha del nombre del archivo (formato: YYYYMMDD-HHMMSS_...)
     */
    private LocalDate extraerFechaDelNombreArchivo(String nombreArchivo) {
        try {
            if (nombreArchivo != null && nombreArchivo.length() >= 8) {
                String fechaStr = nombreArchivo.substring(0, 8);
                if (fechaStr.matches("\\d{8}")) {
                    return LocalDate.parse(fechaStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
                }
            }
        } catch (Exception e) {
            log.debug("No se pudo extraer fecha del archivo: {}", nombreArchivo);
        }
        return null;
    }

    /**
     * Agrega un lead encontrado a la lista de resultados
     */
    private void agregarLeadEncontrado(List<Map<String, Object>> leadsEncontrados,
                                       AudioSinLead audio, ClienteResidencial lead,
                                       String agenteNormalizado, LocalDate fecha) {
        Map<String, Object> item = new HashMap<>();
        item.put("nombreArchivo", audio.getNombreArchivo());
        item.put("agente", agenteNormalizado);
        item.put("movil", audio.getMovilExtraido());
        item.put("fecha", fecha != null ? fecha.toString() : "N/A");
        item.put("leadId", lead.getId());
        item.put("nombreCliente", lead.getNombresApellidos() != null ? lead.getNombresApellidos() : "N/A");
        item.put("fechaCreacionLead", lead.getFechaCreacion().toString());
        leadsEncontrados.add(item);
    }

    /**
     * Agrega un audio no encontrado a la lista de resultados
     */
    private void agregarNoEncontrado(List<Map<String, Object>> noEncontrados,
                                     AudioSinLead audio, String motivo) {
        Map<String, Object> item = new HashMap<>();
        item.put("nombreArchivo", audio.getNombreArchivo());
        item.put("agente", audio.getAgenteExtraido());
        item.put("movil", audio.getMovilExtraido());
        item.put("fecha", audio.getFechaExtraida() != null ? audio.getFechaExtraida().toLocalDate().toString() : "N/A");
        item.put("motivo", motivo);
        noEncontrados.add(item);
    }
}