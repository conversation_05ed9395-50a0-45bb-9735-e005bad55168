package com.midas.crm.entity.DTO.cuestionario;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CuestionarioDTO {
    private Long id;
    private String titulo;
    private String descripcion;
    private Integer tiempoLimite;
    private Integer puntajeAprobacion;
    private Integer intentosMaximos;
    private Boolean mostrarRespuestas;
    private Boolean aleatorizarPreguntas;
    private Long leccionId;
    private String leccionTitulo;
    private String estado;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
    private List<PreguntaDTO> preguntas;
}
